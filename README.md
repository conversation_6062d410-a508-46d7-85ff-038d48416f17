# Setup del progetto
### 1. Installare la Salesforce CLI e le estensioni VSCODE
Installare la [Salesforce CLI](https://developer.salesforce.com/docs/atlas.en-us.246.0.sfdx_setup.meta/sfdx_setup/sfdx_setup_intro.htm), possibilmente attraverso `npm`: `npm install @salesforce/cli --global` .
In VSCode, installare le estensioni [Salesforce Extension Pack](https://marketplace.visualstudio.com/items?itemName=salesforce.salesforcedx-vscode) e [Lightning Web Components](https://marketplace.visualstudio.com/items?itemName=salesforce.salesforcedx-vscode-lwc).

> Nota: la prima richiede JDK 11 o 17 installato, e di impostare la user setting di VSCode `salesforcedx-vscode-apex.java.home` affinché punti alla location dove è installata la JDK.

### 2. Clonare la repository
Url repository: http://obiwan.replynet.prv/e.miani/salesforce-interprete-pu.git

### 3. Autenticarsi localmente alla sandbox
Eseguire il seguente comando:
```
sfdx force:auth:web:login -a <your-sandbox-alias> -r https://test.salesforce.com
```

sostituendo`<your-sandbox-alias>` con l'alias che si vuole dare alla connesione con la sandbox.
Si aprirà una pagina web dove loggarsi con Salesforce.

> Nota: `https://test.salesforce.com` è il parametro corretto, non è l'url della sandbox a cui ci sta connettendo, ma quello di login.

4. Settare l'username di default 
Step opzionale, ma utile per evitare di dover specificare l'username in tutti i comandi successivi.
Eseguire il seguente comando:
```
sfdx force:config:set defaultusername=<your-sandbox-alias>
```

5. Controllare che sia andato a buon fine eseguendo questo comando, che elencherà tutte le org 
 a cui sei autenticato:
```
sf org list
```


# Visualizzare la sandbox online
Collegarsi alla sandbox al link: https://unipol--testpega.sandbox.lightning.force.com/lightning/page/home e autenticarsi.

Conviene aprire Chrome senza CORS dal command prompt:
```bash
"C:\Program Files\Google\Chrome\Application\chrome.exe" --disable-web-security --user-data-dir=~/Temp
```

 per evitare errori di CORS quando ci interfacciamo con i servizi di PU.

# Deployare una change sulla sandbox
Ogni qual volta si voglia testare una change locale del codice all'interno della sandbox, sarà necessario fare un deploy. 

Per far ciò è utile avere le estensioni VSCode installate, così da poter fare click del tasto destro sopra alla cartella `force-app/main/default` all'interno dell'Explorer, e selezionare `SFDX: Deploy this source to org` tra le opzioni del menu a tendina.
Nell'Output tab del terminal integrato è possibile vedere il risultato del deployment.

Alternativamente il comando da terminale è:
```
sf project deploy start -d force-app
```

## Nota importante sugli API
Ogni qualvolta si vada ad interfacciarsi con degli API esterni a Salesforce, questi devono essere adeguatamente configurati e whitelistati, altrimenti Saleforce bloccherà la connessione.

Per fare ciò bisogna modificare/aggiungere alcuni file.

1. Content Security Policy (CSP) trusted sites
All'interno della cartella `force-app\main\default\cspTrustedSites\` sono presenti una serie di file, che vanno a specificare degli url come trusted per la Content Security Policy (CSP).

Questi file dovranno avere nome `<custom-name>.cspTrustedSite-meta.xml`, e questa struttura (sostituendo `https://URL-CUSTOM-DA-WHITELISTARE` e la `DESCRIZIONE`):
```xml
<?xml version="1.0" encoding="UTF-8"?>
<CspTrustedSite xmlns="http://soap.sforce.com/2006/04/metadata">
    <context>All</context>
    <description>DESCRIZIONE</description>
    <endpointUrl>https://URL-CUSTOM-DA-WHITELISTARE</endpointUrl>
    <isActive>true</isActive>
    <isApplicableToConnectSrc>true</isApplicableToConnectSrc>
    <isApplicableToFontSrc>true</isApplicableToFontSrc>
    <isApplicableToFrameSrc>true</isApplicableToFrameSrc>
    <isApplicableToImgSrc>true</isApplicableToImgSrc>
    <isApplicableToMediaSrc>true</isApplicableToMediaSrc>
    <isApplicableToStyleSrc>true</isApplicableToStyleSrc>
</CspTrustedSite>
```

2. Remote site setting
All'interno della cartella `force-app\main\default\remoteSiteSettings\` sono presenti una serie di file, che vanno a specificare i setting degli url precedenti.

Questi file dovranno avere nome `<custom-name>.remoteSite-meta.xml`, e questa struttura (sostituendo `https://URL-CUSTOM-DA-WHITELISTARE`):
```xml
<?xml version="1.0" encoding="UTF-8"?>
<RemoteSiteSetting xmlns="http://soap.sforce.com/2006/04/metadata">
    <disableProtocolSecurity>false</disableProtocolSecurity>
    <isActive>true</isActive>
    <url>https://URL-CUSTOM-DA-WHITELISTARE</url>
</RemoteSiteSetting>
```

3. File di progetto
Nella root del progetto, all'interno del file `sfdx-project.json`, sono ripetuti tutti i siti che sono whitelistati per la CSP, all'interno della property `contentSecurityPolicy` di `securitySettings`:

```json
{
	...,
	"securitySettings": {
		"enableCSPSettings": true,
		"cspSettings": {
		"contentSecurityPolicy": "connect-src 'self' https://evo-dev.unipolsai.it http://localhost:3000;"
		}
	}
}
```


## Nota importante sullo sviluppo
All'interno del progetto è presente una cartella `mocks` con dei json di esempio per alcuni servizi.
E' consigliabile, per alcune fasi dello sviluppo, utilizzare un tool di mock come [Mockoon](https://mockoon.com/) per semplificare e velocizzare l'interazione con le API, sfruttando questi file json.

E' possibile configurare se utilizzare un server mockato locale per le API, o l'ambiente rilasciato evo-dev, con un flag `_mockUrls` all'interno della classe `bffInterprete.js`. Sempre lì dentro è anche possibile configurare il base path locale con la costante `BASE_PATH_MOCK`.

