import { LightningElement, api, track } from 'lwc';
import { utils } from 'c/utils';


export default class AssurancePackage extends LightningElement {
    _field;
    _groups;

    @track checked = false;
    assurancePackageHeader;


    @api 
    get groups() {
        return this._groups;
    }
    set groups(value) {
        this._groups = value;
        this.initializeData();
    }

    @api
    get field() {
        return this._field;
    }
    set field(value) {
        this._field = value;
        this.initializeData();
    }

    // Getter per i dati estratti dai gruppi
    get checkbox() {
        return utils.getFirstFieldInGroupsByType(this._groups, 'pxCheckbox');
    }

    get packageName() {
        const caption = utils.getFirstCaptionByControlFormat(this._groups, 'PackageName');
        return utils.captionToValue(caption)?.value || '';       
    }

    get packageInstallmentsPeriod() {
        const caption = utils.getFirstCaptionByControlFormat(this._groups, 'PackageInstallmentsPeriod');
        return utils.captionToValue(caption)?.value || '';
    }
    
    get format() {
        if (this.field.labelFormat)
        return utils.getClassFromFormat(this.field.labelFormat);
    }        

    get packageDescription() {
        const caption = utils.getFirstCaptionByControlFormat(this._groups, 'PackageDescription');
        return utils.captionToValue(caption)?.value || '';
    }
    
    get labelAllAnno() {
        const caption = utils.getFirstCaptionByControlFormat(this._groups, 'TEXT APP WHL13 WEB WHL16 WHL13 WHL13');
        return utils.captionToValue(caption)?.value || '';
    }

    get PackageInstallmentsPrice() {
        const price = utils.getFirstCaptionByControlFormat(this._groups, 'PackageInstallmentsPrice');
        return utils.captionToValue(price)?.value || '';
    }

    get sconto() {
        const caption = utils.getFirstCaptionByControlFormat(this._groups, 'TEXT APP BDB14 WEB BDB18 BDB16 BDB14');
        return utils.captionToValue(caption)?.value || '';
    }

    get isScontoVisibile() {
        return !this.checked;
    }

    get percentualeSconto() {
        const result = utils.getFirstIntegerByFieldId(this._groups, 'PercentualeSconto');
        if (result) {
            return `${result.value}${result.symbol}`;
        }
    }


    get premioRataScontato() {
        const result = utils.getFirstCurrencyByFieldId(this._groups, 'RataPremioLordoScontato');
        if (result) {
            return `${result.value}${result.symbol}`;
        }
    }

    get premioLordo() {
        const result = utils.getFirstCurrencyByFieldId(this._groups, 'PremioLordo');
        if (result) {
            return `${result.value}${result.symbol}`;
        }
    }

    get premioLordoScontato() {
        const result = utils.getFirstCurrencyByFieldId(this._groups, 'PremioLordoScontato');
        if (result) {
            return `${result.value}${result.symbol}`;
        }
    }

    get rataSuccessivaPremioLordoScontato() {
        const result = utils.getFirstCurrencyByFieldId(this._groups, 'RataSuccessivaPremioLordoScontato');
        if (result) {
            return `${result.value}${result.symbol}`;
        }
    }




    initializeData() {
        if (this._field?.customAttributes) {
            this.assurancePackageHeader = this._field.customAttributes.assurancePackageHeader;
        }

        if (this.checkbox) {
            this.checked = this.checkbox.value === "true";
        }
    }

    handleCheckboxClick(event) {
        this.checked = !this.checked;
        if (!this.checked && this.checkbox?.control?.actionSets) {
            this.dispatchEvent(new CustomEvent('actionexecuted', {
                detail: {
                    actionSets: this.checkbox.control.actionSets,
                    eventType: 'click',
                    data: {
                        [this.checkbox.reference]: true
                    }
                },
                bubbles: true,
                composed: true
            }));
        }
    }

}