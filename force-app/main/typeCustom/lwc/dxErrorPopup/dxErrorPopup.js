import ICON_SVG from '@salesforce/resourceUrl/modalErrorIcon';
import { LightningElement, api } from 'lwc';

export default class DxErrorPopup extends LightningElement {
  @api errorMessage;
  @api moreData;
  modalErrorIcon = ICON_SVG;

  get isErroreGenrico() {
    return this.errorMessage.type === 'popUPErroreGenerico';
  }

  get isErroreAutoUsata() {
    return this.errorMessage.type === 'popUPErroreAutoUsata';
  }

  get isErroreTargaFittizia() {
    return this.errorMessage.type === 'popUPErroreTargaFittizia';
  }

  get isErroreAutoUsataInd() {
    return this.errorMessage.type === 'popUPErroreAutoUsataInd';
  }

  get popupErroreAutoUsataContenuto() {
    const targa = this.moreData.targa;
    const dataDiNascita = this.moreData.dataDiNascita;
    const contenuto = this.errorMessage.contenuto.split('|');

    return { targa, dataDiNascita, contenuto };
  }

  closePopup() {
    this.dispatchEvent(new CustomEvent('closepopup'));
  }

  loadNewPage() {
    this.dispatchEvent(
      new CustomEvent('loadpage', {
        bubbles: true,
        composed: true,
      })
    );
  }
}
