.pxIcon {
  background: no-repeat;
  background-size: cover;
}

.webResponsiveSize {
  width: 40px !important;
  height: 40px !important;
  margin: 0 auto 0 0 !important;
}

@media (max-width: 599px) {
  .webResponsiveSize {
    width: var(--mobile-size) !important;
    height: var(--mobile-size) !important;
    margin: var(--mobile-align) !important;
  }
}

@media (min-width: 600px) and (max-width: 1023px) {
  .webResponsiveSize {
    width: var(--tablet-size) !important;
    height: var(--tablet-size) !important;
    margin: var(--tablet-align) !important;
  }
}

@media (min-width: 1024px) {
  .webResponsiveSize {
    width: var(--desktop-size) !important;
    height: var(--desktop-size) !important;
    margin: var(--desktop-align) !important;
  }
}

.debug {
  border: 1px solid #e7a11f;
}

.temporaryLabel {
  color: #e7a11f;
  font-style: italic;
}

[class^="icon-"] {
  font-family: var(--font-family-icon) !important;
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-Freccia-sx::before {
  content: "\e964";
}

.icon-Chiudi::before {
  content: "\e933";
}

.icon-Matita {
  cursor: pointer;
  height: 28px;
  width: 28px;
  position: relative;
}

.icon-Matita::before {
  content: "";
  position: absolute;
  background-size: cover;
  width: 28px;
  height: 28px;
}

@media (max-width: 599px) {
  .icon-Matita,
  .icon-Matita::before {
    height: 22px;
    width: 22px;
  }
}

.icon-Cart {
  width: 33px;
  height: 30px;
}

.icon-Cart::before {
  cursor: pointer;
  content: "";
  position: absolute;
  background-size: cover;
  width: 30px;
  height: 30px;
}

.icon-Casa2 {
  color: var(--main_color, #000);
  font-size: 30px;
  width: 30px;
  height: 30px;
}

.icon-customer-service {
  color: var(--main_color, #000);
  font-size: 30px;
  width: 30px;
  height: 30px;
}

.icon-customer-service::before {
  cursor: pointer;
  content: "";
  position: absolute;
  background-size: cover;
  height: 30px;
  width: 30px;
}

.icon-Info2 {
  position: relative;
  max-width: 20px;
  max-height: 20px;
  min-width: 20px;
  min-height: 20px;
}

.icon-Info2::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  cursor: pointer;
  background-size: cover;
}

.tooltip-container {
  position: relative;
  width: 48px;
  height: 48px;
}

.Path {
  color: var(--main_color);
  font-size: 30px;
  width: 33px;
  height: 33px;
}

.Path::before {
  cursor: pointer;
  content: "";
  position: absolute;
  background-size: cover;
  height: 33px;
  width: 33px;
}

.icon-check-green {
  width: 20px;
  height: 20px;
}

.icon-check-green::before {
  cursor: pointer;
  content: "";
  position: absolute;
  background-size: cover;
  height: 20px;
  width: 20px;
}

.icon-success {
  width: 40px;
  height: 40px;
}

.icon-success::before {
  cursor: pointer;
  content: "";
  position: absolute;
  background-size: cover;
  height: 40px;
  width: 40px;
}

.icon-Pin {
  color: var(--main_color);
  font-size: 20px;
}

.checkSimple {
  width: 20px;
  height: 20px;
}

.checkSimple::before {
  width: 20px;
  height: 20px;
  color: var(--main_color);
  background-size: cover;
  content: "";
  position: absolute;
}

@media (max-width: 599px) {
  .visibility-src-mobile {
    display: block;
  }
  .visibility-src-tablet {
    display: none;
  }
  .visibility-src-desktop {
    display: none;
  }
}
@media (min-width: 600px) and (max-width: 1023px) {
  .visibility-src-mobile {
    display: none;
  }
  .visibility-src-tablet {
    display: block;
  }
  .visibility-src-desktop {
    display: none;
  }
}
@media (min-width: 1024px) {
  .visibility-src-mobile {
    display: none;
  }
  .visibility-src-tablet {
    display: none;
  }
  .visibility-src-desktop {
    display: block;
  }
}

.icon-Attenzione-pieno::before {
  content: "\e910";
}

.icon-warning {
  width: 40px;
  height: 40px;
}

.icon-warning::before {
  cursor: pointer;
  content: "";
  position: absolute;
  background-size: cover;
  height: 40px;
  width: 40px;
}

.icon-shield-outline {
  height: 33px;
  width: 33px;
}

.icon-shield-outline::before {
  cursor: pointer;
  content: "";
  position: absolute;
  background-size: cover;
  height: 40px;
  width: 40px;
}

.icon-Viaggi-VI {
  color: var(--main_color);
  font-size: 30px;
}

.icon-Viaggi-VI::before {
  content: "\e9c5";
}

.icon-Animali1 {
  color: var(--main_color);
  font-size: 30px;
}

.icon-Animali1::before {
 content: "\e9bd";
}

.icon-Piu {
  color: var(--main_color);
  font-size: 30px;
  cursor: pointer;
}

.icon-Piu::before {
  content: "\e972";
}

.icon-Meno {
  color: var(--main_color);
  font-size: 30px;
  cursor: pointer;
}

.icon-Meno::before {
  content: "\e96b";
}

.iconLabel-container {
  display: flex;
  align-items: center;
}

.boxed {
  border: 1px solid var(--main_color);
}

.icon-Unica {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
}

.icon-Unica::before {
  content: "";
  display: block;
  background-size: cover;
  height: 100%;
  width: 100%;
}

.icon-Unica-positive {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
}

.icon-Unica-positive::before {
  content: "";
  display: block;
  background-size: cover;
  height: 100%;
  width: 100%;
}

.icon-House-protection {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
}

.icon-House-protection::before {
  content: "";
  display: block;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  height: 100%;
  width: 100%;
}

.icon-Car-protection {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
}

.icon-Car-protection::before {
  content: "";
  display: block;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  height: 100%;
  width: 100%;
}

.icon-Pet-protection {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
}

.icon-Pet-protection::before {
  content: "";
  display: block;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  height: 100%;
  width: 100%;
}

.icon-trash:before {
  display: block;
  width: 100%;
  height: 100%;
  content: "\e997";
  color: var(--main_color);
  cursor: pointer;
  font-size: 25px;
}

.icon-Auto2 {
  display: block;
  width: 30px;
  height: 30px;
  background-size: cover;
}

.lockIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.autoRcaIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.assistenzaVeicoloIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.tutelaLegaleVeicoloIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.protezioneVeicoloIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.tecnologiaIconPu{
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.infortuniDelConducenteIconPu{
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.presentIconPu {
  display: block;
  width: 32px;
  height: 32px;
  background-size: cover;
}

.carPositiveIconPu {
  display: block;
  width: 40px;
  height: 40px;
  background-size: cover;
}

.infortuniPositiveIconPu {
  display: block;
  width: 40px;
  height: 40px;
  background-size: cover;
}

.housePositiveIconPu {
  display: block;
  width: 40px;
  height: 40px;
  background-size: cover;
}

.familyPositiveIconPu {
  display: block;
  width: 40px;
  height: 40px;
  background-size: cover;
}

.viaggiPositiveIconPu {
  display: block;
  width: 40px;
  height: 40px;
  background-size: cover;
}

.travelProtectionIconPu{
  display: block;
  width: 32px;
  height: 32px;
  background-size: cover;
}

.familyNegativeIconPu {
  display: block;
  width: 40px;
  height: 40px;
  background-size: cover;
}

.mobilityNegativeIconPu {
  display: block;
  width: 40px;
  height: 40px;
  background-size: cover;
}

.mobilityPositiveIconPu {
  display: block;
  width: 40px;
  height: 40px;
  background-size: cover;
}

.infortuniNegativeIconPu {
  display: block;
  width: 40px;
  height: 40px;
  background-size: cover;
}

.carNegativeIconPu {
  display: block;
  width: 40px;
  height: 40px;
  background-size: cover;
}

.houseNegativeIconPu {
  display: block;
  width: 40px;
  height: 40px;
  background-size: cover;
}

.lightInfoPu{
  display: block;
  width: 24px;
  height: 24px;
  background-size: cover;
  cursor: pointer;
}

.pencilNegativePu{
  display: block;
  width: 40px;
  height: 40px;
  background-size: cover;
  cursor: pointer;
}

.trashNegativePu{
  display: block;
  width: 40px;
  height: 40px;
  background-size: cover;
  cursor: pointer;
}

.successPu{
  display: block;
  width: 40px;
  height: 40px;
  background-size: cover;
}

.roundedCarProtectionPu {
  display: block;
  width: 32px;
  height: 32px;
  background-size: cover;
}

.roundedHouseProtectionPu {
  display: block;
  width: 32px;
  height: 32px;
  background-size: cover;
}

.roundedInjuriesProtectionPu {
  display: block;
  width: 32px;
  height: 32px;
  background-size: cover;
}

.roundedTravelProtectionPu {
  display: block;
  width: 32px;
  height: 32px;
  background-size: cover;
}

.roundedMobilityProtectionPu {
  display: block;
  width: 32px;
  height: 32px;
  background-size: cover;
}

.roundedDiseaseProtectionPu {
  display: block;
  width: 32px;
  height: 32px;
  background-size: cover;
}

.roundedFamilyProtectionPu {
  display: block;
  width: 32px;
  height: 32px;
  background-size: cover;
}

.roundedPetProtectionPu {
  display: block;
  width: 32px;
  height: 32px;
  background-size: cover;
}

.roundedCarProtectionPuNegative {
  display: block;
  width: 32px;
  height: 32px;
  background-size: cover;
}

.roundedHouseProtectionPuNegative {
  display: block;
  width: 32px;
  height: 32px;
  background-size: cover;
}

.roundedInjuriesProtectionPuNegative {
  display: block;
  width: 32px;
  height: 32px;
  background-size: cover;
}

.roundedTravelProtectionPuNegative {
  display: block;
  width: 32px;
  height: 32px;
  background-size: cover;
}

.roundedMobilityProtectionPuNegative {
  display: block;
  width: 32px;
  height: 32px;
  background-size: cover;
}

.roundedDiseaseProtectionPuNegative {
  display: block;
  width: 32px;
  height: 32px;
  background-size: cover;
}

.roundedFamilyProtectionPuNegative {
  display: block;
  width: 32px;
  height: 32px;
  background-size: cover;
}

.roundedPetProtectionPuNegative {
  display: block;
  width: 32px;
  height: 32px;
  background-size: cover;
}

.iconChiusuraPu {
  aspect-ratio: 1 / 1;
  display: block;
  position: relative;
  flex-shrink: 0;
  width: 32px;
  cursor: pointer;
}

@media (max-width: 599px) {
  .iconChiusuraPu {
    width: 24px;
  }
}

.iconChiusuraPu::after,
.iconChiusuraPu::before {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  width: 3px;
  height: 100%;
  background-color: var(--blue-primary, #193A56);
  border-radius: 24px;
}

.iconChiusuraPu::after {
  transform: translateX(-50%) translateY(-50%) rotateZ(-45deg);
}

.iconChiusuraPu::before {
  transform: translateX(-50%) translateY(-50%) rotateZ(45deg);
}

.sezioneProtezioneCasaIconPu{
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.sezioneFurtoIconPu{
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.sezioneTerremotoAlluvioneIconPu{
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.sezioneFotovoltaicoIconPu{
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.sezioneAssistenzaCasaIconPu{
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.frecciaDestraIconPu{
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.sezioneTutelaLegaleFamigliaIconPu{
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.sezioneDanniATerziIconPu{
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.sezioneAssistenzaFamigliaIconPu{
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.sezioneProtezioneViaggioIconPu{
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.sezioneAnnullamentoViaggioIconPu{
  display: block;
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml,%3Csvg%20width%3D%2270%22%20height%3D%2270%22%20viewBox%3D%220%200%2070%2070%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M35.25%204C18.02%204%204%2018.02%204%2035.25C4%2052.48%2018.02%2066.5%2035.25%2066.5C52.48%2066.5%2066.5%2052.48%2066.5%2035.25C66.5%2018.02%2052.48%204%2035.25%204ZM35.25%206.5C42.65%206.5%2049.41%209.31%2054.51%2013.92L41.37%2027.06L39.62%2026.48C39.64%2025.54%2039.65%2024.84%2039.65%2024.32V23.52C39.75%2020.48%2039.7%2013.17%2037.43%2010.83C36.88%2010.26%2036.19%209.96%2035.46%209.96C34.77%209.96%2034.12%2010.24%2033.59%2010.78C31.61%2012.78%2031.23%2018.8%2031.26%2023.49C31.26%2024.01%2031.29%2025.02%2031.34%2026.38L18.24%2030.69L16.7%2038.27L31.78%2036.64C31.78%2036.64%2031.78%2036.64%2031.78%2036.65L13.92%2054.51C9.31%2049.41%206.5%2042.65%206.5%2035.25C6.5%2019.4%2019.4%206.5%2035.25%206.5ZM35.55%2055.2L30.49%2055.69L30.76%2054.52L35.23%2052.64L35.16%2051.74C34.91%2048.41%2034.61%2043.36%2034.33%2037.62L36.85%2035.1V35.19C36.65%2039.99%2036.36%2045.57%2035.99%2051.79L35.94%2052.67L40.33%2054.52L40.6%2055.69L35.54%2055.2H35.55ZM34.17%2034.26L34.15%2033.86L19.83%2035.41L20.4%2032.6L33.91%2028.16L33.88%2027.22C33.81%2025.43%2033.77%2024.09%2033.77%2023.47C33.72%2015.61%2034.9%2012.65%2035.47%2012.45C35.51%2012.45%2035.57%2012.49%2035.64%2012.56C36.81%2013.76%2037.29%2018.94%2037.16%2023.44C37.16%2023.63%2037.16%2023.9%2037.15%2024.28C37.15%2024.95%2037.13%2025.94%2037.1%2027.34L37.08%2028.26L39.39%2029.03L34.17%2034.25V34.26ZM42.05%2029.92L50.1%2032.6L50.67%2035.4L37.99%2033.97L42.05%2029.91V29.92ZM35.25%2064C27.7%2064%2020.82%2061.07%2015.68%2056.29L31.95%2040.02C32.17%2044.36%2032.4%2048.21%2032.61%2051.04L28.63%2052.71L27.27%2058.52L33.41%2057.92C33.75%2059.31%2034.21%2060.07%2034.88%2060.39C35.09%2060.49%2035.32%2060.54%2035.55%2060.54C35.58%2060.54%2035.61%2060.54%2035.64%2060.54C35.98%2060.54%2036.32%2060.47%2036.63%2060.28C37.27%2059.89%2037.71%2059.14%2037.98%2057.94L43.85%2058.51L42.49%2052.71L38.56%2051.06C38.87%2045.75%2039.12%2040.91%2039.31%2036.64L53.82%2038.27L52.28%2030.69L44.04%2027.94L56.3%2015.68C61.08%2020.82%2064.01%2027.69%2064.01%2035.25C64.01%2051.1%2051.11%2064%2035.26%2064H35.25Z%22%20fill%3D%22%23193A56%22%2F%3E%3C%2Fsvg%3E') !important;
  background-size: cover;
}

.sezioneAssistenzaMobilitaIconPu{
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.sezioneInfortuniDaCircolazioneIconPu{
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.sezioneTutelaLegaleMobilitaIconPu{
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.sezioneZeroPensieriMobilitaIconPu{
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.sezioneInfortuniIconPu{
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.sezioneAssistenzaPerInfortunioIconPu{
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.mastercardIconPu{
  display: block;
  width: 28px;
  height: 18px;
  background-size: cover;
}

.visaIconPu{
  display: block;
  width: 28px;
  height: 18px;
  background-size: cover;
}

.applepayIconPu{
  display: block;
  width: 28px;
  height: 18px;
  background-size: cover;
}

.amazonpayIconPu{
  display: block;
  width: 28px;
  height: 18px;
  background-size: cover;
}

.satispayIconPu{
  display: block;
  width: 28px;
  height: 18px;
  background-size: cover;
}

.paypalIconPu{
  display: block;
  width: 28px;
  height: 18px;
  background-size: cover;
}

.petPositiveIconPu {
  display: block;
  width: 40px;
  height: 40px;
  background-size: cover;
}

.sezioneSpeseVeterinarieIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.sezioneAssitenzaCaneGattoIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.sezionePrevenzioneEAltaSpecializzazioneIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.sezioneInvaliditaDaMalattiaIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.sezioneRicoveroSaluteIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.sezioneAssistenzaSaluteIconPu {
  display: block;
  width: 20px;
  height: 20px;
  background-size: cover;
}

.diseasePositiveIconPu {
  display: block;
  width: 40px;
  height: 40px;
  background-size: cover;
}

.diseaseProtectionIconPu {
  display: block;
  width: 32px;
  height: 32px;
  background-size: cover;
}

.failurePu {
  display: block;
  background-size: cover;
}

.privacyPu {
  display: block;
  background-size: cover;
}