
.debug {
  border: 1px solid #e7a11f;
}

.temporaryLabel {
  color: #e7a11f;
  font-style: italic;
}

.visible-desktop {
  display: none;
}
@media (min-width: 1025px) {
  .visible-desktop {
    display: block;
  }
}
  
.visible-tablet {
    display: none;
}
@media (min-width: 768px) and (max-width: 1024px) {
  .visible-tablet {
    display: block;
  }
}

.visible-mobile {
    display: none;
}
@media (max-width: 767px) {
  .visible-mobile {
    display: block;
  }
}

.checkbox-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 16px;
}

/* Bordi del checkbox */
.checkbox-container.checkbox-with-border {
  padding: 8px 16px;
  border: solid 1px #ccc; /* Cambia con una variabile se necessario */
}

/* Posizionamento a destra */
.checkbox-container.position-left {
  flex-direction: row-reverse;
  justify-content: space-between;
  display: flex;
}

.disabled-checkmark {
  cursor: default !important;
  opacity: 0.8;
  pointer-events: none;
}

.checkmark-disabled {
  background-color: white;
  border-color: var(--check-disabled-color);
}

input[type="checkbox"]:checked + .checkmark-disabled {
  border-color: var(--check-green-disabled-color) !important;
  background-color: var(--check-green-disabled-color) !important;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

input[type="checkbox"]:checked ~ .checkmark:after {
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  display: block;
}

input[type="checkbox"]:checked ~ .checkmark {
  background-color: #8bc94d;
  border: none;
}

.checkmark-checked {
  background-color: #8bc94d;
  border: none;
}

/* input[type="checkbox"]:checked ~ .checkmark {
  background-color: #0f3250;
} */

input[type="checkbox"] ~ .checkmark {
  border-radius: 4px;
}
  
.round {
  border-radius: 32px !important;
}

.error-checkmark {
  border-color: var(--alert-color) !important;
}

.checkmark {
    display: block;
    position: relative;
    height: 24px;
    width: 24px;
    border: 2px solid #0f3250;
    overflow: hidden;
    background-color: #fff;
    cursor: pointer;
}

.checkmark {
  display: flex;
  justify-content: center;
  align-items: center;
}

.checkmark:after {
  aspect-ratio: 1 / 2;
  top: unset;
  left: unset;
  width: 6px;
  height: unset;
}

.us-checkbox-label {
  align-self: center;
  display: flex;
  font-size: 16px;
  color: #0f3250;
  float: left;
  font-family: var(--font-family-default);
  font-weight: 400;
  text-transform: none;
  margin-left: 10px;
}