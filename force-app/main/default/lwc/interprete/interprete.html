<template>
  <template if:false={showFailurePage}>
    <div>
      <div class="interprete">
        <c-view view={view} debug={debug}></c-view>

        <c-ui-modal is-open={showModal}>
          <div class="slds-modal__content slds-p-around_medium">
            <c-view view={modalView} debug={debug}>
            </c-view>
          </div>
        </c-ui-modal>
        <div if:true={isLoading} class="spinner">
          <lightning-spinner alternative-text="Loading" size="large"></lightning-spinner>
        </div>
      </div>
    </div>
  </template>

  <template if:true={showFailurePage}>
    <c-page-error request={request} error-message={errorMessage} reload={reloadVisible}></c-page-error>
  </template>
</template>