<template>
  <template if:true={isOpen}>
    <section class="slds-modal slds-fade-in-open slds-modal_large">
      <div class="slds-modal__container">
        <!--<button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" onclick={closeModal}>
          x-->
          <!-- <svg
          class="slds-button__icon slds-button__icon_large"
          aria-hidden="true"
        >
          <use
            xlink:href="/assets/icons/utility-sprite/svg/symbols.svg#close"
          ></use>
        </svg> -->
          <!--<span class="slds-assistive-text">Cancel and close</span>
        </button>
        -->
        <slot></slot>
      </div>
    </section>
    <div class="slds-backdrop slds-backdrop_open"></div>
    <!-- <lightning-modal-header label={header}></lightning-modal-header>
    <lightning-modal-body>
      <c-view view={modalView}>
      </c-view>
    </lightning-modal-body>
    <lightning-modal-footer>
      <lightning-button label="Close" onclick={closeModal}></lightning-button>
    </lightning-modal-footer> -->
  </template>
</template>