import { utils } from 'c/utils';
import { api, LightningElement, track } from 'lwc';

export default class DxAccordion extends LightningElement {
  static accordionOpenStatus = new Map();

  @api accordionType;
  @api titleId = '';
  @api customAttributes = {};
  @api groups = [];

  @track headerGroups = [];
  @track ribbonGroups = [];
  @track contentGroups = [];
  @track footerGroups = [];

  @track showContent = false;
  @track showAccordionArrow = true;
  @track showExplodingCTA = true;

  @track paddingDesktop = '';
  @track paddingTablet = '';
  @track paddingMobile = '';
  @track styleAccordion = '';

  @track thrashIconTelematica;

  connectedCallback() {
    this._initializeAccordion();
  }

  renderedCallback() {
    const openStatusDetector = this.template.querySelector('.AccordionOpenStatusDetector');
    if (openStatusDetector) {
      this.showContent = true;
    }
  }

  _initializeAccordion() {
    if (this.accordionType === '1') {
      const current = DxAccordion.accordionOpenStatus.get(this.titleId);
      this.showContent = current === undefined ? true : current;
      DxAccordion.accordionOpenStatus.set(this.titleId, this.showContent);
    }

    this._splitAccordionGroups(this.groups);
    const hiddenData = utils.getFirstFieldInGroupsByType(this.groups, 'pxHidden');
    this._applyCustomAttributes(hiddenData?.customAttributes);
  }

  _splitAccordionGroups(groups) {
    if (!Array.isArray(groups)) {
      console.warn('dxAccordion: groups is not an array:', groups);
      return;
    }

    if (this.accordionType === 'Telematica') {
      const iconCestinoTelematica = utils.getFirstIconInGroupsByResource(groups, 'trash');
      if (iconCestinoTelematica) this.thrashIconTelematica = iconCestinoTelematica;
    }

    const getFirstLayoutByFormat = (format) =>
      groups.find((g) => g?.layout?.groupFormat === format);
    const getFirstLayoutByContains = (substr) =>
      groups.find((g) => g?.layout?.groupFormat?.includes(substr));
    const getFieldsByType = (type) => {
      return groups
        .map((g) => g.fields || [])
        .reduce((acc, fields) => acc.concat(fields), [])
        .filter((f) => f.type === type);
    };

    const accordionHeader = getFirstLayoutByFormat('AccordionHeader');
    if (accordionHeader) {
      this.headerGroups = accordionHeader?.layout.groups;
      const ribbon = getFirstLayoutByContains('ribbon');
      if (ribbon) {
        this.ribbonGroups = ribbon?.layout.groups;
        this.headerGroups = this.headerGroups.filter(
          (g) => !g?.layout?.groupFormat?.toLowerCase()?.includes('ribbon')
        )[0]?.layout.groups;
      }
      const icon = getFieldsByType('pxIcon')[0];
      this.showAccordionArrow = icon?.customAttributes?.ShowAccordionArrow !== '1';
    }

    const accordionOpened = getFirstLayoutByFormat('AccordionOpened');
    this.contentGroups = accordionOpened?.layout.groups;
    if (!this.contentGroups?.length) this.showExplodingCTA = false;

    const accordionClosed = getFirstLayoutByFormat('AccordionClosed');
    if (accordionClosed) this.footerGroups = accordionClosed.layout.groups;
  }

  _applyCustomAttributes(attrs) {
    if (!attrs) return;
    this.styleAccordion = attrs.style;
    this.paddingDesktop = this._transformPadding(attrs.paddingDesktop);
    this.paddingTablet = this._transformPadding(attrs.paddingTablet);
    this.paddingMobile = this._transformPadding(attrs.paddingMobile);

    if (attrs.expandable === 'false') {
      this.showContent = true;
      this.showExplodingCTA = false;
    }

    if (attrs.openAccordion === 'true') {
      this.showContent = true;
    }
  }

  _transformPadding(padding) {
    return (
      padding?.replace(/Pega(\w+)/gi, (match, p1) => `var(--padding-${p1.toLowerCase()})`) || ''
    );
  }

  toggleVisibility(event) {
    const targetClassList = event.target.classList;

    if (targetClassList.contains('PenIcon')) {
        this.showContent = !this.showContent;
    }
}

  get paddingStyle() {
    let style = '';
    if (this.paddingDesktop) style += `--paddingDesktop: ${this.paddingDesktop};`;
    if (this.paddingTablet) style += `--paddingTablet: ${this.paddingTablet};`;
    if (this.paddingMobile) style += `--paddingMobile: ${this.paddingMobile};`;
    return style;
  }

  get paddingClass() {
    let classList = '';
    if (this.paddingDesktop || this.paddingTablet || this.paddingMobile) {
      classList += 'paddingContainer';
      if (this.paddingDesktop) classList += ' desktop';
      if (this.paddingTablet) classList += ' tablet';
      if (this.paddingMobile) classList += ' mobile';
    }
    return classList;
  }

  get showFooter() {
    return this.footerGroups && !this.showContent;
  }

  get accordionTypeClass() {
    return `Accordion-${this.accordionType}`;
  }

  get arrowIconClass() {
    let base = 'icon-Freccia-down size-l cursor-pointer';
    if (this.showContent) {
      base += ' freccia-su';
    }
    return base;
  }

  get isType4() {
    return this.accordionType === '4';
  }

  get isType7() {
    return this.accordionType === '7';
  }

  get isType8() {
    return this.accordionType === '8';
  }

  get isType12() {
    return this.accordionType === '12';
  }

  get isTelematica() {
    return this.accordionType === 'Telematica';
  }

  get isDefault() {
    return (
      this.accordionType != '4' &&
      this.accordionType != '7' &&
      this.accordionType != '8' &&
      this.accordionType != '12' &&
      this.accordionType != 'Telematica'
    );
  }
}
