import { utils } from 'c/utils';
import { api, LightningElement, track } from 'lwc';

export default class DxAccordion extends LightningElement {
  static accordionOpenStatus = new Map();

  @api accordionType;
  @api titleId = '';
  @api customAttributes = {};
  @api groups = [];

  @track headerGroups = [];
  @track ribbonGroups = [];
  @track contentGroups = [];
  @track footerGroups = [];

  @track showContent = false;
  @track showAccordionArrow = true;
  @track showExplodingCTA = true;

  @track paddingDesktop = '';
  @track paddingTablet = '';
  @track paddingMobile = '';
  @track styleAccordion = '';

  @track thrashIconTelematica;

  connectedCallback() {
    this._initializeAccordion();
  }

  renderedCallback() {
    const openStatusDetector = this.template.querySelector('.AccordionOpenStatusDetector');
    if (openStatusDetector) {
      this.showContent = true;
    }
  }

  _initializeAccordion() {
    if (this.accordionType === '1') {
      const current = DxAccordion.accordionOpenStatus.get(this.titleId);
      this.showContent = current === undefined ? true : current;
      DxAccordion.accordionOpenStatus.set(this.titleId, this.showContent);
    }

    this._splitAccordionGroups(this.groups);
    const hiddenData = utils.getFirstFieldInGroupsByType(this.groups, 'pxHidden');
    this._applyCustomAttributes(hiddenData?.customAttributes);
  }

  _splitAccordionGroups(groups) {
    if (!Array.isArray(groups)) {
      console.warn('dxAccordion: groups is not an array:', groups);
      return;
    }

    if (this.accordionType === 'Telematica') {
      const iconCestinoTelematica = utils.getFirstIconInGroupsByResource(groups, 'trash');
      if (iconCestinoTelematica) this.thrashIconTelematica = iconCestinoTelematica;
    }

    const getFirstLayoutByFormat = (format) =>
      groups.find((g) => g?.layout?.groupFormat === format);
    const getFirstLayoutByContains = (substr) =>
      groups.find((g) => g?.layout?.groupFormat?.includes(substr));
    const getFieldsByType = (type) => {
      return groups
        .map((g) => g.fields || [])
        .reduce((acc, fields) => acc.concat(fields), [])
        .filter((f) => f.type === type);
    };

    const accordionHeader = getFirstLayoutByFormat('AccordionHeader');
    if (accordionHeader) {
      this.headerGroups = accordionHeader?.layout.groups;
      const ribbon = getFirstLayoutByContains('ribbon');
      if (ribbon) {
        this.ribbonGroups = ribbon?.layout.groups;
        this.headerGroups = this.headerGroups.filter(
          (g) => !g?.layout?.groupFormat?.toLowerCase()?.includes('ribbon')
        )[0]?.layout.groups;
      }
      const icon = getFieldsByType('pxIcon')[0];
      this.showAccordionArrow = icon?.customAttributes?.ShowAccordionArrow !== '1';
    }

    const accordionOpened = getFirstLayoutByFormat('AccordionOpened');
    this.contentGroups = accordionOpened?.layout.groups;
    if (!this.contentGroups?.length) this.showExplodingCTA = false;

    const accordionClosed = getFirstLayoutByFormat('AccordionClosed');
    if (accordionClosed) this.footerGroups = accordionClosed.layout.groups;
  }

  _applyCustomAttributes(attrs) {
    if (!attrs) return;
    this.styleAccordion = attrs.style;
    this.paddingDesktop = this._transformPadding(attrs.paddingDesktop);
    this.paddingTablet = this._transformPadding(attrs.paddingTablet);
    this.paddingMobile = this._transformPadding(attrs.paddingMobile);

    if (attrs.expandable === 'false') {
      this.showContent = true;
      this.showExplodingCTA = false;
    }

    if (attrs.openAccordion === 'true') {
      this.showContent = true;
    }
  }

  _transformPadding(padding) {
    return (
      padding?.replace(/Pega(\w+)/gi, (match, p1) => `var(--padding-${p1.toLowerCase()})`) || ''
    );
  }

  handleArrowButtonClick(event) {
    console.log('=== handleArrowButtonClick called ===');
    console.log('Event target:', event?.target);
    console.log('Event target tagName:', event?.target?.tagName);
    console.log('Event target className:', event?.target?.className);

    // Ferma immediatamente la propagazione
    event.stopPropagation();
    event.preventDefault();

    // Controlla rigorosamente che il click sia SOLO su elementi del pulsante arrow
    const isValidArrowClick =
      event.target.classList?.contains('arrowButtonText') ||
      event.target.classList?.contains('icon-Freccia-down') ||
      event.target.classList?.contains('arrowButton');

    if (!isValidArrowClick) {
      console.log('Click ignorato: non è su un elemento valido del pulsante arrow');
      return;
    }

    console.log('Click valido sul pulsante arrow - toggling visibility');

    // Traccia il valore prima del cambiamento
    console.log('showContent PRIMA del toggle:', this.showContent);

    this.showContent = !this.showContent;

    // Traccia il valore dopo il cambiamento
    console.log('showContent DOPO il toggle:', this.showContent);

    if (this.titleId) {
      DxAccordion.accordionOpenStatus.set(this.titleId, this.showContent);
    }

    console.log('contentGroups:', this.contentGroups);
    console.log('contentGroups length:', this.contentGroups?.length);
    console.log('showExplodingCTA:', this.showExplodingCTA);

    // Forza un re-render usando una tecnica diversa
    setTimeout(() => {
      console.log('Timeout - showContent è ancora:', this.showContent);

      // Controlla se il contenuto è presente nel DOM
      const contentElements = this.template.querySelectorAll('c-groups');
      console.log('Elementi c-groups trovati nel DOM:', contentElements.length);

      // Controlla gli elementi di debug
      const debugVisible = this.template.querySelector('.debug-content-visible');
      const debugHidden = this.template.querySelector('.debug-content-hidden');
      console.log('Debug VISIBLE element:', debugVisible);
      console.log('Debug HIDDEN element:', debugHidden);

      const accordion12 = this.template.querySelector('.Accordion12');
      if (accordion12) {
        console.log('Accordion12 innerHTML:', accordion12.innerHTML);
      }

      // Forza il re-rendering modificando una proprietà tracked
      this.showAccordionArrow = this.showAccordionArrow;
    }, 100);

    console.log('=== Fine handleArrowButtonClick ===');
  }

  toggleVisibility(event) {
    // Manteniamo questo metodo per gli altri tipi di accordion
    console.log('=== toggleVisibility called (altri tipi) ===');
    console.log('Accordion Type:', this.accordionType);

    if (this.accordionType === '12') {
      console.log('Type 12 dovrebbe usare handleArrowButtonClick, non questo metodo');
      return;
    }

    this.showContent = !this.showContent;
    if (this.titleId) {
      DxAccordion.accordionOpenStatus.set(this.titleId, this.showContent);
    }
  }

  get paddingStyle() {
    let style = '';
    if (this.paddingDesktop) style += `--paddingDesktop: ${this.paddingDesktop};`;
    if (this.paddingTablet) style += `--paddingTablet: ${this.paddingTablet};`;
    if (this.paddingMobile) style += `--paddingMobile: ${this.paddingMobile};`;
    return style;
  }

  get paddingClass() {
    let classList = '';
    if (this.paddingDesktop || this.paddingTablet || this.paddingMobile) {
      classList += 'paddingContainer';
      if (this.paddingDesktop) classList += ' desktop';
      if (this.paddingTablet) classList += ' tablet';
      if (this.paddingMobile) classList += ' mobile';
    }
    return classList;
  }

  get showFooter() {
    return this.footerGroups && !this.showContent;
  }

  get accordionTypeClass() {
    return `Accordion-${this.accordionType}`;
  }

  get arrowIconClass() {
    let base = 'icon-Freccia-down size-l cursor-pointer';
    if (this.showContent) {
      base += ' freccia-su';
    }
    return base;
  }

  get isType4() {
    return this.accordionType === '4';
  }

  get isType7() {
    return this.accordionType === '7';
  }

  get isType8() {
    return this.accordionType === '8';
  }

  get isType12() {
    return this.accordionType === '12';
  }

  get isTelematica() {
    return this.accordionType === 'Telematica';
  }

  get isDefault() {
    return (
      this.accordionType != '4' &&
      this.accordionType != '7' &&
      this.accordionType != '8' &&
      this.accordionType != '12' &&
      this.accordionType != 'Telematica'
    );
  }
}
