<template>
  <div if:false={showInterprete}>
    <lightning-card title="Ambito di Bisogno">
      <div class="slds-var-m-around_medium">
        <lightning-combobox label="Seleziona Tipo di Prodotto" options={productTypeOptions}
          onchange={handleProductTypeChange}>
        </lightning-combobox>
      </div>

      <!-- CASA -->
      <lightning-card if:true={isCasa} title="Tipo di abitazione">
        <div class="slds-var-m-around_medium">
          <lightning-combobox name="tipologiaAppartamento" label="Seleziona Tipo di appartamento"
            options={tipoDiAbitazioniOptions} onchange={handleDisambiguationFieldChange}>
          </lightning-combobox>
          <lightning-card if:false={isEditable}>
            {addressDisplayer}
            <lightning-button name="confermaIndirizzo" onclick={handleDisambiguationFieldChange}
              label="Modifica indirizzo"></lightning-button>
          </lightning-card>
          <div if:true={isEditable}>
            <lightning-input name="Ambito.Bene.Casa.Indirizzo.NomeStrada" label="Via di residenza"
              onchange={handleDisambiguationFieldChange} value={fieldsValues.nomeStrada}></lightning-input>
            <lightning-input name="Ambito.Bene.Casa.Indirizzo.NumeroCivico" label="Numero civico"
              onchange={handleDisambiguationFieldChange} value={fieldsValues.numeroCivico}></lightning-input>
            <lightning-input name="Ambito.Bene.Casa.Indirizzo.Comune" label="Comune di residenza"
              onchange={handleDisambiguationFieldChange} value={fieldsValues.comune}></lightning-input>
            <lightning-input name="Ambito.Bene.Casa.Indirizzo.Provincia" label="Provincia di residenza"
              onchange={handleDisambiguationFieldChange} maxlength="2" value={fieldsValues.provincia}></lightning-input>
            <lightning-input name="Ambito.Bene.Casa.Indirizzo.Cap" label="CAP"
              onchange={handleDisambiguationFieldChange} maxlength="5" value={fieldsValues.cap}></lightning-input>
            <lightning-input name="Ambito.Bene.Casa.Indirizzo.Stato" label="Stato di residenza"
              onchange={handleDisambiguationFieldChange} value={fieldsValues.stato}></lightning-input>

            <lightning-button name="confermaIndirizzo" onclick={handleDisambiguationFieldChange}
              label="Conferma indirizzo"></lightning-button>
          </div>
        </div>
      </lightning-card>

      <!-- VEICOLO -->
      <lightning-card if:true={isVeicolo} title="Tipo veicolo">
        <div class="slds-var-m-around_medium">
          <lightning-combobox name="Ambito.Bene.Auto.TipoVeicolo" label="Seleziona veicolo"
            options={tipoDiVeicoloOptions} onchange={handleDisambiguationFieldChange}></lightning-combobox>

          <!-- Campo Targa (mostrato solo se noTarga è false) -->
          <template if:true={showTarga}>
            <lightning-input name="Ambito.Bene.Auto.Targa" label="Targa" placeholder="AA123BB"
              onchange={handleDisambiguationFieldChange} disabled={isChecked}>
            </lightning-input>
          </template>

          <!-- Checkbox "Non ho la targa" -->
          <lightning-input name="noTarga" type="checkbox" label="Non ho la targa" value={isChecked}
            onchange={handleDisambiguationFieldChange}></lightning-input>

          <lightning-input name="dataNascita" type="date" label="Data di nascita"
            onchange={handleDisambiguationFieldChange}></lightning-input>

          <lightning-card if:false={isEditable}>
            {addressDisplayer}
            <lightning-button name="confermaIndirizzo" onclick={handleDisambiguationFieldChange}
              label="Modifica indirizzo"></lightning-button>
          </lightning-card>
          <div if:true={isEditable}>
            <lightning-input name="Ambito.Proprietario.Residenza.NomeStrada" label="Via di residenza"
              onchange={handleDisambiguationFieldChange} value={fieldsValues.nomeStrada}></lightning-input>
            <lightning-input name="Ambito.Proprietario.Residenza.NumeroCivico" label="Numero civico"
              onchange={handleDisambiguationFieldChange} value={fieldsValues.numeroCivico}></lightning-input>
            <lightning-input name="Ambito.Proprietario.Residenza.Comune" label="Comune di residenza"
              onchange={handleDisambiguationFieldChange} value={fieldsValues.comune}></lightning-input>
            <lightning-input name="Ambito.Proprietario.Residenza.Provincia" label="Provincia di residenza"
              onchange={handleDisambiguationFieldChange} maxlength="2" value={fieldsValues.provincia}></lightning-input>
            <lightning-input name="Ambito.Proprietario.Residenza.Cap" label="CAP"
              onchange={handleDisambiguationFieldChange} maxlength="5" value={fieldsValues.cap}></lightning-input>
            <lightning-input name="Ambito.Proprietario.Residenza.Stato" label="Stato di residenza"
              onchange={handleDisambiguationFieldChange} value={fieldsValues.stato}></lightning-input>

            <lightning-button name="confermaIndirizzo" onclick={handleDisambiguationFieldChange}
              label="Conferma indirizzo"></lightning-button>
          </div>

          <lightning-input name="Ambito.Bene.Auto.RecuperoClasseAltroVeicolo" type="checkbox"
            label="Voglio trasferire una classe di merito da un altro veicolo"
            onchange={handleDisambiguationFieldChange}></lightning-input>
        </div>
      </lightning-card>

      <!-- Salute -->
      <lightning-card if:true={isSalute} title="Data di nascita">
        <div class="slds-var-m-around_medium">
          <lightning-input name="dataNascita" type="date" label="Data di nascita"
            onchange={handleDisambiguationFieldChange}>
          </lightning-input>
        </div>
      </lightning-card>

      <!-- Infortuni -->
      <lightning-card if:true={isInfortuni} title="Data di nascita">
        <div class="slds-var-m-around_medium">
          <lightning-input name="dataNascita" type="date" label="Data di nascita"
            onchange={handleDisambiguationFieldChange}>
          </lightning-input>
        </div>
      </lightning-card>

      <!-- Mobilità -->
      <lightning-card if:true={isMobilita} title="Data di nascita">
        <div class="slds-var-m-around_medium">
          <lightning-input name="dataNascita" type="date" label="Data di nascita"
            onchange={handleDisambiguationFieldChange}>
          </lightning-input>

          <lightning-card if:false={isEditable}>
            {addressDisplayer}
            <lightning-button name="confermaIndirizzo" onclick={handleDisambiguationFieldChange}
              label="Modifica indirizzo"></lightning-button>
          </lightning-card>
          <div if:true={isEditable}>
            <lightning-input name="Ambito.Proprietario.Residenza.NomeStrada" label="Via di residenza"
              onchange={handleDisambiguationFieldChange}></lightning-input>
            <lightning-input name="Ambito.Proprietario.Residenza.NumeroCivico" label="Numero civico"
              onchange={handleDisambiguationFieldChange}></lightning-input>
            <lightning-input name="Ambito.Proprietario.Residenza.Comune" label="Comune di residenza"
              onchange={handleDisambiguationFieldChange}></lightning-input>
            <lightning-input name="Ambito.Proprietario.Residenza.Provincia" label="Provincia di residenza"
              onchange={handleDisambiguationFieldChange} maxlength="2"></lightning-input>
            <lightning-input name="Ambito.Proprietario.Residenza.Cap" label="CAP"
              onchange={handleDisambiguationFieldChange} maxlength="5"></lightning-input>
            <lightning-input name="Ambito.Proprietario.Residenza.Stato" label="Stato di residenza"
              onchange={handleDisambiguationFieldChange}></lightning-input>

            <lightning-button name="confermaIndirizzo" onclick={handleDisambiguationFieldChange}
              label="Conferma indirizzo"></lightning-button>
          </div>
        </div>
      </lightning-card>

      <!-- Viaggi -->
      <lightning-card if:true={isViaggi}>
        <div class="slds-var-m-around_medium">
          <lightning-combobox name="tipologiaViaggi" label="Paese di destinazione" options={tipoDiViaggiOptions}
            onchange={handleDisambiguationFieldChange}>
          </lightning-combobox>
        </div>
      </lightning-card>

      <!-- Cane e Gatto -->
      <lightning-card if:true={isPet}>
        <div class="slds-var-m-around_medium">
          <lightning-combobox name="tipologiaPet" label="Tipologia animale" options={tipoDiPet}
            onchange={handleDisambiguationFieldChange}>
          </lightning-combobox>
          <lightning-combobox name="etaPet" label="Età animale" options={tipoDiEtaPet}
            onchange={handleDisambiguationFieldChange}>
          </lightning-combobox>
        </div>
      </lightning-card>

      <!-- FAMIGLIA -->
      <lightning-card if:true={isFamiglia} title="Tipo di abitazione">
        <div class="slds-var-m-around_medium">
          <lightning-combobox name="tipologiaAppartamento" label="Seleziona Tipo di appartamento"
            options={tipoDiAbitazioniOptions} onchange={handleDisambiguationFieldChange}>
          </lightning-combobox>
          <lightning-card if:false={isEditable}>
            {addressDisplayer}
            <lightning-button name="confermaIndirizzo" onclick={handleDisambiguationFieldChange}
              label="Modifica indirizzo"></lightning-button>
          </lightning-card>
          <div if:true={isEditable}>
            <lightning-input name="Ambito.Proprietario.Residenza.NomeStrada" label="Via di residenza"
              onchange={handleDisambiguationFieldChange}></lightning-input>
            <lightning-input name="Ambito.Proprietario.Residenza.NumeroCivico" label="Numero civico"
              onchange={handleDisambiguationFieldChange}></lightning-input>
            <lightning-input name="Ambito.Proprietario.Residenza.Comune" label="Comune di residenza"
              onchange={handleDisambiguationFieldChange}></lightning-input>
            <lightning-input name="Ambito.Proprietario.Residenza.Provincia" label="Provincia di residenza"
              onchange={handleDisambiguationFieldChange} maxlength="2"></lightning-input>
            <lightning-input name="Ambito.Proprietario.Residenza.Cap" label="CAP"
              onchange={handleDisambiguationFieldChange} maxlength="5"></lightning-input>
            <lightning-input name="Ambito.Proprietario.Residenza.Stato" label="Stato di residenza"
              onchange={handleDisambiguationFieldChange}></lightning-input>

            <lightning-button name="confermaIndirizzo" onclick={handleDisambiguationFieldChange}
              label="Conferma indirizzo"></lightning-button>
          </div>
        </div>
      </lightning-card>

      <!-- SUBMIT BUTTON -->
      <lightning-button label="Prosegui" onclick={handleProceed} class="slds-var-m-top_medium"
        variant="destructive"></lightning-button>
    </lightning-card>
  </div>

  <template if:true={showPopup}>
    <c-dx-error-popup error-message={modalErrorMessage} more-data={popupMoreData} onclosepopup={closePopup}
      onloadpage={showError}></c-dx-error-popup>
  </template>

  <!-- INTERPRETE -->
  <template if:true={showInterprete}>
    <c-interprete request={request} debug={debug} onnoresult={handleNoResult}></c-interprete>
  </template>

  <template if:true={showFailurePage}>
    <c-page-error request={request} error-message={errorMessage} reload={reloadVisible}></c-page-error>
  </template>

  <pre if:true={debug}>
    {debugJSON}
  </pre>
</template>