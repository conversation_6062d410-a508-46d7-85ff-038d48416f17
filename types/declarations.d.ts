type ClassFieldDecorator =
  | ClassGetterDecoratorContext
  | ClassFieldDecoratorContext;

declare module "lwc" {
  export class LightningElement {
    template: any;
  }
  // export class api {}

  // Decorator functions
  export function api(
    target: any,
    propertyKey: ClassFieldDecorator<Groups, any> & {
      name: string;
      private: false;
      static: false;
    }
  ): void;

  // Decorator functions
  export function track(
    target: any,
    propertyKey: ClassFieldDecorator<Groups, any> & {
      name: string;
      private: false;
      static: false;
    }
  ): void;
}
