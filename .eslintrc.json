{"root": true, "ignorePatterns": ["**/node_modules/*", ".localdevserver", ".sfdx", ".sf", ".vscode", "coverage/", "**/__pycache__/", "**/.venv", "**/venv"], "overrides": [{"files": ["**/*.js"], "extends": ["eslint:recommended"], "env": {"es6": true, "node": true, "browser": true}, "parserOptions": {"ecmaVersion": 2018, "sourceType": "module"}, "rules": {"no-unused-vars": "off", "no-prototype-builtins": "off", "consistent-return": "off"}}, {"files": ["force-app/main/default/aura/**/*.js"], "extends": ["plugin:@salesforce/eslint-plugin-aura/recommended"], "rules": {"vars-on-top": "off", "no-unused-expressions": "off"}}, {"files": ["force-app/main/default/lwc/**/*.js"], "extends": ["@salesforce/eslint-config-lwc/recommended"], "rules": {"no-unused-vars": "off", "no-prototype-builtins": "off", "consistent-return": "off"}, "overrides": [{"files": ["*.test.js"], "rules": {"@lwc/lwc/no-unexpected-wire-adapter-usages": "off"}, "env": {"node": true}}]}, {"files": ["**/*.html"], "extends": ["plugin:prettier/recommended"], "rules": {"prettier/prettier": ["error", {"parser": "html"}]}}], "plugins": ["@salesforce/eslint-plugin-aura", "eslint-plugin-import", "eslint-plugin-jest"], "extends": ["eslint:recommended", "plugin:import/errors", "plugin:import/warnings", "plugin:jest/recommended"], "env": {"node": true, "jest": true}, "parserOptions": {"ecmaVersion": 2020, "sourceType": "module"}, "rules": {"no-console": "warn", "import/no-extraneous-dependencies": ["error", {"devDependencies": true}]}}