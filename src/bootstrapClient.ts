import './polyfills';


import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { DynamicAppModule } from './app/app.module';
import { AppProps } from './app/app.props';
import { AngularMFE, MFE_NAMES } from '@tpd-web-common-libs/nodejs-library';
import { Helpers } from '@tpd-web-common-libs/nodejs-library';

export default AngularMFE.bootstrapClient(
  DynamicAppModule,
  AppProps,
  {
    platformBrowserDynamic

  },
  MFE_NAMES.tpdInterpreteAngularDxApiPu
);
