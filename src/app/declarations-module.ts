import { AccordionComponent } from './components/accordion/accordion.component';
import { Caption } from './components/caption/caption';
import { ButtonComponent } from './components/field-components/button/button.component';
import { DateTimeComponent } from './components/field-components/date-time/date-time.component';
import { DropdownButtonComponent } from './components/field-components/dropdown-button/dropdown-button.component';
import { EmailInputComponent } from './components/field-components/email-input/email-input.component';
import { InputNumberComponent } from './components/field-components/input-number/input-number.component';
import { InputTextAreaComponent } from './components/field-components/input-text-area/input-text-area.component';
import { InputTextComponent } from './components/field-components/input-text/input-text.component';
import { LinkComponent } from './components/field-components/link-component/link.component';
import { PxAutocompleteComponent } from './components/field-components/px-autocomplete/px-autocomplete.component';
import { CheckboxComponent } from './components/field-components/px-checkbox/checkbox.component';
import { PxHiddenComponent } from './components/field-components/px-hidden/px-hidden.component';
import { PxIconComponent } from './components/field-components/px-icon/px-icon.component';
import { RadioComponent } from './components/field-components/radio/radio.component';
import { BoxIndirizzoComponent } from './components/field-components/sub-components/box-indirizzo/box-indirizzo.component';
import { CardGaranziaComponent } from './components/field-components/sub-components/card-garanzia/card-garanzia.component';
import { CardProtezioneComponent } from './components/field-components/sub-components/card-protezione/card-protezione.component';
import { CarelloPUComponent } from './components/field-components/sub-components/carrello-pu/carrello-pu';
import { CircularStepperComponent } from './components/field-components/sub-components/circular-stepper/circular-stepper.component';
import { FeedbackPopupComponent } from './components/field-components/sub-components/feedback-popup/feedback-popup.component';
import { FooterDxAPIPUComponent } from './components/field-components/sub-components/footer-dx-api-pu/footer-dx-api-pu';
import { HeaderDXAPIComponent } from './components/field-components/sub-components/header-dx-api/header-dx-api';
import { ModalUnderwritingComponent } from './components/field-components/sub-components/modal-underwriting/modal-underwriting.component';
import { OtpComponent } from './components/field-components/sub-components/otp/otp.component';
import { SeparatorLineComponent } from './components/field-components/sub-components/parting-line/line.component';
import { PaymentComponent } from './components/field-components/sub-components/payment/payment';
import { StepperComponent } from './components/field-components/sub-components/stepper/stepper.component';
import { VerticalSpaceComponent } from './components/field-components/sub-components/vertical-space/vertical-space';
import { Field } from './components/field/field';
import { FormContainer } from './components/freeFormContainer/formContainer.component';
import { Groups } from './components/groups/groups';
import { Layout } from './components/layout/layout';
import PuPaddingLayout from './components/layout/PuPaddingLayout/PuPaddingLayout';
import { ModalDxApi } from './components/modal/modal-dx-api';
import { Paragraph } from './components/paragraph/paragraph';
import { Rows } from './components/rows/rows';
import { Tooltip } from './components/tooltip/tooltip';
import { TopLevelView } from './components/top-level-view/top-level-view';
import { View } from './components/view/view';
import { FormControlPipe } from './pipe/form-control.pipe';
import { SafeHtmlPipe } from './pipe/safehtml.pipe';
import { TpdInterpreteDxApi } from './tpd-interprete-angular-dx-api-pu/tpd-interprete-angular-dx-api-pu';
import CarouselLayout from './components/layout/CarouselLayout/CarouselLayout';
import AssurancePackageComponent from './components/field-components/sub-components/assurance-package/assurance-package.component';
import CardSezioneComponent from './components/field-components/sub-components/card-sezione/card-sezione.component';
import SplittedRadioLayout from './components/layout/SplittedRadioLayout/SplittedRadioLayout';
import { CustomTextStyleComponent } from './components/custom-text-style/custom-text-style';
import { ConfiguratoreMarketingCard } from './components/field-components/sub-components/configuratore-marketing-card/configuratore-marketing-card.component';
import CardGaranzieHeaderComponent from './components/field-components/sub-components/card-garanzie-header/CardGaranzieHeader.component';
import CarrelloVuotoComponent from './angular-wrapper/carrello-vuoto/carrello-vuoto.component';
import QuotazioneViaggiComponent from './angular-wrapper/quotazione-viaggi/quotazione-viaggi.component';
import CardProtezioneDettaglioGaranzia from './components/field-components/sub-components/card-protezione-dettaglio-garanzia/card-protezione-dettaglio-garanzia.component';
import QuotazioneMalattiaComponent from './angular-wrapper/quotazione-malattia/quotazione-malattia.component';
import QuotazioneInfortuniComponent from './angular-wrapper/quotazione-infortuni/quotazione-infortuni.component';
import UnicoProtezioneComponent from './components/field-components/sub-components/unico-protezione/unico-protezione.component';
import ToastCardComponent from './components/field-components/sub-components/toast-card/toast-card.component';
import PxIntegerComponent from './components/field-components/px-integer/px-integer.component';
import BoxPagamentoComponent from "./components/field-components/sub-components/box-pagamento/box-pagamento.component";
import BoxPrivacyLayoutComponent from "./components/layout/box-privacy-layout/box-privacy-layout.component";
import BoxPrivacyComponent from './components/field-components/sub-components/box-privacy/box-privacy.component';
import AccordionCardProtezioneComponent from './components/layout/accordion-card-protezione/accordion-card-protezione.component';
import ThankYouPageComponent from './angular-wrapper/thank-you-page/thank-you-page.component';
import CardTelematicaComponent from "./components/field-components/sub-components/card-telematica/card-telematica.component";
import LocatorComponent from "./components/field-components/sub-components/locator/locator.component";
import LocatorWrapperComponent from "./angular-wrapper/locator-wrapper/locator-wrapper.component";
import AddressComponentComponent from "./components/field-components/address-component/address-component.component";
import FallimentoPagamentoComponent from "./components/fallimento-pagamento/fallimento-pagamento.component";
import PaginaFallimentoComponent from "./components/pagina-fallimento/pagina-fallimento.component";
import FlatCardLayoutComponent from "./components/layout/flat-card-layout/flat-card-layout.component";
import OtpSplittedBoxComponent
  from "./components/field-components/sub-components/otp-splitted-box/otp-splitted-box.component";
import CheckboxDipendenzaComponent
  from "./components/field-components/px-checkbox/styled-checkbox/checkbox-dipendenza/checkbox-dipendenza.component";
import AccordionCheckboxComponent
  from "./components/field-components/sub-components/accordion-checkbox/accordion-checkbox.component";
import SubmitBlockerComponent from "./components/submit-blocker-component/submit-blocker.component";
import TabBarFrazionamentoComponent
  from "./components/field-components/sub-components/tab-bar-frazionamento/tab-bar-frazionamento.component";
import MultiStepperComponent from "./components/field-components/sub-components/multi-stepper/multi-stepper.component";
import CarouselCardComponent from "./components/field-components/sub-components/carousel-card/carousel-card.component";
import CustomLoadingComponent from "./custom-loading/custom-loading.component";

export function declarationsModule() {
  return [
    TpdInterpreteDxApi,
    TopLevelView,
    Groups,
    Layout,
    View,
    Field,
    Caption,
    Paragraph,
    SafeHtmlPipe,
    FormControlPipe,
    RadioComponent,
    ButtonComponent,
    LinkComponent,
    InputNumberComponent,
    InputTextComponent,
    CheckboxComponent,
    PxIconComponent,
    DateTimeComponent,
    AccordionComponent,
    EmailInputComponent,
    PxAutocompleteComponent,
    FormContainer,
    BoxIndirizzoComponent,
    ModalDxApi,
    ModalUnderwritingComponent,
    SeparatorLineComponent,
    OtpComponent,
    Rows,
    Tooltip,
    DropdownButtonComponent,
    PxHiddenComponent,
    HeaderDXAPIComponent,
    CardGaranziaComponent,
    CardProtezioneComponent,
    CircularStepperComponent,
    FeedbackPopupComponent,
    FooterDxAPIPUComponent,
    CarelloPUComponent,
    StepperComponent,
    VerticalSpaceComponent,
    PaymentComponent,
    PuPaddingLayout,
    CustomTextStyleComponent,
    InputTextAreaComponent,
    CarouselLayout,
    AssurancePackageComponent,
    CardSezioneComponent,
    SplittedRadioLayout,
    ConfiguratoreMarketingCard,
    CardGaranzieHeaderComponent,
    CarrelloVuotoComponent,
    ThankYouPageComponent,
    QuotazioneViaggiComponent,
    CardProtezioneDettaglioGaranzia,
    QuotazioneMalattiaComponent,
    QuotazioneInfortuniComponent,
    UnicoProtezioneComponent,
    ToastCardComponent,
    PxIntegerComponent,
    BoxPrivacyComponent,
    AccordionCardProtezioneComponent,
    BoxPagamentoComponent,
    BoxPrivacyLayoutComponent,
    CardTelematicaComponent,
    LocatorComponent,
    LocatorWrapperComponent,
    AddressComponentComponent,
    FallimentoPagamentoComponent,
    PaginaFallimentoComponent,
    FlatCardLayoutComponent,
    OtpSplittedBoxComponent,
    CheckboxDipendenzaComponent,
    AccordionCheckboxComponent,
    SubmitBlockerComponent,
    TabBarFrazionamentoComponent,
    MultiStepperComponent,
    CarouselCardComponent,


    CustomLoadingComponent
  ];
}
