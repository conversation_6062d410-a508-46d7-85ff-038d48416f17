// Work object form button actionID values
// FINISH is a pseudo-one used to offer a way to intoduce an alternate label for final screen flow submit
export const formButtonActionIDs = {
  CANCEL: 'cancel',
  SUBMIT: 'submit',
  SAVE: 'save',
  BACK: 'back',
  NEXT: 'next',
  FINISH: 'finish',
};

// Work object form default button labels (for each actionid)
export const formButtonLabels = {
  cancel: 'Cancel',
  submit: 'Submit',
  save: 'Save',
  back: 'Back',
  next: 'Continue',
  finish: 'Finish',
};
