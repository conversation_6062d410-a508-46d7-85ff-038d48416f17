import { Data } from '@angular/router';
import { CommonMFE } from '@tpd-web-common-libs/nodejs-library';

export class AppProps {
  public id: string;
  public __mfeName__: string;
  public __host__?: {
    [key: string]: any;
  };
  public config?: Data;
  public isEditMode?: boolean;

  constructor(props: CommonMFE.Types.TProps) {
    this.id = 'id' in props ? props.id : `component-${Date.now()}`;
    this.__mfeName__ =
      '__mfeName__' in props ? props.__mfeName__ : `DEFAULT NAME`;
    this.__host__ = '__host__' in props ? props.__host__ : {};
    this.isEditMode = 'isEditMode' in props ? props.isEditMode : false;
    this.config = 'config' in props ? props.config : {};
  }
}
