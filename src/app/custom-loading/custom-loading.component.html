<div
  *ngIf="this.modaleVisibile"
  class="CustomLoadingContainer">
  <div
    class="CustomLoadingContent">
    <svg class="ContentLogoUnica" width="138" height="35" viewBox="0 0 138 35" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_37007_244799)">
        <path d="M70.2909 0.0546875H64.5V35.0005H70.2909V0.0546875Z" fill="white"/>
        <path d="M21.0609 0V10.0396V21.1505C21.0609 25.5169 17.5565 29.1726 13.2777 29.0892C9.12971 29.0079 5.79291 25.5544 5.79291 21.3026V0H0V22.0592H0.0204335C0.404584 29.2726 6.25879 35 13.4269 35C20.595 35 26.4492 29.2726 26.8333 22.0592H26.8538V0H21.0629H21.0609Z" fill="white"/>
        <path d="M38.4393 35V24.9604V13.8495C38.4393 9.48312 41.9437 5.82743 46.2224 5.9108C50.3705 5.99208 53.7073 9.4456 53.7073 13.6974V35.0021H59.4981V12.9408H59.4777C59.0976 5.72739 53.2414 0 46.0753 0C38.9093 0 33.053 5.72739 32.6689 12.9408H32.6484V35H38.4393Z" fill="white"/>
        <path d="M32.6484 0C35.8463 0 38.4393 2.64485 38.4393 5.90663V10.0375V10.9962L32.6484 17.499V0Z" fill="#003663"/>
        <path d="M138.002 17.5234H132.211V34.9974H138.002V17.5234Z" fill="white"/>
        <path d="M38.4393 0H32.6484V20.8128H38.4393V0Z" fill="white"/>
        <path d="M120.99 0.289062C111.594 0.289062 103.977 8.05897 103.977 17.6421C103.977 27.2253 111.594 34.9952 120.99 34.9952C130.385 34.9952 138.003 27.2253 138.003 17.6421C138.003 8.05897 130.385 0.289062 120.99 0.289062ZM120.951 29.1282C114.731 29.1282 109.69 23.9864 109.69 17.6421C109.69 11.2978 114.731 6.15609 120.951 6.15609C127.171 6.15609 132.212 11.2978 132.212 17.6421C132.212 23.9864 127.171 29.1282 120.951 29.1282Z" fill="white"/>
        <path d="M91.1148 29.136C84.8948 29.136 79.8538 23.9942 79.8538 17.6499C79.8538 11.3056 84.8948 6.1639 91.1148 6.1639C94.4046 6.1639 97.3654 7.60409 99.4231 9.89879C100.099 7.94173 101.025 6.10346 102.161 4.42359C99.1942 1.85169 95.3527 0.296875 91.1536 0.296875C81.7583 0.296875 74.1406 8.06678 74.1406 17.6499C74.1406 27.2331 81.7583 35.003 91.1536 35.003C95.3527 35.003 99.1942 33.4482 102.161 30.8763C101.025 29.1964 100.099 27.3602 99.4231 25.4011C97.3634 27.6958 94.4046 29.136 91.1148 29.136Z" fill="white"/>
        <path d="M13.4243 25.5679C16.2388 25.5679 18.5204 23.2407 18.5204 20.3699C18.5204 17.4991 16.2388 15.1719 13.4243 15.1719C10.6097 15.1719 8.32812 17.4991 8.32812 20.3699C8.32812 23.2407 10.6097 25.5679 13.4243 25.5679Z" fill="#BE2F31"/>
      </g>
      <defs>
        <clipPath id="clip0_37007_244799">
          <rect width="138" height="35" fill="white"/>
        </clipPath>
      </defs>
    </svg>
    <div
      class="ContentMain">
      <svg
        *ngIf="this.tipoLoading === 'showPrice'"
        class="MainContentIcon" width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M57.6033 43.1472C56.7185 43.1472 56.0033 43.864 56.0033 44.7472V54.1552H46.5953C45.7105 54.1552 44.9953 54.872 44.9953 55.7552C44.9953 56.6384 45.7105 57.3552 46.5953 57.3552H57.6033C58.4881 57.3552 59.2033 56.6384 59.2033 55.7552V44.7472C59.2033 43.864 58.4881 43.1472 57.6033 43.1472ZM17.4049 54.1552H7.99688V44.7472C7.99688 43.864 7.28168 43.1472 6.39688 43.1472C5.51208 43.1472 4.79688 43.864 4.79688 44.7472V55.7552C4.79688 56.6384 5.51208 57.3552 6.39688 57.3552H17.4049C18.2897 57.3552 19.0049 56.6384 19.0049 55.7552C19.0049 54.872 18.2897 54.1552 17.4049 54.1552ZM57.6033 6.39844H46.5953C45.7105 6.39844 44.9953 7.11364 44.9953 7.99844C44.9953 8.88164 45.7105 9.59844 46.5953 9.59844H56.0033V19.0064C56.0033 19.8896 56.7185 20.6064 57.6033 20.6064C58.4881 20.6064 59.2033 19.8896 59.2033 19.0064V7.99844C59.2033 7.11364 58.4881 6.39844 57.6033 6.39844ZM19.0049 7.99844C19.0049 8.88164 18.2897 9.59844 17.4049 9.59844H7.99688V19.0064C7.99688 19.8896 7.28168 20.6064 6.39688 20.6064C5.51208 20.6064 4.79688 19.8896 4.79688 19.0064V7.99844C4.79688 7.11364 5.51208 6.39844 6.39688 6.39844H17.4049C18.2897 6.39844 19.0049 7.11364 19.0049 7.99844Z" fill="white"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M37.8693 41.8047C37.0598 41.8047 36.4055 42.4605 36.4055 43.2685V50.5878H39.3332V43.2685C39.3332 42.4605 38.6788 41.8047 37.8693 41.8047ZM27.6152 43.2685V50.5878H24.6875V43.2685C24.6875 42.4605 25.3418 41.8047 26.1514 41.8047C26.9609 41.8047 27.6152 42.4605 27.6152 43.2685Z" fill="white"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M41.8772 50.5894H39.3242H36.3965H27.6133H24.6856H22.1326V42.9495C22.1326 35.8528 27.1376 33.1768 31.8204 33.0392C31.8629 33.0378 31.9054 33.0348 31.9493 33.0304C31.98 33.0319 32.0137 33.0363 32.0195 33.0319V33.0246C32.0752 33.0334 32.1323 33.0378 32.1893 33.0392C36.8722 33.1768 41.8772 35.8528 41.8772 42.9495V50.5894ZM32.5082 30.1247C32.4174 30.1071 32.3252 30.0983 32.23 30.0983L31.9753 30.1042C31.949 30.1027 31.8026 30.0983 31.7762 30.0983C31.6825 30.0983 31.5918 30.1071 31.5025 30.1247C24.0222 30.4423 19.2031 35.456 19.2031 42.9524V52.0561C19.2031 52.8642 19.8575 53.52 20.667 53.52H26.1477H37.8586H43.3393C44.1488 53.52 44.8031 52.8642 44.8031 52.0561V42.9524C44.8031 35.4575 39.9856 30.4438 32.5082 30.1247ZM31.9979 12.5311C35.6298 12.5311 38.5853 15.4866 38.5853 19.1184C38.5853 22.7502 35.6298 25.7057 31.9979 25.7057C28.3646 25.7057 25.4106 22.7502 25.4106 19.1184C25.4106 15.4866 28.3646 12.5311 31.9979 12.5311ZM31.9962 28.6317C37.2426 28.6317 41.5112 24.3631 41.5112 19.1166C41.5112 13.8702 37.2426 9.60156 31.9962 9.60156C26.7497 9.60156 22.4811 13.8702 22.4811 19.1166C22.4811 24.3631 26.7497 28.6317 31.9962 28.6317Z" fill="white"/>
      </svg>
      <svg
        *ngIf="this.tipoLoading === 'warranty'"
        class="MainContentIcon" width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M53.0748 23.7656C52.6866 24.4696 51.9591 24.8685 51.206 24.8685C50.8583 24.8685 50.5042 24.7832 50.1778 24.6019C49.1452 24.0323 48.7698 22.7352 49.3415 21.7027C50.4487 19.6973 50.0903 17.1608 48.4668 15.5374C46.9671 14.0355 44.6482 13.6003 42.7069 14.46C41.6231 14.9379 40.3687 14.4472 39.8909 13.3699C39.4173 12.2926 39.9037 11.0339 40.981 10.5582C44.5287 8.99016 48.7484 9.77949 51.4834 12.5208C54.4423 15.4819 55.0972 20.1048 53.0748 23.7656ZM63.9986 16.3093V22.0885C63.9986 23.5626 62.9533 24.8597 61.5133 25.1712L60.0327 25.4933C60.0285 25.5018 60.0263 25.5125 60.0221 25.521L60.8413 26.7989C61.6391 28.032 61.4642 29.6853 60.421 30.7306L57.3085 33.8432C56.8925 34.2592 56.3463 34.4682 55.8002 34.4682C55.2541 34.4682 54.7101 34.2592 54.2919 33.8432C53.4599 33.009 53.4599 31.6586 54.2919 30.8266L56.7666 28.3498L55.8002 26.8458C55.3906 26.208 55.3479 25.4016 55.6935 24.7274C55.9773 24.1642 56.2205 23.584 56.4082 22.9973C56.645 22.2741 57.2445 21.7322 57.9869 21.5722L59.7319 21.1904V17.2074L57.9869 16.8277C57.2445 16.6677 56.645 16.1237 56.4082 15.4026C56.2205 14.816 55.9773 14.2357 55.6935 13.6725C55.3479 12.9984 55.3906 12.192 55.8002 11.5541L56.7666 10.048L53.9485 7.23199L52.4423 8.19839C51.8066 8.61012 51.0002 8.64852 50.3218 8.30506C49.7671 8.02132 49.1869 7.78026 48.5938 7.58826C47.8727 7.35359 47.3309 6.75412 47.1687 6.01173L46.7933 4.26666H42.8061L42.4285 6.01173C42.2685 6.75412 41.7245 7.35359 41.0034 7.58826C40.4104 7.78026 39.8301 8.02132 39.2754 8.30506C38.5992 8.64852 37.7949 8.61012 37.1549 8.19839L35.6488 7.23199L33.172 9.70665C32.3378 10.5408 30.9874 10.5408 30.1554 9.70665C29.3213 8.87466 29.3213 7.52426 30.1554 6.69012L33.2658 3.57973C34.3026 2.5408 35.9581 2.35946 37.1997 3.1552L38.4776 3.97866C38.4861 3.97439 38.4968 3.97013 38.5053 3.96586L38.8253 2.4832C39.141 1.0432 40.436 0 41.9101 0H47.6893C49.1613 0 50.4562 1.0432 50.7719 2.48106L51.0919 3.96586C51.1005 3.97013 51.1111 3.97439 51.1197 3.97866L52.3997 3.15733C53.6413 2.35946 55.2946 2.5408 56.3314 3.57973L60.4189 7.66719C61.4642 8.71466 61.637 10.3701 60.8391 11.6053L60.0221 12.8768C60.0263 12.8874 60.0285 12.896 60.0327 12.9067L61.5133 13.2288C62.9533 13.5402 63.9986 14.8352 63.9986 16.3093ZM46.9349 41.7831L43.9013 42.4423C43.1631 42.6045 42.5616 43.1463 42.3269 43.8674C42.0517 44.7122 41.704 45.5506 41.2944 46.3591C40.9488 47.0354 40.9914 47.8439 41.401 48.4796L43.0799 51.0908L38.2928 55.878L35.6837 54.1991C35.0458 53.7895 34.2416 53.749 33.561 54.0924C32.761 54.4999 31.9226 54.8476 31.0672 55.1271C30.3461 55.3618 29.8064 55.9612 29.6442 56.7015L28.985 59.733H22.2181L21.561 56.7015C21.3968 55.9612 20.857 55.3618 20.1381 55.1271C19.2805 54.8476 18.4443 54.4999 17.6443 54.0924C16.968 53.749 16.1595 53.7895 15.5195 54.1991L12.9104 55.878L8.1232 51.0908L9.80213 48.4796C10.2117 47.8439 10.2544 47.0354 9.91093 46.3591C9.4992 45.5506 9.15147 44.7122 8.87627 43.8674C8.6416 43.1463 8.04213 42.6045 7.30187 42.4423L4.26827 41.7831V35.0141L7.30187 34.3549C8.04213 34.1949 8.6416 33.653 8.87627 32.9319C9.15147 32.085 9.4992 31.2466 9.91093 30.4381C10.2544 29.7639 10.2117 28.9554 9.80213 28.3175L8.1232 25.7085L12.9104 20.9213L15.5195 22.6002C16.1595 23.012 16.968 23.0525 17.6443 22.7069C18.4443 22.2994 19.2805 21.9517 20.1381 21.6722C20.857 21.4376 21.3968 20.836 21.561 20.0978L22.2181 17.0664H28.985L29.6442 20.0978C29.8064 20.836 30.3461 21.4376 31.0672 21.6722C31.9226 21.9517 32.761 22.2994 33.561 22.7069C34.2416 23.0525 35.0458 23.012 35.6837 22.6002L38.2928 20.9213L43.0799 25.7085L41.401 28.3175C40.9914 28.9554 40.9488 29.7639 41.2944 30.4381C41.704 31.2466 42.0517 32.085 42.3269 32.9319C42.5616 33.653 43.1631 34.1949 43.9013 34.3549L46.9349 35.0141V41.7831ZM48.4181 30.9718H48.4159L45.9626 30.4385C45.8559 30.1676 45.745 29.8988 45.6277 29.6321L46.9866 27.518C47.8762 26.1313 47.6799 24.2774 46.513 23.1084L40.8917 17.4892C39.7034 16.3031 37.8901 16.1068 36.4799 17.0135L34.368 18.3724C34.1013 18.2551 33.8325 18.1441 33.5616 18.0375L33.0282 15.5863C32.6784 13.9713 31.2256 12.8001 29.5722 12.8001H21.6277C19.9744 12.8001 18.5216 13.9713 18.1717 15.5841L17.6405 18.0375C17.3674 18.1441 17.0986 18.2551 16.832 18.3724L14.7178 17.0113C13.3034 16.1068 11.4922 16.3052 10.3083 17.4892L4.68479 23.1084C3.51999 24.2774 3.32373 26.1313 4.21546 27.5201L5.57226 29.6321C5.45493 29.8988 5.34399 30.1676 5.23733 30.4385L2.78186 30.9718C1.1712 31.3238 0 32.7766 0 34.4257V42.3745C0 44.0236 1.1712 45.4763 2.784 45.8284L5.23733 46.3617C5.34399 46.6326 5.45493 46.9014 5.57226 47.1681L4.21333 49.2801C3.32373 50.6689 3.51999 52.5227 4.68693 53.6918L10.3083 59.311C11.4944 60.4971 13.3056 60.6913 14.72 59.7846L16.832 58.4278C17.0986 58.5451 17.3674 58.6561 17.6405 58.7627L18.1717 61.2139C18.5216 62.8289 19.9744 64.0001 21.6277 64.0001H29.5722C31.2256 64.0001 32.6784 62.8289 33.0282 61.2161L33.5616 58.7627C33.8325 58.6561 34.1013 58.5451 34.368 58.4278L36.4842 59.7889C37.8965 60.6934 39.7077 60.495 40.8917 59.311L46.5151 53.6897C47.6799 52.5227 47.8762 50.6689 46.9845 49.2779L45.6277 47.1681C45.745 46.9014 45.8559 46.6326 45.9626 46.3617L48.4181 45.8284C50.0287 45.4763 51.1999 44.0236 51.1999 42.3745V34.4257C51.1999 32.7766 50.0287 31.3238 48.4181 30.9718Z" fill="white"/>
      </svg>
      <span
        class="MainLabel"
        [ngClass]="{
          dotAnimation: this.tipoLoading === 'warranty'
        }"
        [innerHTML]="this.tipoLoading === 'warranty' ? 'Stiamo aggiornando l\'offerta' : this.fraseVisualizzata">
      </span>
      <span
        class="MainLoader"
        [ngClass]="{
          completaLoading: this.completaLoading
        }">
      </span>
      <span
        *ngIf="this.fraseVisualizzata && this.tipoLoading === 'showPrice'"
        class="MainAmbitoContent">
        {{this.fraseVisualizzata}}
      </span>
    </div>
  </div>
</div>
