@import "../../variables.scss";

.CustomLoadingContainer{
  display: flex;
  width: 100vw;
  height: 100vh;

  position: fixed;
  flex-direction: column;
  align-items: center;
  left: 0;
  top: 0;

  padding: 110px 16px;

  background-color: rgba(25, 58, 86, 0.8);
  backdrop-filter: blur(10px);

  z-index: 1000;

  @media #{$bkp_mobile_only} {
    padding: 64px 16px;
  }

  >.CustomLoadingContent{
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 200px;
    width: 100%;
    max-width: 528px;

    @media #{$bkp_mobile_only} {
      gap: 80px;
    }

    >.ContentLogoUnica{
      @media #{$bkp_mobile_only} {
        width: 78px;
      }
    }

    >.ContentMain{
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 40px;
      width: 100%;

      @media #{$bkp_mobile_only} {
        gap: 32px;
      }

      >.MainContentIcon{
        width: 64px;

        @media #{$bkp_mobile_only} {
          width: 40px;
        }
      }

      >.MainLabel{
        font-family: 'Unipol';
        color: white;
        font-size: 20px;
        position: relative;
        text-align: center;

        @media #{$bkp_mobile_only} {
          font-size: 16px;
        }

        &.dotAnimation{
          &::after{
            content: '.';

            position: absolute;
            left: 100%;
            top: 0;

            animation-duration: 3s;
            animation-iteration-count: infinite;
            animation-name: AnimazioneLabel;
          }
        }
      }

      @keyframes AnimazioneLabel {
        0%{
          content: '.';
        }

        33%{
          content: "..";
        }

        66%{
          content: "...";
        }
      }

      >.MainLoader{
        display: block;
        width: 100%;
        height: 4px;

        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 32px;

        position: relative;

        &::after{
          content: "";
          transition-duration: 3s;
          display: block;
          height: 100%;
          background-color: white;
          border-radius: 32px;
          width: 0;

          animation-iteration-count: 1;
          animation-duration: 2s;
          animation-fill-mode: forwards;
          animation-name: AnimazioneCaricamento80;
        }

        @keyframes AnimazioneCaricamento80 {
          0%{
            width: 0;
          }

          100%{
            width: 80%;
          }
        }

        &.completaLoading{
          &::after{
            animation-duration: 0.5s;
            animation-name: AnimazioneCaricamento80100;
          }
        }

        @keyframes AnimazioneCaricamento80100 {
          0%{
            width: 80%;
          }

          100%{
            width: 100%;
          }
        }
      }

      >.MainAmbitoContent{
        padding-top: 40px;
        font-family: "Unipol Medium";
        font-size: 22px;
        text-align: center;
        color: white;

        @media #{$bkp_mobile_only} {
          padding-top: 8px;
          font-size: 18px;
        }
      }
    }
  }
}
