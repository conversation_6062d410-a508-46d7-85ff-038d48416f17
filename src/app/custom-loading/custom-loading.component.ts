import { Component, Input, OnDestroy } from "@angular/core";

@Component({
  templateUrl: './custom-loading.component.html',
  styleUrls: ['./custom-loading.component.scss'],
  selector: 'custom-loading-interprete'
})
export default class CustomLoadingComponent implements OnDestroy{
  private _timeout: number;

  @Input() tipoLoading: "showPrice" | "warranty" = "showPrice";
  @Input() completaLoading = false;

  public fraseVisualizzata: string
  public modaleVisibile = false;

  @Input() set listaFrasi(frasi: {showPrice: string[], warranty: string[]}) {
    if(frasi){
      const targetList = this.tipoLoading === 'showPrice' ? frasi.showPrice : frasi.warranty;
      if(targetList.length > 0){
        const selezionaFrase = () => {
          this.fraseVisualizzata = targetList[Math.floor(targetList.length)];
        }

        selezionaFrase();
        this._timeout = window.setInterval(() => selezionaFrase(), 1000);
      }
    }
  }

  @Input() set mostraCaricamento(statoVisibilita: boolean){
    if(statoVisibilita)
      !this.modaleVisibile && this.mostraModale();
    else {
      this.modaleVisibile && this.nascondiModale();
    }
  }

  public mostraModale() {
    this.modaleVisibile = true;
    /*
    const body = document.body;
    if(body){
      body.style.overflow = 'hidden';
    }*/
  }

  public nascondiModale() {
    this.modaleVisibile = false;
    /*
    const body = document.body;
    if(body){
      body.style.overflow = 'auto';
    }*/
  }

  public ngOnDestroy(): void {
    this._timeout !== undefined && window.clearInterval(this._timeout);
  }
}
