import { CommonModule } from '@angular/common';
import { HttpClientJsonpModule } from '@angular/common/http';
import { HttpClientModule } from '@angular/common/http';
import { FormsModule } from '@angular/forms';
import { ReactiveFormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import {
  RendererModule,
  TransferHttpCacheModule,
} from '@nguniversal/common/clover';
import {
  CommonsComponentsModule,
  TPDConfiguratoreCommon,
  TpdNetworkModule,
} from '@tpd-web-angular-libs/angular-library';

export function importsModule() {
  return [
    BrowserModule.withServerTransition({ appId: 'appId' }),
    RendererModule.forRoot(),
    TransferHttpCacheModule,
    ReactiveFormsModule,
    CommonModule,
    FormsModule,
    HttpClientModule,
    HttpClientJsonpModule,
    TpdNetworkModule,
    TPDConfiguratoreCommon,
    NoopAnimationsModule,
    CommonsComponentsModule,
  ];
}
