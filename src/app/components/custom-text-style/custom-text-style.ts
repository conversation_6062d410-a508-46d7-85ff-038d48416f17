import { Component, Input } from '@angular/core';
import { TextCss, TextHandlerService } from "../../services/text-handler.service";

@Component({
  selector: 'custom-text-style',
  templateUrl: './custom-text-style.html',
  styleUrls: ['./custom-text-style.scss'],
})
export class CustomTextStyleComponent {
  @Input() public textCss: TextCss | null = null;
  @Input() public content = '';

  public constructor(
    private _textHandlerService: TextHandlerService
  ) {}

  @Input() set textCssString(value: string){
    this.textCss = this._textHandlerService.getTextCss(value);
  }
}
