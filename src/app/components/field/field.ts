import { Component, ElementRef, Input } from '@angular/core';
import { IField, IGroup, ILayout } from '../../models/models.commons';
import PuPaddingLayout from '../layout/PuPaddingLayout/PuPaddingLayout';

export interface IDataInputField {
  field: IField | undefined;
  groups?: IGroup[] | undefined;
}

/**
 * Interfaccia utilizzata per la gestione dei customAttributes per la gestione del padding
 */
interface CustomPaddingInterface {
  paddingApp?: string;
  paddingDesktop?: string;
  paddingTablet?: string;
  paddingMobile?: string;
}

@Component({
  selector: 'field',
  templateUrl: './field.html',
  styleUrls: ['./field.scss'],
})
export class Field {
  private _dataInputField: IDataInputField | undefined;

  private _field: IField | undefined;
  private _groups: IGroup[] | undefined;
  private _layout?: ILayout;

  private _paddingField = false;
  private _paddingGroupFormat = '';

  public isWeb = true;
  public isCustomComponent = false;

  public constructor(
    private _elementRef: ElementRef
  ) {}


  @Input() set data(input: IDataInputField) {
    if (input) {
      this._dataInputField = input;
      this._field = input.field;
      this._groups = input.groups;
      this.isWeb = !(
        this._field?.customAttributes?.platform &&
        this._field?.customAttributes?.platform !== 'web'
      );

      if(this._field?.customAttributes?.forcePosition && this._elementRef.nativeElement){
        const forcePosition = this._field?.customAttributes?.forcePosition;
        switch (forcePosition){
          case 'bottom':
            (this._elementRef.nativeElement as HTMLElement).style.alignSelf = 'flex-end';
            (this._elementRef.nativeElement as HTMLElement).style.width = '100%';
            break;
        }
      }

      if (this.hasPaddingAttributes() && this._field) {
        if (!this._groups) this._groups = [{ field: this._field }];
        if (
          this._field.control &&
          this._field.control.type &&
          this._field.control.type === 'pxHidden'
        )
          this.isCustomComponent = true;

        this._paddingField = true;
        this._paddingGroupFormat = this._createPaddingGroupFormat();
        this._deleteCustomPaddingAttributes();
      }
    }
  }

  public hasPaddingAttributes(): boolean {
    let esito = false;
    if (this._field && this._field.customAttributes) {
      const customAttributes = this._field
        .customAttributes as CustomPaddingInterface;

      esito = !!(
        customAttributes.paddingApp ||
        customAttributes.paddingMobile ||
        customAttributes.paddingTablet ||
        customAttributes.paddingDesktop
      );
    }
    return esito;
  }

  private _createPaddingGroupFormat(): string {
    let esito = '';

    if (this._field && this._field.customAttributes) {
      const customAttributes = this._field
        .customAttributes as CustomPaddingInterface;
      if (customAttributes.paddingApp)
        esito += `P ${customAttributes.paddingApp} `;
      if (customAttributes.paddingDesktop)
        esito += `D ${customAttributes.paddingDesktop} `;
      if (customAttributes.paddingTablet)
        esito += `T ${customAttributes.paddingTablet} `;
      if (customAttributes.paddingMobile)
        esito += `M ${customAttributes.paddingMobile}`;
    }

    return esito;
  }

  private _deleteCustomPaddingAttributes() {
    if (this._field && this._field.customAttributes) {
      const customAttributes = this._field
        .customAttributes as CustomPaddingInterface;
      customAttributes.paddingApp = undefined;
      customAttributes.paddingDesktop = undefined;
      customAttributes.paddingTablet = undefined;
      customAttributes.paddingMobile = undefined;
      this._field.customAttributes = customAttributes;
    }
  }

  get field() {
    return this._field;
  }

  get groups() {
    return this._groups;
  }

  get layout() {
    return this._layout;
  }

  get paddingField(): boolean {
    return this._paddingField;
  }

  get paddingGroupFormat(): string {
    return this._paddingGroupFormat;
  }
}
