<ng-container
  [ngSwitch]="field.control?.type"
  *ngIf="this.field && field.visible && isWeb">

  <pu-padding-layout
    *ngIf="this.paddingField; else noPaddingView"
    [data]="{groups: this.groups, groupFormat: this.paddingGroupFormat}"
    [isCustomComponent]="isCustomComponent">
  </pu-padding-layout>

  <ng-template #noPaddingView>
    <tpd-input-number [data]="{field: field}" *ngSwitchCase="'pxNumber'"></tpd-input-number>
    <ng-container *ngSwitchCase="'pxTextInput'">
      <tpd-input-text *ngIf="this.field?.control?.modes[0]?.controlFormat !== 'OTPSplittedBox'" [data]="{field: field}"></tpd-input-text>
      <otp-splitted-box *ngIf="this.field?.control?.modes[0]?.controlFormat === 'OTPSplittedBox'" [data]="{field: field}"></otp-splitted-box>
    </ng-container>
    <tpd-button [data]="{field: field}" *ngSwitchCase="'pxButton'"></tpd-button>
    <tpd-input-text [data]="{field: field}" *ngSwitchCase="'pxPhone'"></tpd-input-text>
    <tpd-input-number [data]="{field: field}" [currencyClass]="true" *ngSwitchCase="'pxCurrency'"></tpd-input-number>
    <tpd-link [data]="{field: field}" *ngSwitchCase="'pxLink'"></tpd-link>
    <tpd-radio [data]="{field: field}" *ngSwitchCase="'pxRadioButtons'"></tpd-radio>
    <tpd-px-checkbox [data]="{field: field}" *ngSwitchCase="'pxCheckbox'"></tpd-px-checkbox>
    <tpd-px-autocomplete [data]="{field: field}" *ngSwitchCase="'pxAutoComplete'"></tpd-px-autocomplete>
    <tpd-px-hidden [data]="{field: field, groups: groups}" *ngSwitchCase="'pxHidden'"></tpd-px-hidden>
    <tpd-email-input [data]="{field: field}" *ngSwitchCase="'pxEmail'"></tpd-email-input>
    <tpd-dropdown-button [data]="{field: field}" *ngSwitchCase="'pxDropdown'"></tpd-dropdown-button>
    <tpd-date-time [data]="{field:field}" *ngSwitchCase="'pxDateTime'"></tpd-date-time>
    <tpd-px-icon [data]="{field: field}" *ngSwitchCase="'pxIcon'"></tpd-px-icon>
    <tpd-input-text-area [data]="{field: field}" *ngSwitchCase="'pxTextArea'"></tpd-input-text-area>
    <px-integer *ngSwitchCase="'pxInteger'" [data]="{field: this.field}"></px-integer>
  </ng-template>
</ng-container>
