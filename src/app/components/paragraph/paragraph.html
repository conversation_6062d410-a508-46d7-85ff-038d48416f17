<ng-container *ngIf="this.paragraph.visible">
  <div
    *ngIf="!setCss"
    class="{{this.paragraphStyle}} paragraph"
    [innerHTML]="this.paragraphValue$ | safeHtml">
  </div>
  <!--Se arriva lo stile da PEGA-->
  <div *ngIf="setCss">
    <div
      class="visible-desktop"
      [ngStyle]="paragraphCss?.desktopCss"
      [innerHTML]="this.paragraphValue$ | safeHtml"
    ></div>
    <div
      class="visible-tablet"
      [ngStyle]="paragraphCss?.tabletCss"
      [innerHTML]="this.paragraphValue$ | safeHtml"
    ></div>
    <div
      class="visible-mobile"
      [ngStyle]="paragraphCss?.mobileCss"
      [innerHTML]="this.paragraphValue$ | safeHtml"
    ></div>
  </div>
</ng-container>
