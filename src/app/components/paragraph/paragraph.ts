import { Component, Input } from '@angular/core';
import {
  TextCss,
  TextHandlerService,
} from '../../services/text-handler.service';
import { IParagraph } from '../../models/models.commons';
import { UtilsService } from '../../services/utils.service';

const REGEX_STYLE_PARAGRAPH = /data-pega-style="([^"]*)"/;

@Component({
  selector: 'paragraph',
  templateUrl: './paragraph.html',
  styleUrls: ['./paragraph.scss'],
})
export class Paragraph {
  private _paragraph!: IParagraph;
  private _paragraphValue = '';

  @Input() valoriDiTesta: string[] = [];
  @Input() valoriDiCoda: string[] = [];

  public paragraphStyle: string | undefined;

  public paragraphCss?: TextCss | null;
  public setCss = false;

  constructor(
    private utilsService: UtilsService,
    private textHandlerService: TextHandlerService
  ) {
    /*DO NOTHING*/
  }

  @Input() set paragraph(input: IParagraph) {
    this._paragraph = input;
    this._paragraphValue = this._paragraph.value;

    const elemAttr = this._paragraphValue
      ? this._paragraphValue.match(REGEX_STYLE_PARAGRAPH)
      : undefined;
    if (elemAttr) {
      if (this._paragraphValue) {
        this._paragraphValue = elemAttr[0]
          ? this._paragraphValue.replace(elemAttr[0], '')
          : this._paragraphValue;
      }

      if (elemAttr[1]) {
        this.paragraphStyle = this.utilsService.getClassFromFormat(elemAttr[1]);
        this.paragraphCss = this.textHandlerService.getTextCss(elemAttr[1]);
        this.setCss = !!this.paragraphCss;
      }
    }
  }

  public get paragraph() {
    return this._paragraph;
  }

  public get paragraphValue$(): string{

    const stringaDiTesta = this.valoriDiTesta.join(' ');
    const stringaDiCoda = this.valoriDiCoda.join(' ');


    let esito = this._paragraphValue || "";
    if(stringaDiTesta.length !== 0 || stringaDiCoda.length !== 0) {
      if (stringaDiTesta)
        esito = `${stringaDiTesta} ${esito}`;
      if (stringaDiCoda)
        esito = `${esito} ${stringaDiCoda}`;

      esito = `<p>${esito.replace('<p >', '').replace('</p>', '')}</p>`;
    }

    return decodeURIComponent(encodeURI(esito));
  }
}
