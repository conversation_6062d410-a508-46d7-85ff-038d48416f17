import { Component, Input } from '@angular/core';
import { FormArray, FormGroup } from '@angular/forms';
import { ModaleErroreDataType } from '@tpd-web-angular-libs/angular-library';
import { ModaleErroreProdottoUnicoComponent } from '@tpd-web-angular-libs/angular-library';
import { TipoModaleErrore } from '@tpd-web-angular-libs/angular-library';
import { Helpers } from '@tpd-web-common-libs/nodejs-library';

import { IView } from '../../models/models.commons';

import { StatusService } from '../../services/status.service';
import { TpdInterpreteDxApi } from "../../tpd-interprete-angular-dx-api-pu/tpd-interprete-angular-dx-api-pu";

@Component({
  selector: 'top-level-view',
  templateUrl: './top-level-view.html',
  styleUrls: ['./top-level-view.scss'],
})
export class TopLevelView {
  private static _toastVisualizzato = false;

  public tipoModaleErrore: TipoModaleErrore;
  public datiModaleErrore: ModaleErroreDataType;

  private _view: IView;
  private _visible = false;
  public formGroup: FormGroup;
  private _formArray: FormArray;

  public visualizzaModaleErrore = false;
  public messaggioModaleErrore = "";

  public visualizzaToast = false;
  public messaggioToast = "";

  public constructor(
    private _statusService: StatusService
  ) {}

  @Input() set view(input: IView) {
    if (input) {
      this._view = input;
      this._visible = !!input.visible;
      let idxLayoutPayment = -1;
      let idxlayoutNotCustom = -1;
      for (let index = 0; index < this._view.groups.length; index++) {
        const group = this._view.groups[index];
        if (
          group.layout?.groupFormat === 'CustomComponent' &&
          group.layout.groups[0]?.field?.customAttributes?.customType === 'PaymentPage' &&
          this._view.groups[index + 1]?.layout?.groupFormat !== 'CustomComponent'
        ) {
          idxLayoutPayment = index;
          idxlayoutNotCustom = index + 1;
          break;
        }
      }
      if (idxLayoutPayment > 0 && idxlayoutNotCustom > 0) {
        const groupTempNotCustom = { ...this._view.groups[idxlayoutNotCustom] };
        const groupTempPayment = { ...this._view.groups[idxLayoutPayment] };
        this._view.groups[idxLayoutPayment] = groupTempNotCustom;
        this._view.groups[idxlayoutNotCustom] = groupTempPayment;
      }

      this.formGroup = new FormGroup({});
      this._formArray = new FormArray([]);

      this._statusService.initFormGroupInstance(this.formGroup);
      this._statusService.initFormArrayInstance(this._formArray);
      this._statusService.resetStatoFormIndipendenti();

      this._checkErroriValidationMessage();
      this._checkVisualizzazionePopup();
    }
  }

  public get view() {
    return this._view;
  }

  private async _checkVisualizzazionePopup(){
    if(this._view){
      switch (this._view.viewID){
        case "MostraDatiCarrello":
          if(!TopLevelView._toastVisualizzato){
            if(await this._statusService.itemAggiuntoAlCarrello()){
              this.visualizzaToast = true;
              this.messaggioToast = "Hai aggiunto il preventivo al carrello";
            }
          }
          break;
        default:
          TopLevelView._toastVisualizzato = false;
          break;
      }
    }
  }

  public chiudiToast(){
    this.visualizzaToast = false;
    TopLevelView._toastVisualizzato = true;
  }

  private _checkErroriValidationMessage(){
    if(this._view.validationMessages){
      if(Helpers.EnvironmentHelper.isClientSide())
        window.document.body.style.overflow = 'hidden';
      this.visualizzaModaleErrore = true;
      const datiParsati = ModaleErroreProdottoUnicoComponent.ParsaInformazioniModale(
        [this._view.validationMessages],
        undefined,
        undefined,
        () => {
          TpdInterpreteDxApi.TpdInterpreteDxApiContext.chiamaRichiestaDiContatto({});
          this.visualizzaModaleErrore = false;
        }
      );
      this.tipoModaleErrore = datiParsati.tipoModale;
      this.datiModaleErrore = datiParsati.datiModale;
    }
  }

  public chiudiModaleErrore(){
    if(Helpers.EnvironmentHelper.isClientSide())
      window.document.body.style.overflow = null;
    this.visualizzaModaleErrore = false;
    this.messaggioModaleErrore = "";
  }

  public get visible() {
    return this._visible;
  }
}
