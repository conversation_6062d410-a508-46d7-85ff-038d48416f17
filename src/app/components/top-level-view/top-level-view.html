<tpd-feedback-popup
  [visualizzaFeedbackPopup]="this.visualizzaToast"
  [contenutoPopup]="this.messaggioToast"
  (onClickClose)="this.chiudiToast()">
</tpd-feedback-popup>

<ng-container *ngIf="this.visible && this.view.groups">
  <modale-errore-prodotto-unico
    *ngIf="this.visualizzaModaleErrore"
    [tipoModaleErrore]="this.tipoModaleErrore"
    [modaleErroreData]="this.datiModaleErrore"
    (onCloseModal)="this.chiudiModaleErrore()">
  </modale-errore-prodotto-unico>
  <form [formGroup]="this.formGroup" name="workItem">
    <groups [groups]="this.view.groups"></groups>
  </form>
</ng-container>
