import { Component, EventEmitter, Input, Output } from '@angular/core';
import { IView } from '../../models/models.commons';
import { UtilsService } from '../../services/utils.service';

@Component({
  selector: 'modal-dx-api',
  templateUrl: './modal-dx-api.html',
  styleUrls: ['./modal-dx-api.scss'],
})
export class ModalDxApi {
  @Output() closeModalEmitter: EventEmitter<any> = new EventEmitter();

  public refreshOnClosePopupAssurance = false;
  private _data: IView | undefined;

  constructor(private utilsService: UtilsService) {}

  @Input() set data(input: IView | undefined) {
    this._data = input;
    this.utilsService.callbackComponentID['closeModal'] = async (action) => await this.closeModal(action || '');
  }

  public get data() {
    return this._data;
  }

  public async closeModal(action: string | boolean) {
    return new Promise(resolve => {
      let refresh;
      if (typeof action === 'boolean') {
        refresh = action ? action : this.refreshOnClosePopupAssurance;
      } else {
        refresh =
          action == 'true'
            ? true
            : action === 'false'
              ? this.refreshOnClosePopupAssurance
              : false;
      }
      this.closeModalEmitter.emit({ close: true, refreshPage: refresh, closeModalCompleteCallback: () => resolve(true)});
    });
  }
}
