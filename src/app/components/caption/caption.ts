import { Component, Input } from '@angular/core';
import { ICaption } from 'src/app/models/models.commons';
import {
  TextCss,
  TextHandlerService,
} from '../../services/text-handler.service';
import { UtilsService } from '../../services/utils.service';
import ComponentReferenceStorageService from "../../services/component-reference-storage.service";

@Component({
  selector: 'caption-elem',
  templateUrl: './caption.html',
  styleUrls: ['./caption.scss'],
})
export class Caption {
  private _caption: ICaption;

  private _captionValue = '';
  public captionFormat = '';
  public isVisible = false;

  public captionCss?: TextCss;

  constructor(
    private utilsService: UtilsService,
    private textHandlerService: TextHandlerService,
    private _componentReferenceStorage: ComponentReferenceStorageService
  ) {
    /*DO NOTHING*/
  }

  @Input() set caption(input: ICaption) {
    if (input) {
      this._caption = input;

      //Nel caso in cui ci sia un register
      if(this._caption.elementUniqueId) {
        this._componentReferenceStorage.storeComponent(this._caption.elementUniqueId, this);
      }

      if (this._caption.value)
        this._captionValue = this.utilsService.htmlDecode(this._caption.value);

      if (this._caption.control && this._caption.control.format) {
        this.captionCss = this.textHandlerService.getTextCss(this._caption.control.format);
        if(!this.captionCss){
          this.captionFormat = this.utilsService.getClassFromFormat(this._caption.control.format);
        }
      }

      this.isVisible = this._caption?.visible;
    }
  }

  public get captionValue(): string{
    return this._captionValue;
  }

  public set captionValue(value: string){
    this._captionValue = this.utilsService.htmlDecode(value);
  }
}
