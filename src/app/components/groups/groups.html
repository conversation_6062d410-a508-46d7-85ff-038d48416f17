<div
  #containerGroup
  *ngIf="groups || rows"
  class="GroupDetector"
  [ngStyle]="{
    'grid-template-columns': gridTemplatecolumns
  }"
  [ngClass]="groupClass">
  <ng-container *ngIf="rows && rows.length > 0">
    <ng-container *ngFor="let row of this.rows">
      <ng-container *ngIf="this.canRenderRowGroup(row.groups)">
        <ng-container *ngFor="let group of row.groups">
          <groups [groups]="[group]"></groups>
        </ng-container>
      </ng-container>
    </ng-container>
  </ng-container>
  <ng-container *ngIf="groups && groups.length>0">
    <ng-container *ngFor="let g of groups; index as i">
      <ng-container *ngIf="!isCustomComponentLayout">
        <layout
          *ngIf="g.layout && g.layout.visible"
          [layout]="g.layout"
          style="{{'width:' + this.isSpaceBetween ? 'auto': '100%'}}">
        </layout>
        <view
          *ngIf="g.view && g.view.visible"
          class="w-full"
          [view]="g.view">
        </view>
        <caption-elem
          *ngIf="g.caption && g.caption.visible"
          [caption]="g.caption">
        </caption-elem>
        <paragraph
          *ngIf="g.paragraph && g.paragraph.visible"
          [paragraph]="g.paragraph"
        ></paragraph>
        <field
          *ngIf="g.field && g.field.visible"
          [data]="{field: g.field}">
        </field>
      </ng-container>
      <ng-container *ngIf="this.isCustomComponentLayout">
        <field
          *ngIf="g.field && g.field.control?.type === 'pxHidden'"
          [data]="{field: g.field, groups: groups}">
        </field>
      </ng-container>
    </ng-container>
  </ng-container>
</div>
