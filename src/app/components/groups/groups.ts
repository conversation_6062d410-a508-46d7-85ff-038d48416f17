import { Component, Input, ViewChild, ElementRef } from '@angular/core';
import { UtilsService } from '../../services/utils.service';
import { IGroup, IRow } from '../../models/models.commons';
import ColorsService from '../../services/colors.service';
import <PERSON><PERSON><PERSON><PERSON>elper from "./helpers/row-col.helper";
import Card<PERSON>oxHelper from "./helpers/card-box.helper";
@Component({
  selector: 'groups',
  templateUrl: './groups.html',
  styleUrls: ['./groups.scss'],
})
export class Groups {
  private _containerGroup: HTMLDivElement;
  private _customInstructions: string;
  private _customInstructionInitialized = false;

  private _rowColHelper = new RowColHelper();
  private _cardBoxHelper = new CardBoxHelper();

  private _rows: IRow[];
  private _groups: IGroup[] | undefined;

  public customInstructionCompleted = false;
  public isSpaceBetween = false;
  public gridTemplatecolumns = '';

  @Input() groupClass = '';
  @Input() isCustomComponentLayout = false;
  @Input() customAttributes: Record<string, string> | undefined;

  public constructor(
    private utilsService: UtilsService,
    private colorsService: ColorsService
  ) {}

  @ViewChild('containerGroup') set containerGroup(containerGroup: ElementRef<HTMLDivElement>){
    this._containerGroup = containerGroup.nativeElement;
    if(this._containerGroup && this._customInstructions && !this._customInstructionInitialized){
      this._customInstructionInitialized = true;
      this.setStyleLayout(this._customInstructions, this.customAttributes);
    }else this.customInstructionCompleted = true;
  }

  @Input() set styleInstructions(input: string) {
    this._customInstructions = input;
    if(this._containerGroup && this._customInstructions && !this._customInstructionInitialized){
      this._customInstructionInitialized = true;
      this.setStyleLayout(this._customInstructions, this.customAttributes);
    }
  }

  @Input() set columnsWidth(input: number[]) {
    this.gridTemplatecolumns = `${input[0] - 1}% ${input[1] - 1}%`;
  }

  @Input() set groups(input: IGroup[] | undefined) {
    this._groups = input;
  }

  public get groups() {
    return this._groups;
  }

  @Input() set rows(input: IRow[] | undefined) {
    this._rows = input;
  }

  public get rows(): IRow[] {
    return this._rows ?? [];
  }

  public setStyleLayout(instructions: string, customAttributes?: Record<string, string>): void {
    const rowColRules = this._rowColHelper.getRules(instructions);
    const cardBoxRules = this._cardBoxHelper.getRules(instructions);

    //RowColAssignment
    if (rowColRules.alignment && rowColRules.alignment.length >= 1) {
      this.isSpaceBetween = this._rowColHelper.setAlignment(this._containerGroup, rowColRules.alignment, rowColRules.type);
    }
    if (rowColRules.gap && this._containerGroup) {
      this._rowColHelper.setGap(this._containerGroup, rowColRules.gap);
    }
    if (rowColRules.gridTemplateColumns && rowColRules.gridTemplateColumns.length >= 2) {
      this._rowColHelper.setColumnsWidth(this._containerGroup, rowColRules.gridTemplateColumns);
    }
    if (rowColRules.avaiableSpace && rowColRules.avaiableSpace.length >= 1) {
      this._rowColHelper.setAvaiableSpace(this._containerGroup, rowColRules.avaiableSpace);
    }
    if (rowColRules.maxWidht && rowColRules.maxWidht.length > 0) {
      this._rowColHelper.setMaxWidth(this._containerGroup, rowColRules.maxWidht.join(''));
    }

    //CardBoxAssignment
    if (cardBoxRules.bgColorCard && cardBoxRules.bgColorCard.length > 0) {
      this._cardBoxHelper.setBgColorCard(this._containerGroup, this.colorsService, cardBoxRules.bgColorCard, customAttributes);
    }

    if (cardBoxRules.borderRadiusCard && cardBoxRules.borderRadiusCard.length > 0) {
      this._cardBoxHelper.setBorderRadiusCard(this._containerGroup, cardBoxRules.borderRadiusCard);
    }

    if (cardBoxRules.setBorderCard && cardBoxRules.setBorderCard.length > 0) {
      this._cardBoxHelper.setBorderCard(this._containerGroup, this.colorsService, cardBoxRules.setBorderCard);
    }

    if (cardBoxRules.paddingCard && cardBoxRules.paddingCard.length > 0) {
      this._cardBoxHelper.setPaddingCard(this._containerGroup, cardBoxRules.paddingCard);
    }

    if (cardBoxRules.bgColorBox && cardBoxRules.bgColorBox.length > 0) {
      this._cardBoxHelper.setBgColorBox(this._containerGroup, this.colorsService, cardBoxRules.bgColorBox, customAttributes);
    }

    if (cardBoxRules.paddingBox && cardBoxRules.paddingBox.length > 0) {
      this._cardBoxHelper.setPaddingBox(this._containerGroup, cardBoxRules.paddingBox);
    }

    this.customInstructionCompleted = true;
  }

  public canRenderRowGroup(rowGroup: IGroup[]) {
    let esito = false;

    if (rowGroup) {
      esito = true;
      for (const group of rowGroup) {
        if (group.layout) esito &&= group.layout.visible;
        else if (group.view) esito &&= group.view.visible;
        else if (group.caption) esito &&= group.caption.visible;
        else if (group.paragraph) esito &&= group.paragraph.visible;
        else if (group.field) esito &&= group.field.visible;
      }
    }

    return esito;
  }
}
