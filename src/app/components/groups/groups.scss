@import "../../../styles.scss";

.GroupDetector::ng-deep{
  @media #{$bkp_mobile_only} {
    *:has(.growMobile){
      flex-grow: 1;
    }
  }
}

.outline{
  position: relative;
  box-shadow: inset 0 0 10px 5px #00FF28;
  cursor: pointer;
}

.w-100 {
  width: 100%;
}
.inlineDoubleGridLayout {
  @media #{$bkp_desktop} {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2%;
  }
}

.inlineTripleGridLayout {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2%;
}

.inlineGridXY::ng-deep {
  display: grid;
  gap: 2%;
  align-items: start;

  .layout-input {
    width: 100% !important;
  }

  .icon-Info2 {
    text-align: end;
    margin-right: 25px;
  }
  .tooltip-container {
    width: initial;
  }
}

.inlineMiddleAlignC::ng-deep {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 10px;
  .layout-input {
    width: 100% !important;
  }

  .icon-Info2 {
    text-align: end;
    margin-left: 10px;
  }
}

.inlineSpaceBetween {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: baseline;
  column-gap: 3vw;
}

.inlineWeb-layout::ng-deep {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2%;
  align-items: center;
  .layout-input {
    width: 100% !important;
  }
}

.responsive2col {
  display: flex;
  column-gap: 6vw;
  @media #{$bkp_mobile} {
    flex-direction: column;
  }
  @media #{$bkp_tablet} {
    flex-direction: column;
  }
  @media #{$bkp_desktop} {
    flex-direction: row;
    justify-content: space-evenly;
  }
}

.responsive2colPU {
  display: flex;
  flex-direction: column;
  gap: 10px;
  @media #{$bkp_tablet} {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }
  @media #{$bkp_desktop} {
    grid-template-columns: 1fr 1fr;
  }
}

.responsive2col1fr2fr {
  display: flex;
  flex-direction: column;
  row-gap: 20px;
  column-gap: 24px;

  > *:first-child {
    grid-area: a;
  }

  > *:last-child {
    grid-area: b;
  }

  @media #{$bkp_tablet} {
    display: grid;
    row-gap: 28px;

    grid-template:
      "a ." auto
      "b b" auto / 1fr 1fr;
  }

  @media #{$bkp_desktop} {
    grid-template: "a b" auto / 1fr 2fr;
    align-items: flex-end;
  }
}

.responsive2col2fr1fr {
  display: flex;
  flex-direction: column;
  row-gap: 20px;
  column-gap: 24px;

  > *:first-child {
    grid-area: a;
  }

  > *:last-child {
    grid-area: b;
  }

  @media #{$bkp_tablet} {
    display: grid;
    row-gap: 28px;

    grid-template:
      "a ." auto
      "b b" auto / 1fr 1fr;
  }

  @media #{$bkp_desktop} {
    grid-template: "a b" auto / 2fr 1fr;
    align-items: flex-end;
  }
}

.responsive2col8 {
  display: flex;
  flex-direction: column;
  gap: 8px;

  @media #{$bkp_tablet} {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }
  @media #{$bkp_desktop} {
    grid-template-columns: 1fr 1fr;
  }
}

.responsive3col {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  row-gap: 20px;
  align-items: baseline;

  @media #{$bkp_mobile_only} {
    align-items: stretch;
  }

  @media #{$bkp_tablet} {
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 24px;
    row-gap: 28px;
  }
  @media #{$bkp_desktop} {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

//Responsive 3col dove il 3° elemento viene messo in full width quando si trova in tablet
.responsive3col3fullwt {
  @extend .responsive3col;
  > *:nth-child(3) {
    @media #{$bkp_tablet_only} {
      grid-column: 1 / span 2;
    }
  }
}

.responsive4col {
  display: flex;
  flex-direction: column;
  row-gap: 20px;
  @media #{$bkp_mobile} {
    grid-template-columns: 1fr;
  }
  @media #{$bkp_tablet} {
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 24px;
    row-gap: 28px;
  }
  @media #{$bkp_desktop} {
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }
}

.mimicASentence {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: baseline;
  gap: 0.2em;

  width: auto;
}

.mimicASentenceCenter {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: baseline;
  gap: 0.2em;
  justify-content: center;
}

.mimicASentenceRight {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: baseline;
  gap: 0.2em;
  justify-content: flex-end;
}

.mimicASentence-responsiveAlign {
  display: flex;
  flex-direction: row;
  gap: 0.2em;
  @media #{$bkp_mobile} {
    align-items: flex-end;
  }

  @media #{$bkp_tablet} {
    align-items: flex-start;
  }

  @media #{$bkp_desktop} {
    align-items: flex-start;
  }
}

.rowCenteredY {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.4em;
}

.responsive2colLarge::ng-deep {
  gap: 4%;
  align-items: center;
  @media #{$bkp_mobile} {
    display: flex;
    flex-direction: column;
  }

  @media #{$bkp_tablet} {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
  }

  @media #{$bkp_desktop} {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
  }

  .input-dati-utente {
    @media #{$bkp_mobile} {
      width: 75vw;
    }
    @media #{$bkp_tablet} {
      width: calc((87vw - (64px * 2)) / 2);
    }
    @media #{$bkp_desktop} {
      width: calc((72vw - (72px * 2)) / 2);
    }
  }
}

.web1colSwapApp2col {
  @media #{$bkp_mobile} {
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
  }

  @media #{$bkp_tablet} {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
  }

  @media #{$bkp_desktop} {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
  }
}

.responsiveTwocolSBCenter {
  @media #{$bkp_mobile} {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  @media #{$bkp_tablet} {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    text-align: left;

    >*:last-child{
      margin-left: auto;
    }
  }

  @media #{$bkp_desktop} {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    text-align: left;

    >*:last-child{
      margin-left: auto;
    }
  }
}

.modalHeader::ng-deep {
  position: relative;
  text-align: center;
  .icon-Chiudi {
    font-size: 25px;
    position: absolute;
  }
  @media #{$bkp_mobile} {
    //padding: 16px;
    padding-bottom: 16px;
    .icon-Chiudi {
      bottom: 16px;
      left: 90%;
    }
  }

  @media #{$bkp_tablet} {
    // padding: 32px;
    padding-bottom: 32px;
    .icon-Chiudi {
      bottom: 32px;
      left: 92%;
    }
  }

  @media #{$bkp_desktop} {
    //padding: 32px;
    padding-bottom: 32px;
    .icon-Chiudi {
      bottom: 32px;
      left: 92%;
    }
  }
}

.modalHeaderBg::ng-deep {
  background-color: #e8f5ff;
  position: relative;
  text-align: center;
  .icon-Chiudi {
    font-size: 25px;
    position: absolute;
  }
  @media #{$bkp_mobile} {
    //padding: 16px;
    padding-bottom: 16px;
    .icon-Chiudi {
      bottom: 16px;
      left: 90%;
    }
  }

  @media #{$bkp_tablet} {
    // padding: 32px;
    padding-bottom: 32px;
    .icon-Chiudi {
      bottom: 32px;
      left: 92%;
    }
  }

  @media #{$bkp_desktop} {
    //padding: 32px;
    padding-bottom: 32px;
    .icon-Chiudi {
      bottom: 32px;
      left: 92%;
    }
  }
}

.responsive2RS1RL {
  display: flex;
  flex-direction: column;
  align-items: center;

  @media #{$bkp_desktop} {
    display: flex;
    flex-direction: row;
    align-items: baseline;
    gap: 0.3em;
  }
}

.inlineTop {
  display: flex;
  flex-direction: row;
  gap: 0.2em;
  margin-top: 16px;
}

.inlineAlignC::ng-deep {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 2%;
  margin-top: 2%;
}

.input-tooltip-container {
  display: flex;
  flex-direction: column;

  @media #{$bkp_mobile} {
    max-width: none;
  }

  @media #{$bkp_tablet} {
    max-width: 500px;
  }

  @media #{$bkp_desktop} {
    max-width: 550px;
  }
  margin-top: 10px;
}

.row-layout,
.col-layout {
  --gap-mobile: 0;
  --gap-tablet: 0;
  --gap-desktop: 0;
  --justify-content-m: "center";
  --align-items-m: "center";
  --justify-content-t: "center";
  --align-items-t: "center";
  --justify-content-d: "center";
  --align-items-d: "center";
  --align-content-d: "initial";
  --align-content-t: "initial";
  --align-content-m: "initial";
  --grid-template-columns-d: "none";
  --grid-template-columns-t: "none";
  --grid-template-columns-m: "none";

  @media #{$bkp_mobile} {
    gap: var(--gap-mobile);
    justify-content: var(--justify-content-m);
    align-content: var(--align-content-m);
    grid-template-columns: var(--grid-template-columns-m);
    align-items: var(--align-items-m);
  }

  @media #{$bkp_tablet} {
    gap: var(--gap-tablet);
    justify-content: var(--justify-content-t);
    align-content: var(--align-content-t);
    grid-template-columns: var(--grid-template-columns-t);
    align-items: var(--align-items-t);
  }

  @media #{$bkp_desktop} {
    gap: var(--gap-desktop);
    justify-content: var(--justify-content-d);
    align-content: var(--align-content-d);
    grid-template-columns: var(--grid-template-columns-d);
    align-items: var(--align-items-d);
  }
}

.row-layout {
  display: flex;
  flex-direction: row;

  @media #{$bkp_mobile} {
    align-items: var(--align-items-m);
  }

  @media #{$bkp_tablet} {
    align-items: var(--align-items-t);
  }

  @media #{$bkp_desktop} {
    align-items: var(--align-items-d);
  }
}

.col-layout {
  display: flex;
  flex-direction: column;
  align-items: stretch !important;
}

.tooltip-card {
  display: block;
  height: auto;
}

.InlineGridDouble{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 5px;
}

.InlineWithFirstElement{
  position: relative;
  >*:last-child{
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
  }
}
