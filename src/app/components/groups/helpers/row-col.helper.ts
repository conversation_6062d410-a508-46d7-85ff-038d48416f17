import { regexpCol, regexpRow, regexpRowSpace } from "../../../utils/regex";

export default class RowColHelper{
  public getRules(format: string): {
    alignment: string[],
    gap: string[],
    maxWidht: string[],
    type: string,
    gridTemplateColumns: string[],
    avaiableSpace: string[],
  } {
    let type;
    let regexp;

    let alignment;
    let gap;
    let maxWidht;
    let avaiableSpace;
    let colWidth;

    if (format.toLowerCase().startsWith('row')) {
      type = 'Row';
      regexp = new RegExp(regexpRow).test(format) ? new RegExp(regexpRow) : new RegExp(regexpRowSpace);
    } else {
      type = 'Col';
      regexp = new RegExp(regexpCol);
    }

    for (const match of format.matchAll(regexp)) {
      alignment =
        match.groups && match.groups['Alignment']
          ? match.groups['Alignment']
            .split(' ')
            .slice(1, match.groups['Alignment'].split(' ').length)
          : '';
      gap =
        match.groups && match.groups['Gap']
          ? match.groups['Gap']
            .split(' ')
            .slice(1, match.groups['Gap'].split(' ').length)
          : '';
      maxWidht =
        match.groups && match.groups['MWidth']
          ? match.groups['MWidth']
            .split(' ')
            .slice(1, match.groups['MWidth'].split(' ').length)
          : '';
      colWidth =
        match.groups && match.groups['ColWidth']
          ? match.groups['ColWidth']
            .split(' ')
            .slice(1, match.groups['ColWidth'].split(' ').length)
          : '';
      avaiableSpace =
        match.groups && match.groups['AvaiableSpace']
          ? match.groups['AvaiableSpace']
            .split(' ')
            .slice(1, match.groups['AvaiableSpace'].split(' ').length)
          : '';
    }

    return {
      alignment: alignment,
      gap: gap,
      maxWidht: maxWidht,
      type: type,
      gridTemplateColumns: colWidth,
      avaiableSpace: avaiableSpace,
    };
  }

  public setAlignment(container: HTMLDivElement, alignmentGroups: string[], layoutType: string): boolean {
    let spaceBetween = false;
    const index = alignmentGroups.length === 4 || alignmentGroups.length === 2 ? 1 : 0;

    for (let i = index; i < alignmentGroups.length; i++) {
      const taglia = i === 3 ? 'm' : i === 2 ? 't' : 'd';

      if (layoutType === 'Row' && container) {
        //Vertical Alignment: T-C-B
        switch (
          alignmentGroups[i].length === 3
            ? alignmentGroups[i].charAt(2)
            : alignmentGroups[i].charAt(1)
          ) {
          case 'T':
            container.style.setProperty(
              `--align-items-${taglia}`,
              'flex-start'
            );
            if (taglia === 'd') {
              container.style.setProperty(
                `--align-items-t`,
                'flex-start'
              );
              container.style.setProperty(
                `--align-items-m`,
                'flex-start'
              );
            }
            break;
          case 'C':
            container.style.setProperty(
              `--align-items-${taglia}`,
              'center'
            );
            if (taglia === 'd') {
              container.style.setProperty(
                `--align-items-t`,
                'center'
              );
              container.style.setProperty(
                `--align-items-m`,
                'center'
              );
            }
            break;
          case 'B':
            container.style.setProperty(
              `--align-items-${taglia}`,
              'flex-end'
            );
            if (taglia === 'd') {
              container.style.setProperty(
                `--align-items-t`,
                'flex-end'
              );
              container.style.setProperty(
                `--align-items-m`,
                'flex-end'
              );
            }
            break;
        }

        if (alignmentGroups[i].length === 3) {
          //Horizontal Alignment: SA-SB-SE
          switch (alignmentGroups[i].charAt(1)) {
            case 'A':
              container.style.setProperty(
                `--justify-content-${taglia}`,
                'space-around'
              );
              if (taglia === 'd') {
                container.style.setProperty(
                  `--justify-content-t`,
                  'space-around'
                );
                container.style.setProperty(
                  `--justify-content-m`,
                  'space-around'
                );
              }
              break;
            case 'B':
              container.style.setProperty(
                `--justify-content-${taglia}`,
                'space-between'
              );
              if (taglia === 'd') {
                container.style.setProperty(
                  `--justify-content-t`,
                  'space-between'
                );
                container.style.setProperty(
                  `--justify-content-m`,
                  'space-between'
                );
              }
              break;
            case 'E':
              container.style.setProperty(
                `--justify-content-${taglia}`,
                'space-evenly'
              );
              if (taglia === 'd') {
                container.style.setProperty(
                  `--justify-content-t`,
                  'space-evenly'
                );
                container.style.setProperty(
                  `--justify-content-m`,
                  'space-evenly'
                );
              }
              break;
          }
        } else if (alignmentGroups[i].length === 2) {
          //Horizontal Alignment: L-C-R
          switch (alignmentGroups[i].charAt(0)) {
            case 'L':
              container.style.setProperty(
                `--justify-content-${taglia}`,
                'flex-start'
              );
              if (taglia === 'd') {
                container.style.setProperty(
                  `--justify-content-t`,
                  'flex-start'
                );
                container.style.setProperty(
                  `--justify-content-m`,
                  'flex-start'
                );
              }
              break;
            case 'C':
              container.style.setProperty(
                `--justify-content-${taglia}`,
                'center'
              );
              if (taglia === 'd') {
                container.style.setProperty(
                  `--justify-content-t`,
                  'center'
                );
                container.style.setProperty(
                  `--justify-content-m`,
                  'center'
                );
              }
              break;
            case 'R':
              container.style.setProperty(
                `--justify-content-${taglia}`,
                'flex-end'
              );
              if (taglia === 'd') {
                container.style.setProperty(
                  `--justify-content-t`,
                  'flex-end'
                );
                container.style.setProperty(
                  `--justify-content-m`,
                  'flex-end'
                );
              }
              break;
          }
        }
      } else if (layoutType === 'Col' && container) {
        //Vertical Alignment: T-C-B
        switch (alignmentGroups[i].charAt(0)) {
          case 'T':
            container.style.setProperty(
              `--justify-content-${taglia}`,
              'flex-start'
            );
            if (taglia === 'd') {
              container.style.setProperty(
                `--justify-content-t`,
                'flex-start'
              );
              container.style.setProperty(
                `--justify-content-m`,
                'flex-start'
              );
            }
            break;
          case 'C':
            container.style.setProperty(
              `--justify-content-${taglia}`,
              'center'
            );
            if (taglia === 'd') {
              container.style.setProperty(
                `--justify-content-t`,
                'center'
              );
              container.style.setProperty(
                `--justify-content-m`,
                'center'
              );
            }
            break;
          case 'B':
            container.style.setProperty(
              `--justify-content-${taglia}`,
              'flex-end'
            );
            if (taglia === 'd') {
              container.style.setProperty(
                `--justify-content-t`,
                'flex-end'
              );
              container.style.setProperty(
                `--justify-content-m`,
                'flex-end'
              );
            }
            break;
        }
        //Horizontal Alignment
        const verticalIns = alignmentGroups[i].slice(1);
        switch (verticalIns) {
          case 'L':
            container.style.setProperty(
              `--align-items-${taglia}`,
              'flex-start'
            );
            if (taglia === 'd') {
              container.style.setProperty(
                `--align-items-t`,
                'flex-start'
              );
              container.style.setProperty(
                `--align-items-m`,
                'flex-start'
              );
            }
            break;
          case 'C':
            container.style.setProperty(
              `--align-items-${taglia}`,
              'center'
            );
            if (taglia === 'd') {
              container.style.setProperty(
                `--align-items-t`,
                'center'
              );
              container.style.setProperty(
                `--align-items-m`,
                'center'
              );
            }
            break;
          case 'R':
            container.style.setProperty(
              `--align-items-${taglia}`,
              'flex-end'
            );
            if (taglia === 'd') {
              container.style.setProperty(
                `--align-items-t`,
                'flex-end'
              );
              container.style.setProperty(
                `--align-items-m`,
                'flex-end'
              );
            }
            break;
          case 'SA':
            container.style.setProperty(
              `--align-content-${taglia}`,
              'space-around'
            );
            if (taglia === 'd') {
              container.style.setProperty(
                `--align-content-t`,
                'space-around'
              );
              container.style.setProperty(
                `--align-content-m`,
                'space-around'
              );
            }
            break;
          case 'SB':
            spaceBetween = true;
            container.style.setProperty(
              `--align-content-${taglia}`,
              'space-between'
            );
            if (taglia === 'd') {
              container.style.setProperty(
                `--align-content-t`,
                'space-between'
              );
              container.style.setProperty(
                `--align-content-m`,
                'space-between'
              );
            }
            break;
          case 'SE':
            container.style.setProperty(
              `--align-content-${taglia}`,
              'space-evenly'
            );
            if (taglia === 'd') {
              container.style.setProperty(
                `--align-content-t`,
                'space-evenly'
              );
              container.style.setProperty(
                `--align-content-m`,
                'space-evenly'
              );
            }
            break;
        }
      }
    }

    return spaceBetween;
  }

  public setColumnsWidth(container: HTMLDivElement, colWidth: string[]): void {
    container.style.display = 'grid';
    container.style.gridTemplateColumns = `${colWidth[0]}% ${colWidth[1]}%`;
  }

  public setAvaiableSpace(container: HTMLDivElement, spacing: string[]): void {
    const index = spacing.length === 4 || spacing.length === 2 ? 1 : 0;

    container.style.display = 'grid';
    for (let i = index; i < spacing.length; i++) {
      const taglia = i === 1 ? 'd' : i === 2 ? 't' : 'm';
      // Gestire AvaiableSpace:L -> Primo gruppo prende spazio disponibile R -> Secondo gruppo prende spazio disponibile
      switch (spacing[i]) {
        case 'L':
          container.style.setProperty(
            `grid-template-columns-${taglia}`,
            '1fr auto'
          );
          break;
        case 'R':
          container.style.setProperty(
            `grid-template-columns-${taglia}`,
            'auto 1fr'
          );
          break;
      }
    }
  }

  public setGap(container: HTMLDivElement, gapGroups: string[]): void {
    if (gapGroups.length === 4) {
      container.style.setProperty(
        '--gap-desktop',
        `${gapGroups[1]}px`
      );
      container.style.setProperty(
        '--gap-tablet',
        `${gapGroups[2]}px`
      );
      container.style.setProperty(
        '--gap-mobile',
        `${gapGroups[3]}px`
      );
    } else if (gapGroups.length === 2) {
      container.style.setProperty(
        '--gap-desktop',
        `${gapGroups[1]}px`
      );
      container.style.setProperty(
        '--gap-tablet',
        `${gapGroups[1]}px`
      );
      container.style.setProperty(
        '--gap-mobile',
        `${gapGroups[1]}px`
      );
    } else {
      container.style.setProperty(
        '--gap-desktop',
        `${gapGroups[0]}px`
      );
      container.style.setProperty(
        '--gap-tablet',
        `${gapGroups[0]}px`
      );
      container.style.setProperty(
        '--gap-mobile',
        `${gapGroups[0]}px`
      );
    }
  }

  public setMaxWidth(container: HTMLDivElement, width: string): void {
    if (width.length > 0 && width.includes('MW')) {
      container.style.maxWidth = `${width.substring(2)}px`;
      container.style.marginRight = 'auto';
      container.style.marginLeft = 'auto';
    }
  }
}
