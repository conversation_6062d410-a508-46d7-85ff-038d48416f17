import { regexpBox, regexpCard } from "../../../utils/regex";
import ColorsService from "../../../services/colors.service";

export default class CardBoxHelper{
  public getRules(format: string): {
    type: string,
    //CARD
    bgColorCard: string,
    borderRadiusCard: string[],
    setBorderCard: string[],
    paddingCard: string[],
    //BOX
    bgColorBox: string,
    paddingBox: string[],
  } {
    //CARD
    let regexp;
    let bgColorCard;
    let borderRadiusCard;
    let setBorderCard;
    let paddingCard;
    //BOX
    let bgColorBox;
    let paddingBox;
    let type: string;

    if (format.startsWith('Card')) {
      type = 'Card';
      regexp = new RegExp(regexpCard);
    } else if (format.startsWith('Box')) {
      type = 'Box';
      regexp = new RegExp(regexpBox);
    }

    for (const match of format.matchAll(regexp)) {
      //CARD
      bgColorCard =
        match.groups && match.groups['bgColor']
          ? match.groups['bgColor'].split(' ')
          : '';

      borderRadiusCard =
        match.groups && match.groups['borderRadius']
          ? match.groups['borderRadius']
            .split(' ')
            .slice(1, match.groups['borderRadius'].split(' ').length)
          : '';

      setBorderCard =
        match.groups && match.groups['border']
          ? match.groups['border'].split(' ')
          : '';

      paddingCard =
        match.groups && match.groups['padding']
          ? match.groups['padding']
            .split(' ')
            .slice(1, match.groups['padding'].split(' ').length)
          : '';

      //BOX
      bgColorBox =
        match.groups && match.groups['colorBox']
          ? match.groups['colorBox'].split(' ')
          : '';

      paddingBox =
        match.groups && match.groups['paddingBox']
          ? match.groups['paddingBox']
            .split(' ')
            .slice(1, match.groups['paddingBox'].split(' ').length)
          : '';
    }

    return {
      type,
      //CARD
      bgColorCard,
      borderRadiusCard,
      setBorderCard,
      paddingCard,
      //BOX
      bgColorBox,
      paddingBox,
    };
  }

  public setBgColorCard(container: HTMLDivElement, colorsService: ColorsService, color: string, customAttributes?: Record<string, string>): void {
    if (colorsService.getColor(`${color}`) !== '#9B9B9B') {
      container.style.backgroundColor = colorsService.getColor(`${color}`);
    } else {
      container.style.backgroundColor = `${color}`;
    }
    if (customAttributes?.productType) {
      switch (customAttributes?.productType.trim().toLowerCase()) {
        case 'auto':
          container.style.backgroundColor = '#0169B4';
          break;
        default:
          break;
      }
    }
  }

  public setBorderRadiusCard(container: HTMLDivElement, borderRadius: string[]): void {
    if (borderRadius.length === 1) {
      container.style.borderRadius = `${borderRadius[0]}px`;
    } else {
      container.style.borderRadius = `${borderRadius[1]}px`;
    }
  }

  public setBorderCard(container: HTMLDivElement, colorsService: ColorsService, borderCard: string[]): void {
    container.style.borderWidth = `${borderCard[0]}px`;
    container.style.borderColor =
      colorsService.getColor(`${borderCard[1]}`);
    container.style.borderStyle = `solid`;
  }

  public setPaddingCard(container: HTMLDivElement, padding: string[]): void {
    if (padding.length === 1) {
      container.style.padding = `${padding[0]}px`;
    } else {
      container.style.padding = `${padding[1]}px`;
    }
  }

  public setBgColorBox(container: HTMLDivElement, colorsService: ColorsService, color: string, customAttributes?: Record<string, string>): void {
    if (container) {
      if (colorsService.getColor(`${color}`) != '#9B9B9B') {
        container.style.backgroundColor = colorsService.getColor(`${color}`);
      } else {
        container.style.backgroundColor = `${color}`;
      }
      if (customAttributes && customAttributes.productType) {
        switch (customAttributes.productType.trim().toLocaleLowerCase()) {
          case 'auto':
            container.style.backgroundColor =
              '#0169B4';
            break;

          default:
            break;
        }
      }
    }
  }

  public setPaddingBox(container: HTMLDivElement, padding: string[]): void {
    if (padding.length === 1) {
      container.style.padding = `${padding[0]}px`;
    } else {
      container.style.padding = `${padding[1]}px`;
    }
  }
}
