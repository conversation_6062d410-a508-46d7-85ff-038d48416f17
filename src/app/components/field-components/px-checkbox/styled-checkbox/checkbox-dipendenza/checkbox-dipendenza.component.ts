import { Component, Input } from "@angular/core";
import { FormControl } from "@angular/forms";
import { IField } from "../../../../../models/models.commons";
import { TextHandlerService } from "../../../../../services/text-handler.service";
import { MessagesService } from "../../../../../services/messages.service";
import { EventsMethod } from "../../../../../utils/eventsMethod";
import { AnalyticsInterprete } from "../../../../../services/analytics.interprete.service";
import { EventsManaged } from "../../../../../utils/events.enum";
import { StatusService } from "../../../../../services/status.service";

@Component({
  templateUrl: './checkbox-dipendenza.component.html',
  styleUrls: ['./checkbox-dipenendza.component.scss'],
  selector: 'checkbox-dipendenza'
})
export default class CheckboxDipendenzaComponent extends EventsMethod{
  public formControl = new FormControl();

  public reference = '';
  public label = '';
  public currency = '';
  public checked = false;

  public constructor(
    private _statusService: StatusService,
    private _messageService: MessagesService,
    private _analyticsService: AnalyticsInterprete,
    private _textHandlerService: TextHandlerService
  ) {
    super(_messageService, _analyticsService);
  }

  @Input() set data(field: IField){
    if(field){
      this.field = field;
      this.reference = this.field.reference || '';
      this.label = field.control?.label || '';
      this.currency = this._textHandlerService.formattaValuta(field.customAttributes?.currency || '0');
      this.checked = field.value === 'true';
      this.formControl.setValue(this.checked);

      this.actionsSet = field.control.actionSets || [];

      this._statusService.addControlFormGroup(
        this.reference,
        this.formControl
      );
    }
  }

  public onClick(value: boolean){
    if(value !== this.checked){
      this.checked = value;
      this.formControl.setValue(value);
      this.onChange();

      this._messageService.onEventExecuted(
        this.actionsSet,
        EventsManaged.click,
        this.processManagement,
        this.gestioneProcesso
      );
    }
  }
}
