@import "../../../../../../styles.scss";

.CheckboxDipendenzaContainer{
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 16px;

  border: 2px solid $ivory;
  padding: 16px;

  cursor: pointer;

  &::ng-deep{
    *{
      color: $blue-primary !important;
    }
  }

  &:has(>input:checked){
    background-color: $blue-primary;
    border: 2px solid $blue-primary;
    &::ng-deep{
      *{
        color: white !important;
      }
    }
  }

  >input[type='checkbox']{
    display: none;
  }

  >.Checkmark{
    display: flex;
    align-items: center;
    justify-content: center;

    aspect-ratio: 1 / 1;
    border: 2px solid $blue-primary;
    border-radius: 4px;
    width: 24px;
    flex-shrink: 0;

    cursor: pointer;

    &::after{
      content: '';
      aspect-ratio: 4 / 2;
      border: 2px solid white;
      border-top: 0;
      border-right: 0;
      width: 30%;
      transform: rotateZ(-45deg);
    }
  }

  &:has(>input:checked){
    >.Checkmark{
      border: 0;
      background-color: $check-green-color;
    }
  }

  >.ZonaCurrency{
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    margin-left: auto;

    &::ng-deep{
      *{
        text-align: right !important;
      }
    }
  }
}
