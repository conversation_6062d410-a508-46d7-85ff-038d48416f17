import { Component, Input, OnDestroy } from '@angular/core';
import { FormControl, ValidationErrors, Validators } from '@angular/forms';
import {
  TextCss,
  TextHandlerService,
} from '../../../services/text-handler.service';
import { IActionSet, IControl, IField } from '../../../models/models.commons';
import { AnalyticsInterprete } from '../../../services/analytics.interprete.service';
import { MessagesService } from '../../../services/messages.service';
import { ListenerErroreFormInterface, StatusService } from "../../../services/status.service";
import { UtilsService } from '../../../services/utils.service';
import { EventsManaged } from '../../../utils/events.enum';
import { EventsMethod } from '../../../utils/eventsMethod';
import ColorsService from "../../../services/colors.service";

interface CheckboxComponentCustomAttributes {
  checkboxWithBorder?: string;
  checkboxWithBorderSize?: string;
  checkboxWithBorderColor?: string;
  ComponentPurpose?: string;
  ignoreLabelWeb?: string;
  labelPositionWeb?: string;
  Subtitle?: string;
  required?: string;
  style?: string;
  Style?: string;
}

@Component({
  selector: 'tpd-px-checkbox',
  templateUrl: './checkbox.component.html',
  styleUrls: ['./checkbox.component.scss'],
})
export class CheckboxComponent extends EventsMethod implements OnDestroy{
  constructor(
    private utilsService: UtilsService,
    public messageService: MessagesService,
    public analyticsService: AnalyticsInterprete,
    private statusService: StatusService,
    private textHandlerService: TextHandlerService,
    private colorsService: ColorsService
  ) {
    super(messageService, analyticsService);
    this._standAloneFormIndex = this.statusService.currentStandAloneFormIndex;
    this.errorListener = this.statusService.registraListenerDiErroreInputInvalido(this._standAloneFormIndex, statoErrore => this.mostraStatoErrore = statoErrore);
  }

  private _standAloneFormIndex = -1;

  public field: IField;
  public fieldControl = new FormControl(false, null);
  public errorListener: ListenerErroreFormInterface;

  public disabled = false;
  public readonly = false;
  public checked = false;
  public mostraStatoErrore = false;

  public checkboxWithBorder = false;
  public checkboxWithBorderSize = "1px";
  public checkboxWithBorderColor = "#ccc";

  public style = '';
  public ignoreLabelWeb = false;
  public labelPositionWeb = "right";

  public label = '';
  public checkLabel = '';
  public subtitle = '';
  public labelFormat = '';
  public labelCss: TextCss;

  public customAttributes: CheckboxComponentCustomAttributes;
  public control: IControl;
  public actionsSet: IActionSet[] = [];

  private _localSupportedTypes = [
    'Toggle',
    'Card'
  ]

  @Input() set data(input: { field: IField }) {
    this.field = input.field;
    if (this.field) {
      this.customAttributes = this.field.customAttributes;
      this.disabled = this.field.disabled || false;
      this.readonly = this.field.readOnly || false;

      if (this.field.labelFormat) {
        this.labelCss = this.textHandlerService.getTextCss(this.field.labelFormat);
        if (!this.labelCss)
          this.labelFormat = this.utilsService.getClassFromFormat(this.field.labelFormat);
      }

      this.style = this.customAttributes?.style || this.customAttributes?.Style || '';
      if(this.style.length === 0 || this._localSupportedTypes.includes(this.style)){
        this.control = this.field.control;
        this.checked = this.field.value === 'true';

        if(!this.readonly){
          this.label = this.utilsService.htmlDecode(this.field.label);
          this.fieldControl = new FormControl(this.checked);

          if (this.customAttributes) {
            this.ignoreLabelWeb = this.customAttributes.ignoreLabelWeb === 'true';
            this.subtitle = this.utilsService.htmlDecode(this.customAttributes.Subtitle);
            if (this.customAttributes.required === 'true')
              this.fieldControl.setValidators([Validators.required, this.checkTrue()]);

            this.checkboxWithBorder = this.customAttributes.checkboxWithBorder === 'true';
            if(this.customAttributes?.checkboxWithBorderSize)
              this.checkboxWithBorderSize = `${this.customAttributes.checkboxWithBorderSize}px`;
            if(this.customAttributes?.checkboxWithBorderColor)
              this.checkboxWithBorderColor = this.colorsService.getColor(this.customAttributes.checkboxWithBorderColor);
            if(this.customAttributes?.labelPositionWeb)
              this.labelPositionWeb = this.customAttributes.labelPositionWeb;
          }

          if (this.control) {
            this.actionsSet = this.control.actionSets || [];
            this.checkLabel = this.utilsService.htmlDecode(this.field?.control?.label || '');
          }

          if(this.disabled)
            this.fieldControl.disable();

          this.statusService.addControlFormGroup(
            this.field.reference ?? '',
            this.fieldControl
          );

          this.setValueProcessManagement();
          this.settaValoriGestioneProcesso();
        }
      }
    }
  }

  //@Override
  override onClick(value: boolean) {
    if (value !== this.fieldControl.value) {
      this.fieldControl.setValue(value);
      this.onChange();

      this.messageService.onEventExecuted(
        this.actionsSet,
        EventsManaged.click,
        this.processManagement,
        this.gestioneProcesso
      );
    }
  }

  /**
   * Funzione di validazione della checkbox
   */
  public checkTrue() {
    return (): ValidationErrors | null => {
      return this.fieldControl.value ? null : { errorCheckboxRequired: true };
    };
  }

  public ngOnDestroy(): void {
    this.errorListener && this.errorListener.rimuoviListenerDiErroreInputInvalido();
  }

  public get checkError(): boolean{
    return this.mostraStatoErrore && this.fieldControl.status === 'INVALID';
  }
}
