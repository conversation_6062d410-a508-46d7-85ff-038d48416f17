<ng-container *ngIf="this.field">
  <ng-container *ngIf="this.readonly">
  <span *ngIf="!this.labelCss" class="{{ this.labelFormat }}">
    {{this.checked ? "Sì" : "No"}}
  </span>
    <custom-text-style
      *ngIf="this.labelCss"
      [content]="this.checked ? 'Sì' : 'No'"
      [textCss]="this.labelCss">
    </custom-text-style>
  </ng-container>

  <ng-container *ngIf="!this.readonly" [ngSwitch]="this.style">
    <checkbox-dipendenza *ngSwitchCase="'DependencyCard'" [data]="this.field"></checkbox-dipendenza>
    <ng-container *ngSwitchDefault>
      <div [ngClass]="{ToggleContainerStyle: style === 'Toggle'}">
        <ng-container *ngIf="!this.ignoreLabelWeb && this.label">
          <span
            *ngIf="!this.labelCss"
            class="{{ this.labelFormat }} label-input" >
            {{this.label}}
          </span>
          <custom-text-style
            *ngIf="this.labelCss"
            [textCss]="this.labelCss"
            [content]="this.checkLabel">
          </custom-text-style>
        </ng-container>
        <label
          [for]="field.reference"
          class="CheckboxContainer"
          [ngClass]="{
            disable: this.disabled,
            switch: this.style == 'Toggle',
            checkboxWithBorder: this.checkboxWithBorder,
            positionRight: this.labelPositionWeb === 'right',
            positionLeft: this.labelPositionWeb === 'left'
          }"
          [ngStyle]="{
            'align-items': this.subtitle ? 'flex-start' : 'center',
            'border-color': this.checkboxWithBorderColor,
            'border-width': this.checkboxWithBorderSize
          }">
          <input
            type="checkbox"
            class="checkbox"
            [id]="this.field.reference"
            [name]="this.field.reference"
            [checked]="this.checked"
            [formControl]="this.fieldControl"
            (click)="onClick(!fieldControl.value)"/>
          <span
            *ngIf="this.style === 'Toggle'"
            class="slider round"></span>
          <span
            *ngIf="style !== 'Toggle'"
            [ngClass]="{
              'disabled-checkmark': this.disabled,
              'checkmark-disabled': this.disabled,
              'error-checkmark': this.checkError
            }"
            class="checkmark"
            [ngStyle]="{ 'margin-top': this.subtitle ? '0.2em' : 'none' }">
          </span>
          <div
            *ngIf="this.checkLabel || this.subtitle"
            [ngClass]="{ marginLabel: this.subtitle }">
            <span
              *ngIf="!this.labelCss && !this.ignoreLabelWeb && this.style !== 'Toggle'"
              class="{{ this.labelFormat }}"
              [ngStyle]="{ 'line-height': 1 }">
              {{ checkLabel }}
            </span>
            <div *ngIf="this.labelCss && !this.ignoreLabelWeb && this.style !== 'Toggle'">
              <custom-text-style
                [textCss]="this.labelCss"
                [content]="this.checkLabel"
              ></custom-text-style>
            </div>
            <p *ngIf="this.subtitle" class="Text-responsive-italic">
              {{ subtitle }}
            </p>
          </div>
        </label>
      </div>
    </ng-container>
  </ng-container>
</ng-container>
