@import "../../../../styles.scss";

//Stile del div container solo quando ci si trova in una modalità di visualizzazione Toggle
.ToggleContainerStyle {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

//Contenitore di tutta la logica del Checkbox
.CheckboxContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 16px;

  //Racchiude la checkbox all'interno di un bordo grigio, attivato con il CA checkboxWithBorder
  &.checkboxWithBorder {
    padding: 12px 16px;
    border: solid 1px $border-card-disabled;
  }

  &.positionLeft{
    flex-direction: row-reverse;
    justify-content: space-between;
  }
}

.disable {
  pointer-events: none;
  cursor: default;
}

.disabled-checkmark {
  cursor: default !important;
  opacity: 0.8;
  pointer-events: none;
}

.error-checkmark {
  border-color: $check-error-color !important;
}

label {
  max-width: none;
  margin: 0;
}

.row {
  //position: relative;
  flex-direction: row;
  gap: 16px;
  align-items: normal;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  > * {
    padding: 0;
    margin: 0;
  }
}

.checkbox {
  //position: absolute;
  display: none;
  margin: 0;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
  &:checked {
    & + .slider {
      background-color: $check-green-color;
      &:before {
        -webkit-transform: translateX(28px);
        -ms-transform: translateX(28px);
        transform: translateX(28px);
      }
      &:after {
        content: "";
        display: block;
        position: relative;
        left: 12px;
        top: 7px;
        width: 8px;
        height: 14px;
        border: solid #fff;
        border-width: 0 2px 2px 0;
        -webkit-transform: rotate(45deg);
        -ms-transform: rotate(45deg);
        transform: rotate(45deg);
      }
    }
  }
}
.checkmark {
  cursor: pointer;
  position: relative;
  // position: absolute;
  // top: 0.2rem;
  // left: 0;

  max-width: 24px;
  min-width: 24px;
  max-height: 24px;
  min-height: 24px;

  background-color: white;
  border: solid 0.2rem $main_color;
  padding-right: 1rem;
  //added for PU
  border-radius: 4px;
}

.checkmark-disabled {
  background-color: white;
  border-color: $check-disabled-color;

  &:has(input:checked){
    border-color: $check-green-disabled-color !important;
    background-color: $check-green-disabled-color !important;
  }
}

/* When the checkbox is checked, add a blue background */
.CheckboxContainer input:checked ~ .checkmark {
  background-color: $color-green;
  border-color: $color-green;
}
/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
  content: "";
  display: none;
}

/* Show the checkmark when checked */
.CheckboxContainer input:checked ~ .checkmark:after {
  display: block;
}

/* Style the checkmark/indicator */
.CheckboxContainer .checkmark:after {
  position: absolute;

  top: 50%;
  left: 50%;

  width: 5px;
  height: 10px;
  border: solid #fff;
  border-width: 0 2px 2px 0;
  -webkit-transform: translateX(-50%) translateY(-50%) rotate(45deg);
  -ms-transform: translateX(-50%) translateY(-50%) rotate(45deg);
  transform: translateX(-50%) translateY(-50%) rotate(45deg);
}

.switch {
  position: relative;
  display: inline-block;
  width: 56px;
  height: 32px;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  &.round {
    border-radius: 32px;
    &:before {
      position: absolute;
      content: "";
      height: 20px;
      width: 20px;
      left: 4px;
      bottom: 6px;
      background-color: white;
      -webkit-transition: 0.4s;
      transition: 0.4s;
      border-radius: 50%;
      box-shadow: 0 4px 8px rgba(26, 26, 26, 0.2);
    }
  }
}
