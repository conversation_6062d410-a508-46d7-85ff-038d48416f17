import { AbstractControl, ValidationErrors, ValidatorFn } from "@angular/forms";

export enum DateTimeFormat {
  'FormatoCustom',
  'Normale'
}

export const flipDateTimeValues = (value: string, dateFormat: string, modalitaOrdintata: boolean): string => {
  const dateTimeValues = getDateTimeValues(value, dateFormat, modalitaOrdintata);
  const orderedToken = findOrderDateTimeToken(dateFormat, !modalitaOrdintata);
  let newValue = "";
  for(const token of orderedToken)
    newValue += dateTimeValues.get(token);

  return newValue;
}

export const findDateTimeSeparators = (dateFormat: string): string[] => {
  const formatSeparators: string[] = [];
  const charToExclude = ['y', 'M', 'd', 'H', 'm', 's'];

  if (dateFormat) {
    let currentSeparator = '';
    for (let i = 0; i < dateFormat.length; i++) {
      if (!charToExclude.includes(dateFormat[i]))
        currentSeparator += dateFormat[i];
      else if (currentSeparator) {
        formatSeparators.push(currentSeparator);
        currentSeparator = '';
      }
    }
  }

  return formatSeparators.length > 0 ?formatSeparators : ['-'];
}

export const getDateTimeFormatWithoutSeparators = (dateFormat: string): string => {
  let esito = dateFormat;

  if(dateFormat){
    const separators = findDateTimeSeparators(dateFormat);
    esito = dateFormat.split(new RegExp(`${separators.join('|')}`)).join('');
  }

  return esito;
}

export const findOrderDateTimeToken = (dateFormat: string, modalitaOrdinata = false): string[] => {
  const esito: string[] = [];
  if(modalitaOrdinata){
    const priorityTokenArray = ['yyyy', 'yy', 'MM', 'dd', 'HH', 'mm', 'ss'];


    if (dateFormat) {
      for (const token of priorityTokenArray) {
        if (dateFormat.includes(token)){
          if(token === 'yy'){
            if(!esito.includes('yyyy'))
              esito.push(token);
          }else esito.push(token);
        }
      }
    }
  }else{
    const separators = findDateTimeSeparators(dateFormat);
    esito.push(...dateFormat.split(new RegExp(separators.join('|'))));
  }

  return esito;
}

export const getDateTimeValues = (value: string, dateFormat: string, modalitaOrdinata: boolean): Map<string, string> => {
  const esito = new Map<string, string>();

  if (value && dateFormat) {
    const priorityTokenArray = findOrderDateTimeToken(dateFormat, modalitaOrdinata);
    for (const token of priorityTokenArray) {
      const foundValue = token === 'yy' ? value.substring(2, token.length + 2) : value.substring(0, token.length);
      value = value.substring(token.length + (token === 'yy' ? 2 : 0));

      esito.set(token, foundValue);
    }
  }

  return esito;
}

export const composeDateTimeValues = (value: string, dateFormat: string, modalitaOrdinata = false): string => {
  let esito = '';

  if(value && dateFormat){
    const values = getDateTimeValues(value, dateFormat, modalitaOrdinata);
    if (values) {
      const separators = findDateTimeSeparators(dateFormat);
      let currentSeparatorIndex = 0;
      for (const token of dateFormat.split(new RegExp(`${separators.join('|')}`))) {
        if (values.has(token) && values.get(token).length > 0)
          esito += `${values.get(token)}${
            currentSeparatorIndex < separators.length
              ? separators[currentSeparatorIndex++]
              : ''
          }`;
      }

      let endWithSeparator: false | string = false;
      do{
        endWithSeparator = false;
        for(const separator of separators){
          if(esito.endsWith(separator)){
            endWithSeparator = separator;
            break;
          }
        }

        if(endWithSeparator)
          esito = esito.substring(0, esito.length - endWithSeparator.length);
      }while (endWithSeparator);
    }
  }

  return esito;
}

const tokenValidators: [string, (value: string) => boolean][] = [
  [
    'yyyy',
    (value) =>
      value.length === 4 &&
      !isNaN(parseInt(value))
  ],
  [
    'yy',
    (value) =>
      value.length === 2 &&
      !isNaN(parseInt(value))
  ],
  [
    'MM',
    (value) =>
      value.length === 2 &&
      !isNaN(parseInt(value)) &&
      parseInt(value) > 0 &&
      parseInt(value) < 13,
  ],
  [
    'dd',
    (value) =>
      value.length === 2 &&
      !isNaN(parseInt(value)) &&
      parseInt(value) > 0 &&
      parseInt(value) < 32,
  ],
  [
    'HH',
    (value) =>
      value.length === 2 &&
      !isNaN(parseInt(value)) &&
      parseInt(value) >= 0 &&
      parseInt(value) < 24,
  ],
  [
    'mm',
    (value) =>
      value.length === 2 &&
      !isNaN(parseInt(value)) &&
      parseInt(value) >= 0 &&
      parseInt(value) < 60,
  ],
  [
    'ss',
    (value) =>
      value.length === 2 &&
      !isNaN(parseInt(value)) &&
      parseInt(value) >= 0 &&
      parseInt(value) < 60,
  ],
];

export const formatDateTimeCheckerVaidator = (dateFormat: string): ValidatorFn => {
  return (control: AbstractControl): ValidationErrors | null => {
    let esito: ValidationErrors | null = null;

    if (control && dateFormat) {
      const value = control.value;
      const check = _validateDateTimeValue(value, dateFormat);
      if (check !== true) {
        esito = { dateTimeFormatError: check.join('-') };
      }
    }

    return esito;
  };
}

const _validateDateTimeValue = (value: string, dateFormat: string): true | string[] => {
  let errorFlag = false;
  const errors: string[] = [];
  if (dateFormat) {
    const dateFormatWithoutSeparators = getDateTimeFormatWithoutSeparators(dateFormat);
    if (value.length === dateFormatWithoutSeparators.length + (hasDataRidotta(dateFormat) ? 2 : 0)) {
      const values = getDateTimeValues(value, dateFormat, false);
      console.log("Val", values);
      const validators  = new Map(tokenValidators);
      for(const mappedValue of Array.from(values)){
        const token = mappedValue[0];
        const mValue = mappedValue[1];

        if(validators.has(token)){
          const controllo = validators.get(token)(mValue);
          if(!controllo) {
            errorFlag = true;
            errors.push(token);
          }
        }
      }
    } else {
      errorFlag = true;
      errors.push("lunghezza");
    }
  }

  const esito = errorFlag ? errors : true;
  console.log("errors", errors, esito);

  return esito;
}

export const hasDataRidotta = (dateFormat: string): boolean => {
  let esito = false;

  if(dateFormat && dateFormat.includes('yy') && !dateFormat.includes('yyyy'))
    esito = true;

  return esito;
}
