import { AfterViewInit, Component, Input, <PERSON><PERSON><PERSON>roy, ViewChild } from "@angular/core";
import { FormControl, ValidatorFn, Validators } from "@angular/forms";
import { TextCss, TextHandlerService } from "../../../services/text-handler.service";
import { IActionSet, IControl, IField } from "../../../models/models.commons";
import { AnalyticsInterprete } from "../../../services/analytics.interprete.service";
import { MessagesService } from "../../../services/messages.service";
import { StatusService } from "../../../services/status.service";
import { UtilsService } from "../../../services/utils.service";
import { EventsMethod } from "../../../utils/eventsMethod";
import { errors } from "../../../utils/errorMessage";
import {
  composeDateTimeValues,
  DateTimeFormat, findOrderDateTimeToken, flipDateTimeValues,
  formatDateTimeCheckerVaidator,
  getDateTimeFormatWithoutSeparators, getDateTimeValues
} from "./date-time.functions";
import { Helpers } from "@tpd-web-common-libs/nodejs-library";
import { DatepickerViaggiComponent } from "@tpd-web-angular-libs/angular-library";
import { DatePipe } from "@angular/common";

@Component({
  selector: 'tpd-date-time',
  templateUrl: './date-time.component.html',
  styleUrls: ['./date-time.component.scss'],
})
export class DateTimeComponent extends EventsMethod implements AfterViewInit, OnDestroy {
  @ViewChild("materialDatepickerViaggi", {read: DatepickerViaggiComponent}) materialDatePickerViaggi: DatepickerViaggiComponent;

  public field: IField;
  public originalValue: string;
  public control: IControl;
  public customAttributes: {[key: string]: string};
  public actionsSet: IActionSet[] = [];

  public fieldControl = new FormControl('', null);
  public visualizedValue = '';
  public fieldControlEnd = new FormControl('', null);

  public separatedVisualizedValue: [string, string][] = []

  public disabled = false;
  public readonly = false;

  public label: string;
  public labelFormat = '';
  public labelCss: TextCss;
  public placeholder = '';

  public dateFormat = '';
  public dateTimeFormat = DateTimeFormat;
  public format: DateTimeFormat = DateTimeFormat.Normale;

  public minDate: Date | undefined;
  public maxDate: Date | undefined;

  public noInputText = false;
  public closeWhenDatesSelected = false;
  public doubleDateSelection = false;
  public dataIniziale: Date | null = null;
  public dataFinale: Date | null = null;
  public range: string;
  public maxInterval: number;
  public yearSelectable = true;
  public dateReadOnly = '';

  public dateConverted: string;
  public showError = true;
  public iBrowser = false;

  constructor(
    private utilsService: UtilsService,
    public override messageService: MessagesService,
    public analyticsService: AnalyticsInterprete,
    private statusService: StatusService,
    private textHandlerService: TextHandlerService,
    private datePipe: DatePipe
  ) {
    super(messageService, analyticsService);
    this.iBrowser = Helpers.EnvironmentHelper.isClientSide();
  }

  public ngAfterViewInit(): void {
    if(this.materialDatePickerViaggi && this.customAttributes) {
      if(this.customAttributes.middlePeriodLabel && this.customAttributes.startPeriodLabel){
        const startPeriodLabel = this.customAttributes.startPeriodLabel;
        const middlePeriodLabel = this.customAttributes.middlePeriodLabel;

        this.materialDatePickerViaggi.setMultipleDateInputText = (dataIniziale, dataFinale) => {
          this.materialDatePickerViaggi.inputText =
            startPeriodLabel + " " +
            String(this.datePipe.transform(dataIniziale, "dd/MM/yyyy")) +
            " " + middlePeriodLabel + " " +
            String(this.datePipe.transform(dataFinale, "dd/MM/yyyy"));
        }
        this.materialDatePickerViaggi.setDate();

      }
    }
  }

  @Input() set data(input: { field: IField }) {
    this.field = input.field;
    this.control = this.field.control;
    this.fieldControl = new FormControl('', null);

    this.disabled = this.field.disabled;
    this.readonly = this.field.readOnly;

    this.label = this.utilsService.htmlDecode(this.field.label ?? '');
    const validators: ValidatorFn[] = [];

    this.placeholder = this.control?.modes[0]?.placeholder || '';
    if (this.field.labelFormat) {
      this.labelCss = this.textHandlerService.getTextCss(this.field.labelFormat);
      if(!this.labelCss)
        this.labelFormat = this.utilsService.getClassFromFormat(this.field.labelFormat);
    }

    this.customAttributes = this.field?.customAttributes;
    this.dateFormat = this.customAttributes?.dateFormat;
    this.format = this.dateFormat ? DateTimeFormat.FormatoCustom : DateTimeFormat.Normale;
    if(this.customAttributes?.startAvailableDate || this.customAttributes?.endAvailableDate || this.customAttributes?.showCalendar){
      this.format = DateTimeFormat.Normale
    }

    if(this.format === DateTimeFormat.FormatoCustom) {
      if (this.field.value) {
        this.field.value = flipDateTimeValues(this.field.value, this.dateFormat, true);
        if(this.readonly)
          this.dateReadOnly = composeDateTimeValues(this.field.value, this.dateFormat);
      }
    }


    if(!this.readonly && !this.disabled){
      this.originalValue = this.field.value;

      this.noInputText = this.customAttributes?.isExpanded === 'true';
      this.actionsSet = this.control?.actionSets || [];
      this.doubleDateSelection = this.customAttributes?.dataType === 'Range' && !!this.customAttributes?.dateToReference;

      // eslint-disable-next-line @typescript-eslint/unbound-method
      this.field.required &&validators.push(Validators.required);
      this.format === DateTimeFormat.FormatoCustom && validators.push(formatDateTimeCheckerVaidator(this.dateFormat));

      if(this.format === DateTimeFormat.FormatoCustom){
        const formatWithoutSeparators = getDateTimeFormatWithoutSeparators(this.dateFormat);
        this.statusService.aggiungiSubmitCallback(this.field.reference, (value: string) => {
          value = flipDateTimeValues(value, this.dateFormat, false);
          let coda = "";
          if(this.hasAnnoRidotto)
            coda = this.originalValue.substring(formatWithoutSeparators.length + 2);
          if(this.hasFormatoOreMinuti)
            coda = '00';
          return `${value}${coda}`;
        });
      }

      validators.length > 0 && this.fieldControl.setValidators(Validators.compose(validators));
      this.statusService.addControlFormGroup(this.field.reference, this.fieldControl);
    }else{
      this.fieldControl.disable();
    }

    if (!this.readonly && this.field.value) {
      // prevalorizzazione
      this.format === DateTimeFormat.Normale && this.prevalorizzazioneStandard();
      this.format === DateTimeFormat.FormatoCustom && this.prevalorizzazioneAlternativa();
    }

    if (this.customAttributes?.startAvailableDate) {
      let startDate;
      if (this.customAttributes?.startAvailableDate.includes('-')) {
        startDate = new Date(this.customAttributes?.startAvailableDate);
        this.minDate = new Date(startDate.setDate(startDate.getDate() - 1));
      } else {
        const [availabletYear, availableMonth, availableDay] = [
          this.customAttributes.startAvailableDate.slice(0, 4),
          this.customAttributes.startAvailableDate.slice(4, 6),
          this.customAttributes.startAvailableDate.slice(6),
        ];
        startDate = new Date(`${availabletYear}-${availableMonth}-${availableDay}`);
        this.minDate = new Date(startDate.setDate(startDate.getDate() - 1));
      }
      if (this.dataIniziale && this.minDate.getTime() > this.dataIniziale?.getTime())
        this.dataIniziale = null;
    }

    if (this.customAttributes?.endAvailableDate) {
      if (this.customAttributes?.endAvailableDate.includes('-'))
        this.maxDate = new Date(this.customAttributes?.endAvailableDate);
      else {
        const [availabletYear, availableMonth, availableDay] = [
          this.customAttributes.endAvailableDate.slice(0, 4),
          this.customAttributes.endAvailableDate.slice(4, 6),
          this.customAttributes.endAvailableDate.slice(6),
        ];
        this.maxDate = new Date(`${availabletYear}-${availableMonth}-${availableDay}`);
      }
      if (this.dataFinale && this.maxDate.getTime() < this.dataFinale?.getTime())
        this.dataFinale = null;
    }

    if (this.doubleDateSelection) {
      if (this.field.required)
        // eslint-disable-next-line @typescript-eslint/unbound-method
        this.fieldControlEnd.setValidators(Validators.required);
      if (this.customAttributes?.maxRangeLength)
        this.range = this.customAttributes?.maxRangeLength;

      this.statusService.addControlFormGroup(
        this.customAttributes.dateToReference,
        this.fieldControlEnd
      );
    }

    this.yearSelectable = !(this.customAttributes?.YearSelectable && this.customAttributes?.YearSelectable === 'false');

    this.setValueProcessManagement();
    this.settaValoriGestioneProcesso();
  }

  public ngOnInit() {
    if(!this.field?.customAttributes?.dateFormat){
      if (this.field.value.length >= 4 && this.field.value.length <= 8) {
        if (this.field.value.length == 8) {
          this.dateReadOnly = this.field.value.replace(
            /(\d{4})(\d{2})(\d{2})/,
            '$3/$2/$1'
          );
        } else if (this.field.value.length == 6) {
          this.dateReadOnly = this.field.value.replace(/(\d{4})(\d{2})/, '$2/$1');
        } else if (this.field.value.length == 4) {
          this.dateReadOnly = this.field.value;
        }
      }
    }
  }

  public multipleDatesEmitter(event: any) {
    /* se event ha un solo un elemento assunto come data iniziale (per rifacimento ng15)
    TODO: verificare se è così veramente [rb] */
    this.dataIniziale = Array.isArray(event) ? event[0] : event; //

    if (this.range) {
      this.maxDate = this.dateHandler();
    }

    this.dataFinale = Array.isArray(event) ? event[1] : undefined;
    this.closeWhenDatesSelected = !this.noInputText;

    if (this.dataIniziale) {
      this.fieldControl.setValue(this.dataIniziale.toLocaleDateString('it-IT'));
      if (!this.doubleDateSelection) {
        this.onChange();
      }
    }
    if (this.dataFinale) {
      this.fieldControlEnd.setValue(
        this.dataFinale?.toLocaleDateString('it-IT')
      );
      this.onChange();
    }
  }

  public dateHandler(): Date | undefined {
    if (this.range && this.dataIniziale) {
      this.maxInterval = parseInt(this.range);
      const d = new Date(this.dataIniziale);
      d.setHours(0, 0, 0, 0);
      d.setDate(d.getDate() + this.maxInterval);
      d.setSeconds(d.getSeconds() - 1);
      return d;
    }
    return undefined;
  }

  public prevalorizzazioneStandard() {
    try{
      let startYear = "0000";
      let startMonth = "00";
      let startDay = "00";
      this.dataIniziale = new Date();

      if(this.field?.value?.includes('/') || this.field?.value?.includes('-')){
        const separator = this.field?.value?.includes('/') ? '/' : '-';
        let values = this.field?.value.split(separator);
        if(values.length === 3){
          if(values[0].length === 2)
            values = values.reverse();
          startYear = values[0];
          startMonth = values[1];
          startDay = values[2];
        }
        this.dataIniziale = new Date(startYear + '-' + startMonth + '-' + startDay);
      }else if(!isNaN(parseInt(this.field.value))){
        [startYear, startMonth, startDay] = [
          this.field.value.slice(0, 4),
          this.field.value.slice(4, 6),
          this.field.value.slice(6),
        ];
        this.dataIniziale = new Date(startYear + '-' + startMonth + '-' + startDay);
      }

      this.dateConverted = this.dataIniziale.toLocaleDateString('it-IT');
      this.fieldControl.setValue(this.dateConverted);

      if (
        this.doubleDateSelection &&
        this.customAttributes?.dateToValue &&
        this.customAttributes?.dateToValue.length > 0
      ) {
        const [endYear, endMonth, endDay] = [
          this.customAttributes.dateToValue.slice(0, 4),
          this.customAttributes.dateToValue.slice(4, 6),
          this.customAttributes.dateToValue.slice(6),
        ];
        this.dataFinale = new Date(`${endYear}-${endMonth}-${endDay}`);
        this.fieldControlEnd.setValue(
          this.dataFinale.toLocaleDateString('it-IT')
        );
      }
    }catch(e){
      console.error("Errore durante la prevalorizzazione standard", e);
    }
  }

  /**
   * Accoda un nuovo carattere al campo
   * @param char
   */
  private _appendChar(char: string) {
    if (this.customAttributes && this.customAttributes.dateFormat) {
      if (!isNaN(parseInt(char))) {
        const value = `${this.fieldControl.value}${char}`;
        const targetVisualizedValue = composeDateTimeValues(value, this.dateFormat);
        const formatWithoutSeparators = getDateTimeFormatWithoutSeparators(this.dateFormat);

        if(value.length <= formatWithoutSeparators.length + (this.hasAnnoRidotto ? 2 : 0)){
          this.fieldControl.setValue(value);
          this.visualizedValue = targetVisualizedValue;

          if(value.includes("2400") && targetVisualizedValue.includes('24:00'))
            this.fieldControl.setValue(value.replace("2400", "0000"));
        }
      }
    }
  }

  private _deleteCharacter() {
    let value = this.fieldControl.value;
    if(this.visualizedValue.length > 0) {
      value = value.substring(0, value.length - 1);
      this.fieldControl.setValue(value);
    }
    this.visualizedValue = composeDateTimeValues(value, this.dateFormat);
  }

  public keyPressFunction($event: KeyboardEvent) {
    $event.preventDefault();
    if (!this.disabled && !this.readonly) {
      ($event.target as HTMLInputElement).setSelectionRange(this.visualizedValue.length, this.visualizedValue.length);
      switch ($event.key) {
        case 'Backspace': {
          this._deleteCharacter();
          break;
        }
        default: {
          this._appendChar($event.key);
          break;
        }
      }
    }
  }

  public clickFunction($event: MouseEvent) {
    ($event.target as HTMLInputElement).setSelectionRange(this.visualizedValue.length, this.visualizedValue.length);
  }

  public prevalorizzazioneAlternativa() {
    if ( this.dateFormat ) {
      this.visualizedValue = composeDateTimeValues(this.field.value, this.dateFormat);
      const formatWithoutSeparators = getDateTimeFormatWithoutSeparators(this.dateFormat);
      const newValue = this.field.value.substring(0, formatWithoutSeparators.length + (this.hasAnnoRidotto ? 2 : 0) );
      this.fieldControl.setValue(newValue);
    }
  }

  /**
   * Genera la stringa identificativa per il messaggio di errore
   */
  public errorMessage(): string {
    const literal: [string, [string, string]][] = [
      ['yyyy', ['anno', "dell'"]],
      ['yy', ['anno', "dell'"]],
      ['MM', ['mese', 'del ']],
      ['dd', ['giorno', 'del ']],
      ['HH', ['ore', 'delle ']],
      ['mm', ['minuti', 'dei ']],
      ['ss', ['secondi', 'dei ']],
    ];

    let esito = '';

    if (this.field && this.fieldControl) {
      if (this.fieldControl.hasError('dateTimeFormatError')) {
        const errorType = this.fieldControl.getError(
          'dateTimeFormatError'
        ) as string;
        if (errorType === 'lunghezza') esito = errors.microchip;
        else {
          let first = true;
          esito = 'Il formato ';
          const literalMap = new Map(literal);
          const errors = errorType.split('-');
          for (let i = 0; i < errors.length; i++) {
            if (first) {
              esito += literalMap.get(errors[i])[1];
              first = false;
            }
            esito += literalMap.get(errors[i])[0];
            if (errors.length !== 1) {
              if (i < errors.length - 2) esito += ', ';
              else if (i < errors.length - 1) esito += ' e ';
            }
          }
          esito += ' è errato.';
        }
      }
    }

    return esito;
  }

  public ngOnDestroy() {
    this.statusService.removeControlFormGroup(this.field.reference);
    this.doubleDateSelection && this.statusService.removeControlFormGroup(this.customAttributes?.dateToReference);
  }

  public get hasAnnoRidotto(): boolean{
    let esito = false;

    if(this.dateFormat.includes('yy') && !this.dateFormat.includes('yyyy'))
      esito = true;

    return esito;
  }

  public get hasFormatoOreMinuti(): boolean{
    let esito = false;

    if(this.dateFormat.includes('HH') && this.dateFormat.includes('mm') && !this.dateFormat.includes('ss'))
      esito = true;

    return esito;
  }

  public get fieldControlValue(): string{
    let esito = this.visualizedValue || "";
    if(esito.includes('00:00'))
      esito = esito.replace('00:00', '24:00');
    return esito
  }
}
