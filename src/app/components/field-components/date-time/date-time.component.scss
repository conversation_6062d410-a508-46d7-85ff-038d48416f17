@import "../../../../styles.scss";
.layout-input-date {
  @media #{$bkp_mobile} {
    width: 90vw;
  }

  @media #{$bkp_tablet} {
    width: 500px;
  }

  @media #{$bkp_desktop} {
    width: 550px;
  }
}
.date-picker-container {
  @media #{$bkp_mobile} {
    max-width: none;
  }

  @media #{$bkp_tablet} {
    max-width: 500px;
  }

  @media #{$bkp_desktop} {
    max-width: 550px;
  }
}

.inputFieldClass {
  --color: #{$middle-grey-pu};
  --borderColor: #{$main_color};
  --backgroundColor: white;

  width: 100%;
  height: 48px;
  border-radius: 0;

  padding: 12px 16px 12px 16px;

  background-color: var(--backgroundColor);
  border: solid 1px var(--borderColor);
  color: var(--color);

  &:disabled {
    --color: #{$grey};
    --borderColor: #{$ivory};
    --backgroundColor: #{$ivory};
    cursor: not-allowed;
    pointer-events: none;
  }
}
