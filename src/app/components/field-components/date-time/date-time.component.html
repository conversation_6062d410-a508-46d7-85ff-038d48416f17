<div style="display: flex; flex-direction: column" *ngIf="this.field && !this.readonly">
  <div *ngIf="!this.labelCss" class="{{ labelFormat }} mrg-label-input">
    {{ label }}
  </div>
  <custom-text-style
    *ngIf="!!this.labelCss"
    class="mrg-label-input"
    [content]="this.label"
    [textCss]="this.labelCss">
  </custom-text-style>
  <div class="date-picker-container" *ngIf="!this.readonly && iBrowser">
    <material-datepicker-viaggi
      #materialDatepickerViaggi
      *ngIf="this.format === this.dateTimeFormat.Normale"
      [dataIniziale]="this.dataIniziale"
      [dataFinale]="this.dataFinale"
      [noInputText]="this.noInputText"
      [placeholder]="this.placeholder"
      [doubleDateSelection]="this.doubleDateSelection"
      [minDate]="this.minDate"
      [maxDate]="this.maxDate"
      [dateFormat]="'DD-MM-YYYY'"
      [closeWhenDatesSelected]="this.closeWhenDatesSelected"
      [yearSelectable]="this.yearSelectable"
      [emitOnInputClick]="false"
      [disabled]="this.disabled"
      (multipleDatesEmitter)="this.multipleDatesEmitter($event)">
    </material-datepicker-viaggi>
    <input
      *ngIf="this.format === this.dateTimeFormat.FormatoCustom"
      type="text"
      class="inputFieldClass"
      [disabled]="this.disabled"
      [readOnly]="this.readonly"
      [value]="this.fieldControlValue"
      [placeholder]="this.placeholder"
      (keypress)="$event.preventDefault()"
      (keydown)="this.keyPressFunction($event)"
      (keyup)="$event.preventDefault()"
      (click)="this.clickFunction($event)"
    />
    <div [hidden]="!showError" *ngIf="errorMessage() && fieldControl.invalid">
      <i style="color: #ff001f;" class="icon-Attenzione-pieno bd-icona"></i>
      <span class="testo-non-valido">{{ this.errorMessage() }}</span>
    </div>
  </div>
</div>

<div
  class="date-picker-container"
  *ngIf="this.readonly && this.labelCss">
  <custom-text-style [textCss]="this.labelCss" [content]="this.dateReadOnly">
  </custom-text-style>
</div>
