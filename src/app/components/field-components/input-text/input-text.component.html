<div *ngIf="field" class="layout-input">
  <ng-container [ngSwitch]="controlFormat">
    <ng-container *ngSwitchCase="'OTPSplittedBox'">
      <div class="splittedBox">
        <div
          *ngIf="formValidazione && formValidazione.controls"
          class="inputContainer"
        >
          <input
            *ngFor="let control of formValidazione.controls; index as i"
            class="otpInput"
            [attr.id]="'Input_' + i"
            type="number"
            min="0"
            max="9"
            step="1"
            (keyup)="changeInput(control, i, $event)"
            [formControl]="control | formControl"
            inputmode="numeric"
          />
        </div>

        <div
          *ngIf="
            customAttributes?.outcomeValidation === 'KO' &&
            showOutcomeValidation
          "
          class="w-label"
        >
          <i class="icon-Attenzione-pieno bd-icona"></i>
          <span class="testo-non-valido">{{ outcomeMessageKO }}</span>
        </div>

        <div *ngIf="fieldControl.invalid && errorMessage()" class="w-label">
          <i class="icon-Attenzione-pieno bd-icona"></i>
          <span class="testo-non-valido">{{ errorMessage() }}</span>
        </div>

        <div class="w-label Text">
          <div *ngIf="maxAttempts">Tentativi rimasti {{ numTentativi }}</div>
          <div id="countdown"></div>
        </div>
      </div>
    </ng-container>

    <ng-container *ngSwitchDefault>
      <div
        class="{{ labelFormat }} mrg-label-input"
        *ngIf="((field.labelReserveSpace && !label) || label) && !setCss"
      >
        {{ label }}
      </div>
      <!--Se arriva lo stile da PEGA-->
      <div
        class="mrg-label-input"
        *ngIf="
          ((field.labelReserveSpace && !label) || label) &&
          setCss &&
          this.labelCss
        "
      >
        <custom-text-style
          [textCss]="this.labelCss"
          [content]="this.label"
        ></custom-text-style>
      </div>
      <input
        #textInput
        *ngIf="!field.readOnly"
        class="input-dati-utente"
        style="padding: 12px 16px 12px 16px"
        [id]="id"
        type="text"
        [placeholder]="placeholder"
        [required]="required"
        [value]="this.fieldValue"
        [formControl]="this.hasDateFormat && this.disabled ? undefined : fieldControl"
        [ngClass]="{
          'disabled-input-box': disabled,
          'invalid-input': fieldControl.touched && fieldControl.invalid && this.showError
        }"
        (blur)="onBlur($event)"
        (change)="onChange($event)"
        (focus)="onFocus($event)"
        (click)="onClick($event)"
      />
      <div *ngIf="this.field.readOnly && !this.setCss">
        <div>{{ this.fieldValue }}</div>
      </div>
      <custom-text-style
        *ngIf="this.field.readOnly && this.setCss"
        [content]="this.fieldValue"
        [textCss]="this.labelCss">
      </custom-text-style>
      <div
        [hidden]="!showError"
        *ngIf="
          fieldControl.touched &&
          fieldControl.invalid &&
          errorMessage() &&
          !disabled &&
          !field.readOnly
        "
      >
        <i class="icon-Attenzione-pieno bd-icona"></i>
        <span class="testo-non-valido">{{ errorMessage() }}</span>
      </div>
    </ng-container>
  </ng-container>
</div>
