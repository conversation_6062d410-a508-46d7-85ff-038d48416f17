import { Component, Input, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  AbstractControl,
  AsyncValidatorFn,
  FormArray,
  FormControl,
  FormGroup,
  ValidationErrors,
  Validators,
} from '@angular/forms';

import { addError, removeError, validation } from '../../../utils/regex';

import { Observable, Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  IActionSet,
  IControl,
  IField,
  IMode,
} from '../../../models/models.commons';
import { AnalyticsInterprete } from '../../../services/analytics.interprete.service';
import { MessagesService } from '../../../services/messages.service';
import { ListenerErroreFormInterface, StatusService } from "../../../services/status.service";
import { letterNumberCf, UtilsService } from '../../../services/utils.service';
import { errors } from '../../../utils/errorMessage';
import { TpdUtils } from '@tpd-web-angular-libs/angular-library';
import {
  TextCss,
  TextHandlerService,
} from '../../../services/text-handler.service';
import { EventsManaged } from '../../../utils/events.enum';
import { EventsMethod } from '../../../utils/eventsMethod';

interface RequiredCustomAttributes {
  travelerIndex: string;
  taxCode: string;
  referenceCF: string;
  validation: string;
  maxAge: string;
  compareDate: string;
  otherTaxCodeReference: string;
  required: string;
  Required: string;
  StartTimer: string;
  MaxAttempts: string;
  outcomeValidation: '' | 'OK' | 'KO';
  nameReference: string;
  surnameReference: string;
  disabled: string;
  memorizzaCFContraente: string;
  memorizzaNomeContraente: string;
  memorizzaCognomeContraente: string;
  memorizzaEmailContraente: string;
  memorizzaCellulareContraente: string;
  dateFormat: string;
  alwaysSendReference: string;  //Usare questo quando sarà necessario e fare il refactoring per inviare comunque il reference
}

@Component({
  selector: 'tpd-input-text',
  templateUrl: './input-text.component.html',
  styleUrls: ['./input-text.component.scss'],
})
export class InputTextComponent extends EventsMethod implements OnInit, OnDestroy {
  private _standAloneFormIndex = -1;

  constructor(
    private utilsService: UtilsService,
    public override messageService: MessagesService,
    public analyticsService: AnalyticsInterprete,
    private statusService: StatusService,
    private textHandlerService: TextHandlerService
  ) {
    super(messageService, analyticsService);
    this._standAloneFormIndex = this.statusService.currentStandAloneFormIndex;
  }

  public override field: IField | undefined = undefined;
  public fg: FormGroup | undefined = undefined;
  public control: IControl | undefined = undefined;
  public customAttributes: RequiredCustomAttributes | undefined = undefined;
  public formValidazione: FormArray | undefined = undefined;

  public fieldControl = new FormControl('', null);
  public label = '';
  public labelFormat = '';
  public labelCss?: TextCss | null;
  public setCss = false;
  public override actionsSet: IActionSet[] = [];
  public disabled = false;
  public placeholder = '';
  public formatType = '';
  public showLabel = false;
  public controlFormat = '';
  public maxChars: number | false = false;
  public minChars: number | false = false;
  public startTimer: string | false = false;
  public maxAttempts: string | false = false;
  public numTentativi: number | false = false;
  public errorOTP: string | false = false;
  public outcomeMessageKO = errors['outcomeValidationKO'];
  public showOutcomeValidation = false;
  public referenceCF: string | false = false;
  public referencesToCompare: { nome?: string; cognome?: string; cf?: string } = {};
  public required = false;
  public id = '';
  public viaSubscription!: Subscription;
  public maxAge: string | false = false;
  public compareDate: string | false = false;
  public otherTaxCodeReference: string | false = false;

  private formValidazioneChangeSub: Subscription | undefined = undefined;
  private timerSubscription: Subscription | undefined = undefined;
  private checkCFSubscription: Subscription | undefined = undefined;
  private doubletCFSubscription: Subscription | undefined = undefined;
  private nameCFSubscription: Subscription | undefined = undefined;
  private elseIFSubscription: Subscription | undefined = undefined;
  private errorListener: ListenerErroreFormInterface;
  public showError = true;

  public form = this.statusService.getFormGroup();

  @Input() set data(input: { field: IField }) {
    //Array dei validatori da inserire all'interno dell'array
    const asyncValidators: any[] = []; //Validatori asincroni da aggiungere al field
    const validators = []; //Validatori da aggiungere al field

    if (input && input.field) {
      this.field = input.field;
      this.statusService.setReferencesContraente(this.field);
      this.required = this.field.required ?? false;

      if (this.field.value)
        this.fieldControl = new FormControl(this.field.value);

      this.control = this.field.control;

      this.label = this.field.label
        ? this.utilsService.htmlDecode(this.field.label)
        : '';
      this.labelFormat = this.field.labelFormat
        ? this.utilsService.getClassFromFormat(this.field.labelFormat)
        : '';

      if (this.field.labelFormat) {
        this.labelFormat = this.utilsService.getClassFromFormat(
          this.field.labelFormat
        );
        this.labelCss = this.textHandlerService.getTextCss(
          this.field.labelFormat
        );

        this.setCss = !!this.labelCss;
      } else {
        this.labelFormat = '';
      }

      this.disabled =
        (this.field.disabled ||
          (this.customAttributes &&
            this.customAttributes.disabled === 'true')) ??
        false;

      //Casistica in cui il field non è di sola lettura
      if (
        !this.field.readOnly &&
        this.field.control &&
        this.field.control.modes
      ) {
        const modes = this.field.control.modes.find(
          (item) => item.modeType === 'editable'
        );
        if (modes) {
          this.controlFormat = modes.controlFormat ?? '';
          this.maxChars = Number(modes.maxChars);
          this.minChars = Number(modes.minChars);
        }
      }

      /*Sezione di analisi e controllo dei mode*/
      if (this.field.control && this.field.control.modes) {
        const modes: IMode[] = this.field.control.modes;
        modes.forEach((mode) => {
          //Controllo sui mode
          if (!this.placeholder && mode.placeholder)
            this.placeholder = mode.placeholder;
        });
      }

      //Gestione sui custom attributes
      this.customAttributes = this.field
        .customAttributes as RequiredCustomAttributes;
      if (this.customAttributes) {
        this.referenceCF = this.customAttributes.referenceCF;
        this.maxAge = this.customAttributes.maxAge;
        this.compareDate = this.customAttributes.compareDate;
        this.otherTaxCodeReference =
          this.customAttributes.otherTaxCodeReference;

        //Il required può essere passato anche attraverso i customAttributes
        if (!this.required)
          this.required =
            this.customAttributes.required === 'true' ||
            this.customAttributes.Required === 'true';
        //-------------------------------------------------------------------

        const regexValidators =
          validation[
            this.customAttributes.validation as keyof typeof validation
          ];
        if (regexValidators)
          validators.push(Validators.pattern(regexValidators));

        //Gestione dell' One Time Password
        if (this.controlFormat === 'OTPSplittedBox') {
          this.startTimer = this.customAttributes.StartTimer;
          this.maxAttempts = this.customAttributes.MaxAttempts;
          const outcomeValidation = this.customAttributes.outcomeValidation;

          this.showOutcomeValidation = true;
          setTimeout(() => {
            this.showOutcomeValidation = false;
          }, 2000);

          this.buildFormValidation();
          if (this.formValidazione) {
            this.formValidazioneChangeSub =
              this.formValidazione.valueChanges.subscribe((e) => {
                if (e) {
                  const otp = this.formValidazione
                    ? this.formValidazione.value.join('')
                    : '';
                  this.fieldControl.setValue(otp);
                }
              });
          }

          this.statusService.failedValidationOTP(outcomeValidation);
          this.numTentativi = this.statusService.getNumberFailed(
            parseInt(this.maxAttempts)
          );

          if (this.startTimer) this.statusService.setTimerOTP(this.startTimer);
        }

        //--//

        const taxCode = this.customAttributes.taxCode as 'name' | 'surname';
        if (this.customAttributes.validation === 'nameWithCF') {
          if (taxCode) validators.push(this.checkCF('name', taxCode));
          else if (this.referenceCF) this.checkCFEvent('name', 'errorNameCF');
        }

        /*--*/
        if (this.customAttributes.validation === 'surnameWithCF') {
          if (taxCode) validators.push(this.checkCF('surname', taxCode));
          else if (this.referenceCF)
            this.checkCFEvent('surname', 'errorSurnameCF');
        }

        /*--*/
        if (this.customAttributes.validation === 'taxCodeTravel') {
          // controllo CIN
          validators.push(this.checkLetterCalculation());

          if (this.otherTaxCodeReference) {
            // controllo su codice fiscale duplicato
            this.checkDoubletCF(this.otherTaxCodeReference);
          }

          if (this.maxAge && this.compareDate) {
            // controllo su codice fiscale overAge(80)
            validators.push(
              this.cfDataNascitaOverAgeValidator(this.maxAge, this.compareDate)
            );
          }

          /*--*/
          const index = this.customAttributes.travelerIndex;
          if (
            this.customAttributes.nameReference &&
            this.customAttributes.surnameReference &&
            index
          ) {
            //controllo su nome e cognome
            const splittedNameRef =
              this.customAttributes.nameReference.split('.');
            this.referencesToCompare.nome = `${splittedNameRef[0]}.${splittedNameRef[1]}(${index}).${splittedNameRef[2]}`;

            const splittedSurnameRef =
              this.customAttributes.surnameReference.split('.');
            this.referencesToCompare.cognome = `${splittedSurnameRef[0]}.${splittedSurnameRef[1]}(${index}).${splittedSurnameRef[2]}`;

            //validators.push(this.CfWithNameESurname(this.referencesToCompare));
            this.CfWithNameESurname(this.referencesToCompare.nome, 'name');
            this.CfWithNameESurname(
              this.referencesToCompare.cognome,
              'surname'
            );
          }
        }

        /*--*/
        if (
          this.customAttributes.validation === 'taxCode' &&
          this.customAttributes.nameReference &&
          this.customAttributes.surnameReference
        ) {
          //controllo su nome e cognome
          this.referencesToCompare.nome = this.customAttributes.nameReference;
          this.referencesToCompare.cognome =
            this.customAttributes.surnameReference;

          this.CfWithNameESurname(this.referencesToCompare.nome, 'name');
          this.CfWithNameESurname(this.referencesToCompare.cognome, 'surname');
        }
      }

      if (this.required) {
        // eslint-disable-next-line @typescript-eslint/unbound-method
        validators.push(Validators.required);
        this.showError = false;
      }

      if (this.minChars) {
        validators.push(Validators.minLength(Number(this.minChars)));
      }

      if (this.maxChars) {
        validators.push(Validators.maxLength(Number(this.maxChars)));
      }

      if (this.controlFormat === 'OTPSplittedBox') {
        if (this.startTimer)
          this.fieldControl.setAsyncValidators(this.timerValidator());
        if (this.maxAttempts) validators.push(this.maxFailValidator());
      }

      if (validators.length > 0) {
        this.fieldControl.setValidators(Validators.compose(validators));
      }

      if (asyncValidators.length > 0)
        this.fieldControl.setAsyncValidators(asyncValidators);

      //Casistica in cui l'input text è di sola lettura
      if (this.field.readOnly) {
        if (this.field.value) {
          const sVal = this.utilsService.htmlDecode(this.field.value);
          this.fieldControl.setValue(sVal);
        }

        if (this.field.disabled) this.fieldControl.disable();

        if (this.field.control && this.field.control.modes) {
          const modes1 = this.field.control.modes.find(
            (element) => element.modeType === 'readOnly'
          );
          if (modes1 && modes1.formatType) this.formatType = modes1.formatType;
        }
      }

      if (this.control && this.control.actionSets)
        this.actionsSet = this.control.actionSets;

      if (this.field.reference) {
        this.id = this.field.reference.split('.')[2];
        if (this.field.reference === this.utilsService.civicoAssente) {
          this.fieldControl.markAsTouched();
          this.field.value = '';
          this.utilsService.civicoAssente = '';
          setTimeout(() => {
            if (typeof document === 'object') {
              document.getElementById(`${this.id}`)?.focus();
            }
          }, 500);
        }
      }

      this.viaSubscription = this.utilsService.viaAssente.subscribe((value) => {
        if (this.field && this.field.reference === value) {
          this.fieldControl.markAsPristine();
          this.fieldControl.markAsUntouched();
          this.utilsService.setReference('');
        }
      });

      // prevalorizzazione
      if (this.field.value)
        this.fieldControl.setValue(
          this.utilsService.htmlDecode(this.field.value)
        );

      // istanza controller text-input
      if (this.field.reference && this.fieldControl)
        this.statusService.addControlFormGroup(
          this.field.reference,
          this.fieldControl
        );

      this.setValueProcessManagement();
      this.settaValoriGestioneProcesso();
    }

    if (
      this.field?.customAttributes?.requiredIF &&
      this.field?.customAttributes?.requiredIF == 'true'
    ) {
      this.requiredIF();
    }
  }

  public ngOnInit(): void {
    this.errorListener = this.statusService.registraListenerDiErroreInputInvalido(this._standAloneFormIndex, statoErrore => this.showError = statoErrore);
  }

  /**
   * Controlla sulla base dell'errore generato sul field contro quale messaggio visualizzare all'utente
   * @returns Restituisce Una stringa indicante il messaggio di errore da visualizzare
   */
  errorMessage(): string {
    let message = '';

    if (this.field && this.fieldControl.hasError('pattern')) {
      message =
        errors[this.field.customAttributes.validation as keyof typeof errors];
    }

    if (
      this.field &&
      this.field.control &&
      this.field.control.modes &&
      this.field.control.modes[0] &&
      this.fieldControl.hasError('minlength') &&
      this.controlFormat !== 'OTPSplittedBox'
    ) {
      message = `${errors.minChars} ${this.field.control.modes[0].minChars}`;
    }

    if (
      this.field &&
      this.field.control &&
      this.field.control.modes &&
      this.field.control.modes[0] &&
      this.fieldControl.hasError('maxlength') &&
      this.controlFormat !== 'OTPSplittedBox'
    ) {
      message = `${errors.maxChars} ${this.field.control.modes[0].maxChars}`;
    }

    if (this.fieldControl.hasError('errorNameCF')) {
      message = errors['errorNameCF'];
    }

    if (this.fieldControl.hasError('errorSurnameCF')) {
      message = errors['errorSurnameCF'];
    }

    if (this.fieldControl.hasError('errorTimeOut')) {
      message = errors['errorTimeOut'];
    }

    if (this.fieldControl.hasError('maxFailError')) {
      message = errors['maxFailError'];
    }

    if (this.fieldControl.hasError('taxCode')) {
      message = errors['taxCode'];
    }

    if (this.fieldControl.hasError('overAge')) {
      message = errors['overAge'] + ` ${this.maxAge} anni.`; // Per ulteriori informazioni, <a href="/trova-agenzie">recati in Agenzia.</a>
    }

    if (this.fieldControl.hasError('DoubletCF')) {
      message = errors['DoubletCF'];
    }
    if (this.fieldControl.hasError('taxCodeName')) {
      message = errors['taxCodeName'];
    }
    if (this.fieldControl.hasError('taxCodeSurname')) {
      message = errors['taxCodeSurname'];
    }
    if (this.fieldControl.hasError('required')) {
      message = errors['required'];
    }

    return message;
  }

  checkCFEvent(
    type: 'name' | 'surname',
    error: 'errorNameCF' | 'errorSurnameCF'
  ) {
    this.checkCFSubscription = this.statusService
      .getFormGroup()
      .valueChanges.subscribe((e) => {
        if (e) {
          const controls = this.statusService.getFormGroup().controls;
          const CF =
            controls && controls[this.referenceCF as keyof typeof controls];
          if (this.fieldControl && this.fieldControl.value && CF && CF.valid) {
            const isValid = !this.utilsService.checkNameSurnameOnFiscalCode(
              CF.value,
              this.fieldControl.value,
              type
            );
            if (isValid) {
              this.field &&
                removeError(
                  controls[this.field.reference as keyof typeof controls],
                  error
                );
            } else {
              this.field &&
                addError(
                  controls[this.field.reference as keyof typeof controls],
                  error
                );
            }
          } else {
            this.field &&
              removeError(
                controls[this.field.reference as keyof typeof controls],
                error
              );
          }
        }
      });
  }

  CfWithNameESurname(refToCompare: string, type: 'name' | 'surname') {
    this.nameCFSubscription = this.statusService
      .getFormGroup()
      .valueChanges.subscribe((res) => {
        if (res) {
          const controls = this.statusService.getFormGroup().controls;
          const error = type === 'name' ? 'errorNameCF' : 'errorSurnameCF';
          const taxCodeError =
            type === 'name' ? 'taxCodeName' : 'taxCodeSurname';
          //console.log(res, 'Response valuesChanges');
          if (
            this.field &&
            this.field.reference &&
            res[refToCompare] &&
            res[this.field.reference] &&
            res[this.field.reference].length === 16
          ) {
            const isValid = !this.utilsService.checkNameSurnameOnFiscalCode(
              res[refToCompare],
              res[this.field.reference],
              'taxcode',
              type
            );
            if (controls && controls[this.field.reference]) {
              if (isValid) {
                removeError(controls[this.field.reference], taxCodeError);
                removeError(controls[refToCompare], error);
              } else {
                addError(controls[this.field.reference], taxCodeError);

                controls[refToCompare].markAsTouched();
                addError(controls[refToCompare], error);
              }
            } else {
              removeError(controls[this.field.reference], taxCodeError);
              removeError(controls[refToCompare], error);
            }
          } else {
            if (
              this.field &&
              this.field.reference &&
              res[this.field.reference] &&
              res[this.field.reference].length === 16
            ) {
              removeError(controls[this.field.reference], taxCodeError);
            }
            if (res[refToCompare]) {
              removeError(controls[refToCompare], error);
            }
          }
        }
      });
  }

  checkCF(taxcode: string, type: 'name' | 'surname') {
    return (control: AbstractControl): ValidationErrors | null => {
      let esito: any = null;
      if (control && control.value) {
        const isValid = !this.utilsService.checkNameSurnameOnFiscalCode(
          taxcode,
          control.value,
          type
        );
        const errorNameCF = { errorNameCF: true };
        const errorSurnameCF = { errorSurnameCF: true };
        esito = isValid ? null : type == 'name' ? errorNameCF : errorSurnameCF;
      }
      return esito;
    };
  }

  maxFailValidator() {
    return (control: AbstractControl): ValidationErrors | null => {
      let esito: any = null;
      if (control) {
        const isValid = this.maxAttempts
          ? this.statusService.getNumberFailed(parseInt(this.maxAttempts)) > 0
          : true;
        esito = isValid ? null : { maxFailError: true };
      }
      return esito;
    };
  }

  timerValidator(): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      return this.statusService.observableTimer.pipe(
        map((res) => {
          return res ? null : { errorTimeOut: true };
        })
      );
    };
  }

  cfDataNascitaOverAgeValidator(Age: string, dateToCompare: string) {
    return (control: AbstractControl): ValidationErrors | null => {
      let esito: any = null;
      if (control && control.value && control.value.length === 16) {
        let compareDate = dateToCompare;
        const dataDiNascitaByCf = TpdUtils.dataNascitaFromCF(
          control.value
        ) as string;
        if (dataDiNascitaByCf && dataDiNascitaByCf.indexOf('/') > -1) {
          const dataNascitaSplitted = dataDiNascitaByCf.split('/');
          const dataNascita = new Date(
            dataNascitaSplitted[1] +
              '/' +
              dataNascitaSplitted[0] +
              '/' +
              dataNascitaSplitted[2]
          );
          const maxAge = parseInt(Age, 10);
          const maxAgeBirthday = new Date(
            dataNascita.setFullYear(dataNascita.getFullYear() + maxAge)
          );
          if (!dateToCompare.includes('-')) {
            const [year, month, day] = [
              dateToCompare.slice(0, 4),
              dateToCompare.slice(4, 6),
              dateToCompare.slice(6),
            ];
            compareDate = year + '-' + month + '-' + day;
          }
          const dataFineViaggio = new Date(compareDate);

          esito =
            dataFineViaggio.getTime() > maxAgeBirthday.getTime()
              ? { overAge: true }
              : null;
        }
      }
      return esito;
    };
  }

  checkDoubletCF(keyWord: string) {
    this.doubletCFSubscription = this.statusService
      .getFormGroup()
      .valueChanges.subscribe((e) => {
        if (e) {
          const controls = this.statusService.getFormGroup().controls;
          if (controls) {
            const filteredControls = [];
            for (const c in controls) {
              if (
                this.field &&
                c &&
                c.includes(keyWord) &&
                c != this.field.reference &&
                controls[c]?.value === this.fieldControl.value
              )
                filteredControls.push(controls[c]);
            }
            if (filteredControls.length > 0) {
              this.field &&
                this.field.reference &&
                addError(controls[this.field.reference], 'DoubletCF');
            } else {
              this.field &&
                this.field.reference &&
                removeError(controls[this.field.reference], 'DoubletCF');
            }
          } else {
            this.field &&
              this.field.reference &&
              removeError(controls[this.field.reference], 'DoubletCF');
          }
        }
      });
  }

  checkLetterCalculation() {
    return (control: AbstractControl): ValidationErrors | null => {
      let esito: any = null;
      if (control && control.value && control.value.length === 16) {
        const fiscalCode = control.value.toUpperCase();
        const rest = this.utilsService.checkLetter(fiscalCode);
        const lastLetter = fiscalCode[fiscalCode.length - 1];
        esito =
          rest === letterNumberCf[lastLetter as keyof typeof letterNumberCf]
            ? null
            : { taxCode: true };
      }
      return esito;
    };
  }

  buildFormValidation() {
    this.formValidazione = new FormArray<any>([]);
    for (let i = 0; i < Number(this.maxChars); i++) {
      // eslint-disable-next-line @typescript-eslint/unbound-method
      this.formValidazione.push(new FormControl('', Validators.required));
    }
  }
  changeInput(control: any, index: any, $event: any) {
    if ($event.keyCode === 8) {
      // backspace
      const i = index == 0 ? 0 : index - 1;
      if (typeof document === 'object') {
        const newFocus = document.getElementById(`Input_${i}`);
        newFocus && newFocus.focus();
      }
    } else {
      if (control?.value.toString().length > 1) {
        const firstFigure = control?.value.toString()[0];
        control.setValue(firstFigure);
      } else {
        //control.setValue(control.value.replace(/[^0-9]/g, ''));
        if (control?.valid && index < Number(this.maxChars) - 1) {
          /*TODO: Sotto a parseInt prima stava solo index. eslint dava errore e ho messo il parse. Poteva essere anche float?*/
          if (typeof document === 'object') {
            const newFocus = document.getElementById(
              `Input_${parseInt(index) + 1}`
            );
            newFocus && newFocus.focus();
          }
        }
      }
    }
  }

  requiredIF() {
    this.elseIFSubscription = this.statusService
      .getFormGroup()
      .valueChanges.subscribe((res) => {
        if (res) {
          const controls = this.statusService.getFormGroup().controls;
          this.required = true;
          // this.field.required = true
          if (this.field && this.field.reference && res) {
            if (
              res['Contraente.Contatti.Email'] !== '' &&
              res['Contraente.Contatti.Cellulare'] == ''
            ) {
              if (
                this.field.fieldID == 'Cellulare' &&
                this.field.customAttributes.requiredIF == 'true' &&
                this.field.customAttributes.conditionReference ==
                  'Contraente.Contatti.Email'
              ) {
                this.required = false;
                // this.field.required = false
              }

              if (
                this.field.fieldID == 'Email' &&
                this.field.customAttributes.requiredIF == 'true' &&
                this.field.customAttributes.conditionReference ==
                  'Contraente.Contatti.Cellulare'
              ) {
                this.required = true;
                // this.field.required = true
              }
            } else if (
              res['Contraente.Contatti.Cellulare'] !== '' &&
              res['Contraente.Contatti.Email'] == ''
            ) {
              if (
                this.field.fieldID == 'Email' &&
                this.field.customAttributes.requiredIF == 'true' &&
                this.field.customAttributes.conditionReference ==
                  'Contraente.Contatti.Cellulare'
              ) {
                this.required = false;
                // this.field.required = false
              }

              if (
                this.field.fieldID == 'Cellulare' &&
                this.field.customAttributes.requiredIF == 'true' &&
                this.field.customAttributes.conditionReference ==
                  'Contraente.Contatti.Email'
              ) {
                this.required = true;
                // this.field.required = true
              }
            }
          }
        }
      });
  }

  public get hasDateFormat(): boolean{
    return !!this.customAttributes?.dateFormat;
  }

  public get fieldValue(): string{
    let esito = this.fieldControl.value;
    if(this.hasDateFormat)
      esito = esito.replace('00:00', '24:00');
    return esito;
  }

  onChange(val: any) {
    this.settaValoriGestioneProcessoDaStorage(false)
    this.messageService.onEventExecuted(
      this.actionsSet,
      EventsManaged.change,
      this.processManagement,
      this.gestioneProcesso
    );
  }

  ngOnDestroy(): void {
    if (this.field) {
      if (this.field.reference)
        this.statusService.removeControlFormGroup(this.field.reference);

      if (this.customAttributes) {
        this.timerSubscription && this.timerSubscription.unsubscribe();
        this.formValidazioneChangeSub &&
          this.formValidazioneChangeSub.unsubscribe();
        this.viaSubscription && this.viaSubscription.unsubscribe();
        this.checkCFSubscription && this.checkCFSubscription.unsubscribe();
        this.doubletCFSubscription && this.doubletCFSubscription.unsubscribe();
        this.nameCFSubscription && this.nameCFSubscription.unsubscribe();
        this.elseIFSubscription && this.elseIFSubscription.unsubscribe();
      }
    }
    this.errorListener && this.errorListener.rimuoviListenerDiErroreInputInvalido();
  }
}
