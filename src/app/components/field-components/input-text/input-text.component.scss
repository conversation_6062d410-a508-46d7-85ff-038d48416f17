@import "../../../../styles.scss";

.layout-input {
  width: 100%;
}

.input-dati-utente {
  width: 100%;
  min-width: 100%;
  height: 48px;
  border-radius: 0;
  background-color: white;
  border: solid 1px $main_color;
  color: $middle-grey-pu;
}

.invalid-input {
  border-color: $alert-color;
}

.icon-Attenzione-pieno {
  color: $error_red;
  font-size: 40px;
}

.bd-icona {
  font-size: 22px;
  color: #ff001f;
  top: 5px;
  position: relative;
  margin-right: 5px;
}

.testo-non-valido {
  height: 14px;
  font-family: "Unipol Medium";
  font-size: 14px;
  font-stretch: normal;
  font-style: normal;
  line-height: 0.88;
  letter-spacing: normal;
  color: #ff001f;
}

.disable-input {
  border-color: $ivory !important;
  color: $middle-grey;
  opacity: 1;
  cursor: not-allowed;
  pointer-events: none;
}

.disable {
  color: $middle-grey;
}

//Definizione dello stile per l'otp
.splittedBox {
  display: flex;
  flex-direction: column;
  align-items: center;

  > .inputContainer {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;

    gap: 16px;

    .otpInput {
      font-size: 16px;
      font-family: "Unipol Bold";
      font-weight: 550;
      text-align: center;
      color: black;

      background-color: transparent;
      border: none;
      border-bottom: 2px solid black !important;

      display: flex;
      justify-content: center;
      align-items: center;

      aspect-ratio: 1 / 1;
      width: 40px;

      /*Firefox*/
      -moz-appearance: textfield;

      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        display: none;
      }
    }
  }

  .error-otp-validation {
    color: $main_red;
    margin-bottom: 10px;
  }
}
