import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import {
  TextCss,
  TextHandlerService,
} from '../../../services/text-handler.service';
import { errors } from '../../../utils/errorMessage';
import {
  IActionSet,
  IControl,
  IField,
  IOption,
} from '../../../models/models.commons';
import { AnalyticsInterprete } from '../../../services/analytics.interprete.service';
import { MessagesService } from '../../../services/messages.service';
import { ListenerErroreFormInterface, StatusService } from "../../../services/status.service";
import { UtilsService } from '../../../services/utils.service';
import { EventsManaged } from '../../../utils/events.enum';
import { EventsMethod } from '../../../utils/eventsMethod';

@Component({
  selector: 'tpd-dropdown-button',
  templateUrl: './dropdown-button.component.html',
  styleUrls: ['./dropdown-button.component.scss'],
})
export class DropdownButtonComponent extends EventsMethod implements OnInit, OnDestroy {
  private _standAloneFormIndex = -1;

  constructor(
    private utilsService: UtilsService,
    public override messageService: MessagesService,
    public analyticsService: AnalyticsInterprete,
    private statusService: StatusService,
    private textHandlerService: TextHandlerService
  ) {
    super(messageService, analyticsService);
    this._standAloneFormIndex = this.statusService.currentStandAloneFormIndex;
  }

  override field!: IField;
  fieldControl = new FormControl('', null);
  label = '';
  labelFormat = '';
  control: IControl | undefined;
  override actionsSet!: IActionSet[];
  customAttributes: any;
  disabled: boolean | undefined;
  maxLength: number | undefined;
  options: IOption[] | undefined;
  opened = false;
  defaultValue = '';
  formatType: string | undefined;
  showLabel: boolean | undefined;
  override eventsManaged = EventsManaged;
  optionSelected: IOption | undefined = {
    value: '',
    key: '',
  };
  public labelCss: TextCss | null = null;
  public setCss = false;
  public showError = true;
  private errorListener: ListenerErroreFormInterface;
  public showLikeLabel = false;

  public placeHolder = ''; //Placeholder da visualizzare sul select

  @Input() set data(input: { field: IField }) {
    this.field = input.field;
    this.defaultValue = this.utilsService.htmlDecode(this.field?.value ?? '');
    this.fieldControl = new FormControl(this.defaultValue);
    this.control = this.field.control;

    this.label = this.utilsService.htmlDecode(this.field.label ?? '');
    if (this.field.labelFormat) {
      this.labelFormat = this.utilsService.getClassFromFormat(
        this.field.labelFormat
      );
      this.labelCss = this.textHandlerService.getTextCss(
        this.field.labelFormat
      );

      this.setCss = !!this.labelCss;
    } else {
      this.labelFormat = '';
    }
    this.disabled = this.field.disabled;
    this.maxLength = this.field.maxLength;
    this.customAttributes = this.field?.customAttributes;

    if (this.customAttributes && this.customAttributes.showLikeLabel) {
      this.showLikeLabel = this.customAttributes.showLikeLabel;
    }

    //options
    if (this.field && this.field.control && this.field.control.modes) {
      this.options = this.field.control.modes[0].options;
    }

    if (this.field.required) {
      // eslint-disable-next-line @typescript-eslint/unbound-method
      this.fieldControl.setValidators([Validators.required]); //errore lint
      this.showError = false;
    }

    //ReadOnly, prevalorizzazione e disabilitazionne
    if (this.field.readOnly === true) {
      if (this.options && this.options.length > 0) {
        const sVal = this.getOptionValue(this.defaultValue);
        this.fieldControl.setValue(sVal);
        if (this.field && this.field.control && this.field.control.modes) {
          const modes1 = this.field.control.modes.find(
            (element) => element.modeType === 'readOnly'
          );
          const formatType = modes1?.formatType;
          this.formatType = formatType;
        }
      }
      if (this.field.disabled) {
        this.fieldControl.disable();
      }
    }

    //Lettura dei modes per il recupero delle informazioni
    if (this.field && this.field.control && this.field.control.modes) {
      const modes = this.field.control.modes;
      for (const mode of modes) {
        if (mode.placeholder) this.placeHolder = mode.placeholder;
      }
    }
    //----------------------------------------------------

    if (this.control) {
      this.actionsSet = this.control.actionSets ?? [];
    }

    // prevalorizzazione
    this.fieldControl.setValue(this.defaultValue);

    if (this.options && this.options.length > 0) {
      this.optionSelected = this.options.find(
        (e) => e.key === this.defaultValue
      );
    } else {
      this.optionSelected = undefined;
    }

    // istanza controller text-input
    if (!this.field.readOnly) {
      this.statusService.addControlFormGroup(
        this.field.reference ?? '',
        this.fieldControl
      );
    }
    this.setValueProcessManagement();
    this.settaValoriGestioneProcesso();
  }

  public ngOnInit(): void {
    this.errorListener = this.statusService.registraListenerDiErroreInputInvalido(this._standAloneFormIndex, statoErrore =>  this.showError = statoErrore);
  }

  getOptionValue(value: string): string {
    if (this.options) {
      for (const obj of this.options) {
        if (obj['key'] === value) {
          return this.utilsService.htmlDecode(obj['value'] ?? '');
        }
      }
    }

    return '';
  }

  openSelect() {
    const open = !this.opened;
    this.opened = open;
  }

  override onClick(opt: IOption) {
    if (opt.key !== this.fieldControl.value) {
      this.optionSelected = opt;
      this.fieldControl.setValue(opt.key ?? null);
      this.checkSendAnalytics();
      this.onChange();
    }
    this.opened = false;
    this.messageService.onEventExecuted(
      this.actionsSet,
      EventsManaged.click,
      this.processManagement,
      this.gestioneProcesso
    );
  }

  filterElements(ev: any, i?: number) {
    ev.preventDefault();
    const input = ev.target.value.toUpperCase();
    if (input.length > 1) {
      this.changeSelectedElement(input);
    }
  }

  changeSelectedElement(autocomplete: any) {
    if (
      this.field &&
      this.field.control &&
      this.field.control.modes &&
      this.field.control.modes[0].options
    ) {
      this.options = this.field.control.modes[0].options;
      const options_new = [];
      for (let i = 0; i < this.options.length; i++) {
        if (this.options[i].value?.toUpperCase().match(autocomplete)) {
          options_new.push(this.options[i]);
        }
      }
      this.options = options_new;
    }
  }

  /**
   * Restituisce il value da visualizzare sul dropdown
   */
  public get value(): string {
    return this.optionSelected
      ? `${this.optionSelected.value[0].toUpperCase()}${this.optionSelected.value.substring(
          1
        )}`
      : this.defaultValue
      ? this.defaultValue
      : this.placeHolder;
  }

  errorMessage(): string {
    let message = '';

    if (this.fieldControl.hasError('required')) {
      message = errors['required'];
    }

    return message;
  }

  ngOnDestroy(): void {
    this.errorListener && this.errorListener.rimuoviListenerDiErroreInputInvalido;
    if (this.field && !this.field.readOnly)
      this.statusService.removeControlFormGroup(this.field.reference ?? '');
  }
}
