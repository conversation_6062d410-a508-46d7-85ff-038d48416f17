@import "../../../../styles.scss";

.select {
  @include selectBoxUnipol(".tpd_selectAngular");
  margin-bottom: 0 !important;

  .select-styled {
    color: $middle-grey-pu !important;
    position: absolute;
    background-color: #fff;
    padding: 6px 12px !important;
    padding-right: 40px !important;
    border: 1px solid #0f3250 !important;
    text-align: left;
    word-break: break-word;
    overflow: hidden;
    align-items: center;
    height: 48px;
    display: flex;
    overflow: hidden;

    >.valueContent{
      display: block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      height: auto;
      align-self: center;
    }

    &.isDisabled {
      pointer-events: none;
      color: $grey !important;
      border: 0 !important;
      &:after {
        display: none;
      }
    }

    &.active {
      &:after {
        transform: rotate(-45deg) !important;
        top: 19px !important;
      }
    }

    &:after {
      border: none !important;
      transform: rotate(135deg) !important;
      width: 11px !important;
      cursor: pointer;
      display: inline-block;
      height: 11px !important;
      border-style: solid !important;
      border-width: 2px 2px 0 0 !important;
      position: absolute;
      top: 17px !important;
      right: 17px !important;
    }
  }

  .tpd_selectAngular {
    display: flex;
    font-family: "Unipol";
    font-size: 16px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.25;
    letter-spacing: normal;
    color: #0f3250;
    color: #fff;
    width: 100%;
    height: 50px;

    &.isDisabled {
      cursor: default !important;
    }

    .select-options {
      border: 1px solid #0f3250 !important;
      border-top: none !important;
      top: 96% !important;

      box-shadow: 0 1px 8px 0 rgba(0, 0, 0, 0.2);

      li {
        display: flex;
        flex-direction: row;
        align-items: center;
        color: $middle-grey-pu !important;
        margin: 0;
        padding: 6px 12px !important;
        border-top: 1px solid #0f3250 !important;
        border-bottom: none !important;
        border-left: none !important;
        border-right: none !important;
        min-height: 48px;
        height: auto;
        z-index: 50;

        >.itemContent{
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
        }
      }
    }

    @media #{$bkp_mobile} {
      max-width: none;
    }

    @media #{$bkp_tablet} {
      max-width: 500px;
    }

    @media #{$bkp_desktop} {
      max-width: 550px;
    }
    .invalid-input {
      border: solid 1px $alert-color !important;
    }
  }
}
.simple-label {
  font-size: 13px;
  font-weight: bold;
  color: $dark-light_blue;
}

.select-read-only {
  color: $middle-grey-pu !important;
  background-color: #f1f1f1;
  padding: 12px 16px;
  text-align: left;
  word-break: break-word;
  overflow: hidden;
  align-items: center;
  height: 48px;
  display: flex;

  &.active {
    &:after {
      transform: rotate(-45deg) !important;
      top: 19px !important;
    }
  }

  &:after {
    border: none !important;
    transform: rotate(135deg) !important;
    width: 11px !important;
    cursor: pointer;
    display: inline-block;
    height: 11px !important;
    border-style: solid !important;
    border-width: 2px 2px 0 0 !important;
    position: absolute;
    top: 17px !important;
    right: 17px !important;
  }
}

.layout-input {
  width: 100%;
}
