<div *ngIf="field" class="select layout-input">
  <div
    *ngIf="this.field.labelReserveSpace || this.label || this.field.readOnly"
    [ngClass]="{'mrg-label-input': !this.field.readOnly}">
    <span
      class="{{ this.labelFormat }}"
      *ngIf="!this.setCss; else labelElseBlock"
      >{{ this.field.readOnly ? this.value : this.label }}</span
    >
    <ng-template #labelElseBlock>
      <custom-text-style
        [textCss]="this.labelCss"
        [content]="this.field.readOnly ? this.value : this.label"
      ></custom-text-style>
    </ng-template>
  </div>

  <div
    *ngIf="!field.readOnly"
    class="tpd_selectAngular"
    [ngClass]="{ isDisabled: disabled }"
  >
    <div
      class="select-styled"
      (click)="opened = !opened"
      [ngClass]="{
        active: opened,
        'isDisabled disabled-input-box': disabled,
        'invalid-input': fieldControl.touched && fieldControl.invalid
      }"
    >
      <span
        class="valueContent"
        [innerHtml]="
          optionSelected
            ? optionSelected.value
            : this.defaultValue
            ? this.defaultValue
            : this.placeHolder
        ">
      </span>
    </div>
    <ul class="select-options" [hidden]="!opened">
      <li *ngFor="let opt of options" (click)="onClick(opt)">
        <span class="itemContent">{{ opt.value }}</span>
      </li>
    </ul>
  </div>

  <div
    [hidden]="!showError"
    *ngIf="
      fieldControl.touched &&
      fieldControl.invalid &&
      !disabled &&
      errorMessage()
    "
  >
    <i style="color: #ff001f;" class="icon-Attenzione-pieno bd-icona"></i>
    <span class="testo-non-valido">{{ errorMessage() }}</span>
  </div>
</div>
