import { Component, Input, ViewChild } from '@angular/core';
import { Subscription } from 'rxjs';
import {
  IActionSet,
  IControl,
  IField,
  IGroup,
} from '../../../models/models.commons';
import { AnalyticsInterprete } from '../../../services/analytics.interprete.service';
import { MessagesService } from '../../../services/messages.service';
import { StatusService } from '../../../services/status.service';
import {
  TooltipDirection, TooltipDirectionMapper,
  UtilsService
} from "../../../services/utils.service";
import { EventsManaged } from '../../../utils/events.enum';
import { EventsMethod } from '../../../utils/eventsMethod';
import {
  ICON_MAPPER,
  IMG_DESKTOP,
  IMG_MOBILE,
  IMG_TABLET,
} from '../../../utils/iconMapping';
import { Tooltip } from '../../tooltip/tooltip';
import RetrieverService from "../../../services/retriever.service";
import { TpdInterpreteDxApi } from "../../../tpd-interprete-angular-dx-api-pu/tpd-interprete-angular-dx-api-pu";

interface ICustomAttributePxIcon {
  transform?: string;                       // roundedApp Da ignorare al momento per web
  size?: string;                            // Dimensione dell'icona. Se il type = image allora sarà fullwidth altrimenti se il type = icon potrà essere S,M,L
  resource?: string;                        //Risorsa di caricamento dell'immagine. Deve essere presente nell'ICON_MAPPER per essere letta correttamente
  type?: 'image' | 'icon';                  //Tipo di icona gestita, può essere un'immagine oppure un icona
  TooltipID?: string;                       //Gestisce l'id del tooltip
  tooltipDirection?: TooltipDirection;      //Gestisce la direzione di apertura della tooltip
  onCloseRefresh?: string;                  //Se viene chiuso il modale o la tooltip indica se la pagina sotto deve o meno essere refreshata
  label?: string;                           //Label dell'icona
  labelFormat?: string;                     //Formato della label
  platform?: string;                        //Piattaforma di rendering dell'icona
  modalCloseIcon?: string;                  //Sono identiche come significato, servono a chiudere una modale
  closeModal?: string;                      //Come sopra

  webResponsiveSize?: string;               //Serve a gestire la responsività dell'icona sulle 3taglie del web e l'allinemanto dove possibile
  productType?: string;                     //Product type per la chiamata alla create di modifica

  specialLoading?: string;                  //Indica se chiamare oppure no la special loading
  loadingList?: 'warranty' | 'showPrice';   //Indica il tipo di loading speciale da chiamare
}

@Component({
  selector: 'tpd-px-icon',
  templateUrl: './px-icon.component.html',
  styleUrls: ['./px-icon.component.scss'],
})
export class PxIconComponent extends EventsMethod {
  private static _tooltipIdCounter = 0;
  private static _tooltipListId: string[] = [];

  @ViewChild('tooltip') tooltip!: Tooltip;

  public override field?: IField;
  public override actionsSet: IActionSet[] = [];
  private overlaySubscription?: Subscription;
  private customAttributes: ICustomAttributePxIcon = {};
  private control: IControl = {};

  public label = '';
  public iconLabelContent = '';
  public iconLabelFormat = '';

  public type: 'image' | 'icon' = 'icon';
  public size: 'S' | 'M' | 'L' = 'S';
  public resource = '';
  public isWebPlatform = true;
  public boxed = false;

  public modalCloseIcon = false;
  public callbackByComponentID: (val: string) => void;

  public tooltipID = '';
  public tooltipView: IGroup[] = [];
  public tooltipDirection = TooltipDirection.TooltipLU;
  public refreshOnClose = '';

  public imgMobile = '';
  public imgTablet = '';
  public imgDesktop = '';

  public webResponsiveSize: string;
  public responsiveSizeFormattedLabel: string;


  private _mostraLoadingCustom = false;
  private _stileLoadingCustom: "warranty" | "showPrice";
  private _caseDiModifica = false;
  private _refreshCloseIcon = false;

  constructor(
    private utilsService: UtilsService,
    public override messageService: MessagesService,
    public analyticsService: AnalyticsInterprete,
    private statusService: StatusService,
    private _retrieverService: RetrieverService
  ) {
    super(messageService, analyticsService);
  }

  @Input() set data(input: { field: IField }) {
    this.field = input.field;
    if (this.field) {
      this.label = this.utilsService.htmlDecode(this.field.label);
      this.customAttributes = this.field.customAttributes;
      this.control = this.field.control;

      if (this.control)
        this.actionsSet = this.control.actionSets ?? [];

      if (this.customAttributes) {
        this.tooltipID = this._retrieverService.getCustomAttributeFromFieldUnsensitive(input.field, 'tooltipID', undefined);
        if(!this.tooltipID && this.actionsSet.length > 0 && this.actionsSet[0].actions){
          //Genera un tooltip nel caso in cui non sia stato passato come parametro
          if(this.actionsSet[0].actions.find(action => action.action === 'localAction' && action.actionProcess.target === 'overlay')){
            this.tooltipID = this._generateRandomTooltipId();
          }
        }
        if(this._idExistInCurrentView(this.tooltipID)){
          this.tooltipID = this._generateRandomTooltipId();
        }
        PxIconComponent._tooltipListId.push(this.tooltipID);

        const tooltipDirectionData = this._retrieverService.getCustomAttributeFromFieldUnsensitive(input.field, 'tooltipDirection', TooltipDirection.TooltipLU).toLowerCase();
        this.tooltipDirection = TooltipDirectionMapper[tooltipDirectionData || 'lu'];
        this.refreshOnClose = this._retrieverService.getCustomAttributeFromFieldUnsensitive(input.field, 'onCloseRefresh');
        this.type = this._retrieverService.getCustomAttributeFromFieldUnsensitive(input.field, 'type', 'icon') as 'image' | 'icon';
        this.resource = this._retrieverService.getCustomAttributeFromFieldUnsensitive(input.field, 'resource');
        this.size = this._retrieverService.getCustomAttributeFromFieldUnsensitive(input.field, 'size', 'S') as 'S' | 'M' | 'L';
        this.isWebPlatform = this._retrieverService.getCustomAttributeFromFieldUnsensitive(input.field, 'platform', 'web') === 'web';
        this.iconLabelContent = this._retrieverService.getCustomAttributeFromFieldUnsensitive(input.field, 'label');
        this.iconLabelFormat = this.utilsService.getClassFromFormat(this._retrieverService.getCustomAttributeFromFieldUnsensitive(input.field, 'labelFormat'));
        this.modalCloseIcon =
          this._retrieverService.getCustomAttributeFromFieldUnsensitive(input.field, 'modalCloseIcon', undefined) === 'true' ||
          this._retrieverService.getCustomAttributeFromFieldUnsensitive(input.field, 'closeModal', undefined) === 'true';
        this._refreshCloseIcon = this._retrieverService.getCustomAttributeFromFieldUnsensitive(input.field, 'componentID', undefined) === 'closeModalAndReloadPage';
        this.webResponsiveSize = this._retrieverService.getCustomAttributeFromFieldUnsensitive(input.field, 'webResponsiveSize', undefined);

        this.responsiveSizeFormattedLabel = this._responsiveSizeFormattedLabel;
        this._caseDiModifica = this._retrieverService.getCustomAttributeFromFieldUnsensitive(input.field, 'GestioneProcesso.CaseDiModifica', undefined) === 'true';
        this._mostraLoadingCustom = this._retrieverService.getCustomAttributeFromFieldUnsensitive(input.field, 'specialLoading', undefined) === 'true';
        this._stileLoadingCustom = this._retrieverService.getCustomAttributeFromFieldUnsensitive(input.field, 'loadingList', "showPrice") as 'showPrice' | 'warranty';
      }

      if(this.type === 'image'){
        this.imgMobile = IMG_MOBILE[this.resource];
        this.imgTablet = IMG_TABLET[this.resource];
        this.imgDesktop = IMG_DESKTOP[this.resource];

        if(!this.imgMobile)
          this.imgMobile = `/NextAssets/interprete-pu/immagini-pu/Mobile/${this.resource}.png`;
        if(!this.imgTablet)
          this.imgTablet = `/NextAssets/interprete-pu/immagini-pu/Tablet/${this.resource}.png`;
        if(!this.imgDesktop)
          this.imgDesktop = `/NextAssets/interprete-pu/immagini-pu/Desktop/${this.resource}.png`;
      }else{
        if (this.resource) {
          this.boxed = this.resource.includes('Boxed');
          this.resource = ICON_MAPPER[this.resource];
        }

        if (this.modalCloseIcon && this.isWebPlatform)
          this.callbackByComponentID = this.utilsService.getCustomCallbackByComponentID("closeModal");
      }

      this.overlaySubscription = this.messageService
        .getOverlayData()
        .subscribe((e) => {
          if (e.id === this.tooltipID) {
            this.tooltipView = e.data;
            this.tooltip.openTooltip();
          }
        });
    }

    this.setValueProcessManagement();
    this.settaValoriGestioneProcesso();
  }

  private _generateRandomTooltipId(): string{
    return `RANDOM-TOOLTIP-ID-${PxIconComponent._tooltipIdCounter++}`;
  }

  private _idExistInCurrentView(tooltipId: string): boolean{
    const index = PxIconComponent._tooltipListId.findIndex(targetId => targetId === tooltipId);
    return index !== -1;
  }

  private _deleteTooltipId(tooltipId: string){
    PxIconComponent._tooltipListId = PxIconComponent._tooltipListId.filter(targetId => targetId !== tooltipId);
  }

  private get _responsiveSizeFormattedLabel(): string{
    let esito = '';

    if(this.webResponsiveSize){
      const part = this.webResponsiveSize.split(' ');
      if(part.length === 3){
        for(let i = 0; i < part.length; i++){
          const size = part[i];
          const dimensione = size.substring(0, size.length - 1);
          const allineamento = size.substring(size.length - 1);

          const mapperTaglia = ['--desktop', '--tablet', '--mobile'];
          const mapperAllineamento = {'L': '0 auto 0 0', 'C': '0 auto', 'R': '0 0 0 auto'};
          esito += `${mapperTaglia[i]}-size: ${dimensione}px; ${mapperTaglia[i]}-align: ${mapperAllineamento[allineamento]}; `;
        }
      }
    }

    return esito;
  }

  override async onClick() {
    //Richiamiamo il settaValoriGestioneProcesso per gestire la parte dei valoi memorizzati sulle chiavi
    this.settaValoriGestioneProcesso(true);
    this.checkSendAnalytics();

    this.messageService.customLoadingFlag = this._mostraLoadingCustom;
    this.messageService.customLoadingType = this._stileLoadingCustom;

    const tooltipVal = this.tooltipID && this.statusService.getTooltipElemByID(this.tooltipID);
    if (tooltipVal) {
      if (this.tooltip.isOpen) {
        this.tooltip.close();
      } else {
        this.tooltipView = tooltipVal;
        this.tooltip.openTooltip();
      }
    } else {
      if(this._caseDiModifica){
        this._chiamaCreateDiModifica();
      }else if (this.callbackByComponentID && typeof this.callbackByComponentID === 'function') {
        await this.callbackByComponentID(this.refreshOnClose);
        this.messageService.onEventExecuted(
          this.actionsSet,
          EventsManaged.click,
          this.processManagement,
          this.gestioneProcesso,
          this.tooltipID
        );
      } else if (this._refreshCloseIcon){
        this.messageService.onEventExecuted(
          this.actionsSet,
          EventsManaged.click,
          this.processManagement,
          this.gestioneProcesso,
          undefined,
          'closeModalAndReloadPage'
        );
      } else {
        this.messageService.onEventExecuted(
          this.actionsSet,
          EventsManaged.click,
          this.processManagement,
          this.gestioneProcesso,
          this.tooltipID
        );
      }
    }
  }

  private _chiamaCreateDiModifica(){
    const productType = {
      'AUTO': 'PUVEICOLO',
      'CASA': 'PUCASA',
      'FAMIGLIA': 'PUFAMIGLIA'
    }[this.customAttributes.productType]?? this.customAttributes.productType;
    const gestioneProcesso = {};
    for(const key in this.customAttributes){
      if(key.toLowerCase().includes('gestioneprocesso'))
        gestioneProcesso[key] = this.customAttributes[key];
    }
    TpdInterpreteDxApi.TpdInterpreteDxApiContext.chiamaCustomCreate(productType, gestioneProcesso);
  }

  public ngOnDestroy() {
    this.overlaySubscription && this.overlaySubscription.unsubscribe();
    this._deleteTooltipId(this.tooltipID);
  }
}
