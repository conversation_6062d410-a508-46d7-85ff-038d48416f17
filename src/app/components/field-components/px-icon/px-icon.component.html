<div
  *ngIf="type === 'icon'"
  [ngClass]="{
    'tooltip-container': this.tooltipView.length > 0,
    'iconLabel-container': iconLabelContent
  }">
  <ng-container *ngIf="label && field.showLabel">{{ label }}</ng-container>
  <div
    class="{{ resource }}"
    [ngClass]="{
      boxed: boxed,
      webResponsiveSize: !!this.webResponsiveSize
    }"
    [style]="this.responsiveSizeFormattedLabel"
    (click)="onClick()"
    (mouseover)="onHover()">
  </div>
  <ng-container *ngIf="iconLabelContent">
    <span
      class="{{ iconLabelFormat }} cursor-pointer"
      (click)="onClick()">
      {{ iconLabelContent }}
    </span>
  </ng-container>
  <ng-container *ngIf="tooltipID">
    <tooltip-dx-api
      #tooltip
      [data]="{ groups: tooltipView, tooltipDirection: tooltipDirection }">
    </tooltip-dx-api>
  </ng-container>
</div>

<div *ngIf="type === 'image'" (click)="onClick()" (mouseover)="onHover()">
  <div>
    <img [src]="imgMobile" style="margin: 0 auto; width: 100%;" class="{{ size }} visibility-src-mobile" />
    <img [src]="imgTablet" style="margin: 0 auto; width: 100%;" class="{{ size }} visibility-src-tablet" />
    <img [src]="imgDesktop" style="margin: 0 auto; width: 100%;" class="{{ size }} visibility-src-desktop" />
  </div>
</div>
