<div *ngIf="field">
  <div
    class="{{ labelFormat }} label-input"
    [ngClass]="{ disable: disabled }"
    *ngIf="((field.labelReserveSpace && !label) || label) && !setCss"
  >
    {{ label }}
  </div>
  <!--Se arriva lo stile da PEGA-->
  <div *ngIf="((field.labelReserveSpace && !label) || label) && setCss">
    <div class="visible-desktop label-input" [ngStyle]="labelCss?.desktopCss">
      {{ label }}
    </div>
    <div class="visible-tablet label-input" [ngStyle]="labelCss?.tabletCss">
      {{ label }}
    </div>
    <div class="visible-mobile label-input" [ngStyle]="labelCss?.mobileCss">
      {{ label }}
    </div>
  </div>
  <input
    class="input-dati-utente"
    type="email"
    [placeholder]="placeholder"
    [required]="field.required"
    [value]="field.value"
    [ngClass]="{ 'disable-input': disabled, 'border-red': false }"
    (blur)="onBlur($event)"
    (change)="onChange($event)"
    (focus)="onFocus($event)"
    (click)="onClick($event)"
  />

  <div *ngIf="false" class="width-label">
    <i class="icon-Attenzione-pieno bd-icona"></i>
    <span class="testo-non-valido">Error </span>
  </div>
</div>
