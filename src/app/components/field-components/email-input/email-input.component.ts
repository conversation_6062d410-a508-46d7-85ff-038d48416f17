import { Component, Input } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import {
  TextCss,
  TextHandlerService,
} from '../../../services/text-handler.service';
import { IActionSet, IControl, IField } from '../../../models/models.commons';
import { AnalyticsInterprete } from '../../../services/analytics.interprete.service';
import { MessagesService } from '../../../services/messages.service';
import { StatusService } from '../../../services/status.service';
import { UtilsService } from '../../../services/utils.service';
import { EventsMethod } from '../../../utils/eventsMethod';

@Component({
  selector: 'tpd-email-input',
  templateUrl: './email-input.component.html',
  styleUrls: ['./email-input.component.scss'],
})
export class EmailInputComponent extends EventsMethod {
  constructor(
    private utilsService: UtilsService,
    public override messageService: MessagesService,
    public analyticsService: AnalyticsInterprete,
    private statusService: StatusService,
    private textHandlerService: TextHandlerService
  ) {
    super(messageService, analyticsService);
  }

  fieldControl = new FormControl('', null);
  label = '';
  labelFormat = '';
  control: IControl | undefined;
  override actionsSet!: IActionSet[];
  disabled: boolean | undefined;
  placeholder = '';
  formatType = '';
  showLabel: boolean | undefined;

  public labelCss?: TextCss | null;
  public setCss = false;

  @Input() set data(input: { field: IField }) {
    this.field = input.field;
    this.fieldControl = new FormControl(this.field.value ?? '');
    this.control = this.field.control;

    this.label = this.utilsService.htmlDecode(this.field.label ?? '');
    if (this.field.labelFormat) {
      this.labelFormat = this.utilsService.getClassFromFormat(
        this.field.labelFormat
      );
      this.labelCss = this.textHandlerService.getTextCss(
        this.field.labelFormat
      );

      this.setCss = !!this.labelCss;
    } else {
      this.labelFormat = '';
    }
    this.disabled = this.field.disabled;

    if (this.field.required) {
      // eslint-disable-next-line @typescript-eslint/unbound-method
      this.fieldControl.setValidators([Validators.required, Validators.email]); //errore lint
    } else {
      // eslint-disable-next-line @typescript-eslint/unbound-method
      this.fieldControl.setValidators([Validators.email]); //errore lint
    }

    if (this.field.readOnly === true) {
      const sVal = this.utilsService.htmlDecode(this.field.value ?? '');
      this.fieldControl.setValue(sVal);

      if (this.field.disabled) {
        this.fieldControl.disable();
      }
    }

    if (this.control && this.control.actionSets) {
      this.actionsSet = this.control.actionSets;
    }

    // prevalorizzazione
    this.fieldControl.setValue(this.field.value ?? '');

    // istanza controller email-input
    this.statusService.addControlFormGroup(
      this.field.reference ?? '',
      this.fieldControl
    );
    this.setValueProcessManagement();
    this.settaValoriGestioneProcesso();
  }

  ngOnDestroy(): void {
    this.statusService.removeControlFormGroup(
      this.field && this.field.reference ? this.field.reference : ''
    );
  }
}
