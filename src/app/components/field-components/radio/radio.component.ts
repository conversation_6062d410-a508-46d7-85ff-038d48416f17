import {
  Component,
  ElementRef,
  Input,
  QueryList,
  ViewChildren,
} from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import {
  TextCss,
  TextHandlerService,
} from '../../../services/text-handler.service';
import {
  IActionSet,
  IControl,
  IField,
  IOption,
} from '../../../models/models.commons';
import { AnalyticsInterprete } from '../../../services/analytics.interprete.service';
import { MessagesService } from '../../../services/messages.service';
import { StatusService } from '../../../services/status.service';
import { UtilsService } from '../../../services/utils.service';
import { EventsManaged } from '../../../utils/events.enum';
import { EventsMethod } from '../../../utils/eventsMethod';

type ComponentType =
  | 'Radio'
  | 'TimeSlot'
  | 'Community buttons'
  | 'Community buttons S'
  | 'Card'
  | 'FlatCard'
  | 'Flat Card Inverted'
  | 'FlatCardInverted'
  | 'RadioCard'
  | 'Package'
  | 'GreenCheck';

interface RadioComponentCustomAttributes {
  titleFormat: string; //Indica il formato del titolo non selezionato
  titleFormatSelected: string; //Indica il formato del titolo selezionato
  subtitleFormat: string; //Indica il formato del sottotitolo non selezionato
  subtitleFormatSelected: string; //Indica il formato del sottotitolo selezionato
  style: string; //Stile del radiobutton
  subtitle: string; //Lista dei sottotitoli
  textShowMore: string; //Testo da visualizzare per il "vedi di più"
  textShowLess: string; //Testo da visualizzare per il "vedi di meno"
  textShowStyle: string; //Stile per la label del "vedi di ..."
  maxVisibleElement: string; //Elementi massimi visualizzabili nel "vedi di più"
  separator: string; //Separator
  boldWord: string; //Parole in grassetto
}

@Component({
  selector: 'tpd-radio',
  templateUrl: './radio.component.html',
  styleUrls: ['./radio.component.scss'],
})
export class RadioComponent extends EventsMethod {
  override field: IField = undefined;
  override actionsSet: IActionSet[] = [];
  public fieldControl = new FormControl('');
  public customAttributes: RadioComponentCustomAttributes;
  public control?: IControl;

  public required = false;
  public readOnly = false;
  public valueReadonly$ = '';
  public isDisabled = false;

  public label = '';
  public labelCss: TextCss = null;
  public setLabelCss = false;
  public labelFormat$ = '';

  public setMultipleCss = false;
  public titleFormat: TextCss = null;
  public subtitleFormat: TextCss = null;
  public titleFormatSelected: TextCss = null;
  public subtitleFormatSelected: TextCss = null;

  public subtitle: string[] = [];
  public options: IOption[] = [];

  public componentType: ComponentType = 'Radio';
  public separator!: string;
  public community = false;

  public radioOrientation: 'horizontal' | 'vertical' = 'horizontal';
  public radioOrientationClass = '';
  public labelOptStyle = '';
  public opened = false;
  public placeholder = "Scegli un'opzione";

  public itemSelected: IOption;

  public vediTutte = false;
  public labelShowMore = 'Vedi tutte';
  public labelShowLess = 'Vedi meno';
  public labelShowStyle = '';
  public maxVisibleElement = 0;

  @Input() set data(input: { field: IField }) {
    this.field = input.field;
    if (this.field) {
      this.readOnly = this.field.readOnly;
      this.control = this.field.control;
      this.customAttributes = this.field.customAttributes;
      this.fieldControl.setValue(this.field.value ?? '');

      this.labelCss = this.textHandlerService.getTextCss(
        this.field.labelFormat
      );
      if (!this.labelCss) {
        this.labelFormat$ = this.utilsService.getClassFromFormat(
          this.field.labelFormat
        );
      } else this.setLabelCss = true;

      if (this.control && this.control.modes && this.control.modes[0].options)
        this.options = this.control.modes[0].options;

      if (!this.readOnly) {
        //Recupero dal control
        if (this.control) {
          if (this.control.actionSets)
            this.actionsSet = this.control.actionSets;
          if (
            this.control.modes &&
            this.control.modes[0] &&
            this.control.modes[0].orientation
          ) {
            this.radioOrientation = this.control.modes[0].orientation as
              | 'vertical'
              | 'horizontal';
            this.radioOrientationClass = `radio-${this.radioOrientation}`;
          }

          if (
            this.control.modes &&
            this.control.modes[0] &&
            this.control.modes[0].controlFormat
          )
            this.labelOptStyle = this.utilsService.getClassFromFormat(
              this.control.modes[0].controlFormat
            );
        }

        //Recupero dei customAttributes
        if (this.customAttributes) {
          if (this.customAttributes.titleFormat)
            this.titleFormat = this.textHandlerService.getTextCss(
              this.customAttributes.titleFormat
            );
          if (this.customAttributes.titleFormatSelected)
            this.titleFormatSelected = this.textHandlerService.getTextCss(
              this.customAttributes.titleFormatSelected
            );
          if (this.customAttributes.subtitleFormat)
            this.subtitleFormat = this.textHandlerService.getTextCss(
              this.customAttributes.subtitleFormat
            );
          if (this.customAttributes.subtitleFormatSelected)
            this.subtitleFormatSelected = this.textHandlerService.getTextCss(
              this.customAttributes.subtitleFormatSelected
            );
          if (this.customAttributes.subtitle)
            this.subtitle = this.utilsService
              .htmlDecode(this.customAttributes.subtitle)
              .split('|');
          if (this.customAttributes.style)
            this.componentType = this.customAttributes.style as ComponentType;
          if (this.customAttributes.separator)
            this.separator = this.customAttributes.separator;
          if (this.customAttributes.textShowMore)
            this.labelShowMore = this.utilsService.htmlDecode(
              this.customAttributes.textShowMore
            );
          if (this.customAttributes.textShowLess)
            this.labelShowLess = this.utilsService.htmlDecode(
              this.customAttributes.textShowLess
            );
          if (this.customAttributes.textShowStyle)
            this.labelShowStyle = this.utilsService.getClassFromFormat(
              this.customAttributes.textShowStyle
            );
          if (
            this.customAttributes.maxVisibleElement &&
            !isNaN(parseInt(this.customAttributes.maxVisibleElement))
          )
            this.maxVisibleElement = parseInt(
              this.customAttributes.maxVisibleElement
            );
          if (this.customAttributes.boldWord && this.options) {
            this.options.forEach((opt) => {
              for (const word of this.field.customAttributes.boldWord.split(
                '|'
              )) {
                if (opt.value && opt.value.includes(word))
                  opt.value = opt.value.replace(word, `<b>${word}</b>`);
              }
            });
          }
        }

        //Controlliamo che i format siano tutti valorizzati
        this.setMultipleCss =
          !!this.titleFormat &&
          !!this.titleFormatSelected &&
          !!this.subtitleFormat &&
          !!this.subtitleFormatSelected;

        if (this.field.value)
          this.fieldControl = new FormControl(this.field.value);
        if (this.field.label)
          this.label = this.utilsService.htmlDecode(this.field.label);
        this.community =
          this.componentType && this.componentType === 'Community buttons';

        if (this.field.required) {
          this.required = this.field.required;
          // eslint-disable-next-line @typescript-eslint/unbound-method
          this.fieldControl.setValidators(Validators.required);
        }

        if (this.field.disabled) {
          this.fieldControl.disable();
          this.isDisabled = this.field.disabled;
        }

        // istanza controller radio button
        this.field.reference = this.field.reference ? this.field.reference : '';
        this.statusService.addControlFormGroup(
          this.field.reference,
          this.fieldControl
        );

        this.setValueProcessManagement();
        this.settaValoriGestioneProcesso();
      }

      this.valueReadonly$ = this.field.value
        ? this.getOptionValue(this.field.value)
        : undefined;
      //Gestisce la pressione del click
      this.fieldControl.valueChanges.subscribe(() => {
        this.messageService.onEventExecuted(
          this.actionsSet,
          EventsManaged.change,
          this.processManagement,
          this.gestioneProcesso
        );
      });
    }
  }

  constructor(
    private statusService: StatusService,
    private utilsService: UtilsService,
    public analyticsService: AnalyticsInterprete,
    public override messageService: MessagesService,
    private textHandlerService: TextHandlerService
  ) {
    super(messageService, analyticsService);
  }

  /**
   * Restituisce il valore associato all'option con la chiave inserita
   * @param key Chiave di ricerca per l'option
   */
  public getOptionValue(key: string): string {
    let esito = '';
    for (const option of this.options) {
      if (option.key === key)
        esito = this.utilsService.htmlDecode(option.value);
    }

    return esito;
  }

  /**
   * Controlla che il button value è selezionato oppure no
   * @param buttonValue Valore del button
   */
  public isSelected(buttonValue: string): boolean {
    return this.fieldControl.value === buttonValue;
  }

  /**
   * Apre la select
   */
  public openSelect() {
    this.opened = !this.opened;
  }

  /**
   * Seleziona un determinato item
   * @param item Item da selezionare
   */
  public selectItem(item: IOption) {
    this.itemSelected = item;
    this.opened = false;
    this.fieldControl.setValue(item.key ?? null);
  }

  /**
   * Seleziona un orario
   * @param value Orario da selezionare
   */
  public selectOrario(value: IOption) {
    this.fieldControl.setValue(value.key ?? null);
    this.itemSelected = value;
  }

  /**
   * Espande le agenzie
   */
  public expandAgenzie() {
    this.vediTutte = !this.vediTutte;
  }

  override onClick(option: IOption) {
    if (option.key !== this.fieldControl.value) {
      this.fieldControl.setValue(option.key ?? null);
      this.onChange();
    }
    this.isSelected(option.key ?? '');
    this.messageService.onEventExecuted(
      this.actionsSet,
      EventsManaged.click,
      this.processManagement,
      this.gestioneProcesso
    );
  }

  ngOnDestroy() {
    this.statusService.removeControlFormGroup(this.field.reference ?? '');
  }
}
