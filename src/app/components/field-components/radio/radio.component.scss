@import "../../../../styles.scss";

.wrapper-field {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.radio-button {
  border: white;
  box-shadow: 0 2px 20px 2px rgb(26 26 26 / 16%);
  background-color: white;
}

.radio-vertical {
  .radio-layout {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 2rem;
    //align-items: center;
    margin-top: 2rem;
    margin-bottom: 2rem;
    .card-radio-option {
      //border: solid 2px $soft-grey;
      border: solid 2px gray;
      @media #{$bkp_mobile} {
        width: $width_container_mobile;
        padding: 22px;
      }
      @media #{$bkp_tablet} {
        width: $width_container_tablet;
        padding: 32px 60px;
      }
      @media #{$bkp_desktop} {
        width: $width_container_desktop;
        padding: 32px 60px;
      }
    }

    span.Text-responsive-bold {
      font-family: $font-family-bold;
      color: $main_color;
      line-height: 1;

      @media #{$bkp_mobile} {
        font-size: $font-text-responsive-mobile-size;
      }

      @media #{$bkp_tablet} {
        font-size: $font-text-responsive-tablet-size;
      }

      @media #{$bkp_desktop} {
        font-size: $font-text-responsive-desktop-size;
      }
    }

    .package-radio-option {
      cursor: pointer;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px;
      gap: 16px;
      width: 240px;
      height: 96px;
      background: #ffffff;
      border: 1px solid #cccccc;
      border-radius: 24px;
      position: relative;

      color: $blue-primary;

      ::after {
        content: "";
        position: absolute;
        border-style: solid;
        border: solid transparent;
        height: 0;
        width: 0;
      }

      .radio-input-container {
        input[type="radio"] {
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          cursor: pointer;

          display: inline-block;
          width: 24px;
          height: 24px;
          padding: 2.5px;

          background-clip: content-box;
          border: 2px solid #0f3250;
          border-radius: 50%;
          margin: 0;
        }

        input[type="radio"]:checked {
          background-color: #59a627;
          border: 2px solid #ffffff;
          padding: 0;
        }

        input[type="radio"]:focus {
          outline: none;
        }

        input[type="radio"][disabled]:checked {
          background-color: #cccccc !important;
          cursor: default;
        }

        input[type="radio"]:disabled {
          border: 2px solid #cccccc !important;
          cursor: default;
        }
      }
      &:has(input[type="radio"]:checked) {
        background-color: $blue-primary;
        border-color: $blue-primary;
        .color-opt {
          color: white;
        }
        ::after {
          top: 100%;
          left: 50%;
          border-color: $blue-primary transparent transparent transparent;
          margin-left: -12px;
          border-width: 12px;
        }
      }
    }
  }

  .radio-size {
    width: 500px;
    height: 100px;
    @media only screen and (max-width: 550px) {
      width: 100%;
      min-width: 250px;
      max-width: 90vw;
      height: 70px !important;
    }
  }

  .text-radio-option {
    @media #{$bkp_mobile} {
      font-size: $font-radio-vertical-mobile-size;
    }

    @media #{$bkp_tablet} {
      font-size: $font-radio-vertical-tablet-size;
    }

    @media #{$bkp_desktop} {
      font-size: $font-radio-vertical-desktop-size;
    }
  }
}

.radio-horizontal {
  .radio-layout {
    display: flex;
    flex-direction: row;
    gap: 2.4rem;
    justify-content: center;
    margin-top: 1rem;
    margin-bottom: 1rem;
    .card-radio-option {
      //border: solid 2px $soft-grey;
      border: solid 2px gray;
      @media #{$bkp_mobile} {
        width: $width_container_mobile;
        padding: 22px;
      }
      @media #{$bkp_tablet} {
        width: $width_container_tablet;
        padding: 32px 60px;
      }
      @media #{$bkp_desktop} {
        width: $width_container_desktop;
        padding: 32px 60px;
      }
    }
    span.Text-responsive-bold {
      font-family: $font-family-bold;
      color: $main_color;

      @media #{$bkp_mobile} {
        font-size: $font-text-responsive-mobile-size;
      }

      @media #{$bkp_tablet} {
        font-size: $font-text-responsive-tablet-size;
      }

      @media #{$bkp_desktop} {
        font-size: $font-text-responsive-desktop-size;
      }
    }
    .package-radio-option {
      cursor: pointer;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px;
      gap: 16px;
      width: 240px;
      height: 96px;
      background: #ffffff;
      border: 1px solid #cccccc;
      border-radius: 24px;
      position: relative;

      color: $blue-primary;

      ::after {
        content: "";
        position: absolute;
        border-style: solid;
        border: solid transparent;
        height: 0;
        width: 0;
      }

      .radio-input-container {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-clip: content-box;

        input[type="radio"] {
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          cursor: pointer;

          display: inline-block;
          width: 24px;
          height: 24px;
          padding: 2.5px;

          background-clip: content-box;
          border: 2px solid #0f3250;
          border-radius: 50%;
          margin: 0;
        }

        input[type="radio"]:checked {
          border: none;
          padding: 0;
          aspect-ratio: 1 / 1;
          content: "";
          background-color: $white;
          width: 100%;
          clip-path: polygon(
            28% 38%,
            41% 53%,
            75% 24%,
            86% 38%,
            40% 78%,
            15% 50%
          );
        }

        input[type="radio"]:focus {
          outline: none;
        }

        input[type="radio"][disabled]:checked {
          background-color: #cccccc !important;
          cursor: default;
        }

        input[type="radio"]:disabled {
          border: 2px solid #cccccc !important;
          cursor: default;
        }

        &:has(input[type="radio"]:checked) {
          background-color: $color-green;
          border: 2px solid #ffffff;
        }
      }
      &:has(input[type="radio"]:checked) {
        background-color: $blue-primary;
        border-color: $blue-primary;
        .color-opt {
          color: white;
        }
        ::after {
          top: 100%;
          left: 50%;
          border-color: $blue-primary transparent transparent transparent;
          margin-left: -12px;
          border-width: 12px;
        }
      }
    }
  }

  .radio-size {
    max-width: 450px;
    @media #{$bkp_mobile} {
      width: 43vw;
      min-height: 70px;
    }

    @media #{$bkp_tablet} {
      width: 37vw;
      min-height: 100px;
    }

    @media #{$bkp_desktop} {
      width: 31vw;
      min-height: 100px;
    }
  }
  .radio-size-S {
    max-width: 450px;
    @media #{$bkp_mobile} {
      min-height: 60px;
    }

    @media #{$bkp_tablet} {
      min-height: 100px;
    }

    @media #{$bkp_desktop} {
      min-height: 100px;
    }
  }
  .text-radio-option {
    @media #{$bkp_mobile} {
      font-size: $font-radio-horizontal-mobile-size;
    }

    @media #{$bkp_tablet} {
      font-size: $font-radio-horizontal-tablet-size;
    }

    @media #{$bkp_desktop} {
      font-size: $font-radio-horizontal-desktop-size;
    }
  }
}

.radio-selected {
  background-color: $selected-radio;
  .text-radio-option {
    color: white;
  }
}

.radio-option-container {
  display: flex;
  align-items: center;
  justify-content: center;
  //margin: auto;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  padding: 0 3.25vw;
}

.container-option-community {
  justify-content: flex-start;
}

.text-radio-option {
  font-weight: normal;
  font-family: $font-family-default;
  color: $main_color;
}

.container-radio {
  span,
  .subtitle-radio {
    margin-left: 12px;
    margin-right: 40px;
    font-family: $font-family-default;
    font-size: $font-text-size;
    color: $main_color;
  }

  input[type="radio"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    cursor: pointer;

    display: inline-block;
    width: 20px;
    height: 20px;
    padding: 2.5px;

    background-clip: content-box;
    border: 2px solid #0f3250;
    border-radius: 50%;
    margin: 0;
  }

  input[type="radio"]:checked {
    background-color: $medium-light-blue;
  }

  input[type="radio"]:focus {
    outline: none;
  }

  input[type="radio"][disabled]:checked {
    background-color: #cccccc !important;
    cursor: default;
  }

  input[type="radio"]:disabled {
    border: 2px solid #cccccc !important;
    cursor: default;
  }
}

.item-orario {
  margin: 30px 15px;
  width: 210px;
  height: 105px;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: column;
  cursor: pointer;
}

.icon-Tempo {
  font-size: 50px;
  margin-right: 8px;
  color: $color-darker;
}

.selected {
  border: 1.6px solid #5393bc;
}

.medium {
  font-family: $font-family-medium;
}

.timeSlot {
  display: flex;
  justify-content: space-evenly;

  input[type="radio"] {
    display: none;
  }
}

.select-styled {
  padding: 11px 16px 10px 15px;
  color: $middle-grey;
  width: 226px;
  border: solid 2px #c1c1c1;

  &.isDisabled {
    pointer-events: none;
    color: $middle-grey !important;
    border-color: $middle-grey !important;
  }

  &.active {
    &:after {
      transform: rotate(-45deg) !important;
      top: 19px !important;
    }
  }
}

.triangle-down {
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid $middle-grey;
  position: relative;
  top: 30px;
  left: 88px;
  cursor: pointer;
}

.tpd_selectAngular {
  display: flex;
  flex-direction: column;
  align-items: center;

  &.isDisabled {
    cursor: default !important;
  }
}

li {
  list-style: none;
  padding: 11px 16px 10px 15px;
  color: $middle-grey;
  margin-left: -40px;
  width: 226px;
  border: solid 2px #c1c1c1;
  border-top: none;
}

@media #{$bkp_mobile} {
  .wrapper-button-select {
    display: none;
  }
}

@media #{$bkp_tablet} {
  .wrapper-select-access {
    display: none;
  }

  .wrapper-button-select {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}

@media #{$bkp_desktop} {
  .wrapper-select-access {
    display: none;
  }

  .wrapper-button-select {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}

.disable {
  color: $middle-grey !important;
}

.inline-radio {
  justify-content: start !important;
}

.radio-input-container {
  width: 20px;
  height: 20px;
}

$circle-size: 18px;
$inner-circle-size: 6px;

.blue-border {
  border: solid 1px #0f3250;
}

.rounded-square {
  width: $circle-size;
  height: $circle-size;
  border-radius: 50%;
  background-color: white;
}

.rounded-square-selected {
  background-color: transparent;
  border: solid 1px white;
}

.inner-circle {
  width: $inner-circle-size;
  height: $inner-circle-size;
  border-radius: 50%;
  background-color: white;
}

.radio-button-container {
  position: relative;
  width: $circle-size;
  height: $circle-size;
  cursor: pointer;
  margin-right: 23px;
}
.radio-button-container > * {
  position: absolute;
}

.inner-circle-container {
  left: 0;
  top: 0;
  width: $circle-size;
  height: $circle-size;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

/*FlatCardRadio*/

.FlatCardContainer {
  gap: 16px;
  flex-shrink: 0 !important;
  .FlatCard {
    --cardBorder: #{$border-card-disabled};
    --cardBackground: #{$white};
    --textColor: #{$blue-primary};

    border: solid 1px var(--cardBorder);
    background-color: var(--cardBackground);
    color: var(--textColor);

    min-height: auto !important;
    height: auto !important;

    &:has(label input[type="radio"]:checked) {
      /*Cambio dei colori quando il radio è checckato*/
      --cardBorder: #{$blue-primary};
      --cardBackground: #{$blue-primary};
      --textColor: #{$white};
    }

    > label {
      --markerBorderColor: #{$blue-primary};
      --markerBackgroundColor: #{$white};
      --markerBorderSize: 2px;

      /*Label che contiene i testi*/
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      gap: 8px;
      cursor: pointer;

      min-height: auto !important;
      height: auto !important;
      padding: 16px;

      &:has(input[type="radio"]:checked) {
        /*Cambio dei colori quando il radio è checckato*/
        --markerBorderColor: #{$color-green};
        --markerBackgroundColor: #{$color-green};
        --markerBorderSize: 0;
      }

      > div {
        /*Div che contiene il titolo e sottotitolo*/
        display: flex;
        flex-direction: column;
        gap: 4px;
      }

      &:has(input[type="radio"]:checked) > .hiddenWhenChecked {
        /*Nasconde il contenuto quando il radio è checckato*/
        display: none;
      }

      &:has(input[type="radio"]:not(:checked)) > .hiddenWhenUnchecked {
        /*Nasconde il contenuto quando il radio non è checckato*/
        display: none;
      }

      > .marker {
        /*marker*/
        aspect-ratio: 1 / 1;
        display: flex;
        justify-content: center;
        align-items: center;

        flex-shrink: 0;

        background-color: var(--markerBackgroundColor);
        border: solid var(--markerBorderSize) var(--markerBorderColor);
        border-radius: 300px;
        width: 20px;

        &::after {
          /*Checkmark icon*/
          aspect-ratio: 1 / 1;
          content: "";
          background-color: $white;

          width: 100%;
          clip-path: polygon(
            28% 38%,
            41% 53%,
            75% 24%,
            86% 38%,
            40% 78%,
            15% 50%
          );
        }
      }

      > input[type="radio"] {
        /*Nascondiamo il radio*/
        display: none;
      }
    }
  }
}

/*FlatCardInverted*/
.FlatInvertedCardContainer {
  gap: 16px;
  .FlatInvertedCard {
    --cardBorder: #{$border-card-disabled};
    --cardBackground: #{$white};
    --textColor: #{$blue-primary};

    border: solid 1px var(--cardBorder);
    background-color: var(--cardBackground);
    color: var(--textColor);
    flex-grow: 1;

    min-height: auto !important;
    height: auto !important;

    &:has(label input[type="radio"]:checked) {
      --cardBorder: #{$blue-primary};
      --cardBackground: #{$blue-primary};
      --textColor: #{$white};
    }

    > label {
      --markerBorderColor: #{$blue-primary};
      --markerBackgroundColor: #{$white};
      --markerBorderSize: 2px;

      /*Label che contiene i testi*/
      display: flex;
      align-items: center;
      font-size: 16px;
      gap: 8px;
      cursor: pointer;

      width: 100%;

      height: auto !important;
      min-height: auto !important;
      padding: 16px;

      &:has(input[type="radio"]:checked) {
        --markerBorderColor: #{$color-green};
        --markerBackgroundColor: #{$color-green};
        --markerBorderSize: 0;
      }

      > .marker {
        aspect-ratio: 1 / 1;
        display: flex;
        justify-content: center;
        align-items: center;

        flex-shrink: 0;

        background-color: var(--markerBackgroundColor);
        border: solid var(--markerBorderSize) var(--markerBorderColor);
        border-radius: 300px;
        width: 16px;

        &::after {
          aspect-ratio: 1 / 1;
          content: "";
          background-color: $white;

          width: 100%;
          clip-path: polygon(
            28% 38%,
            41% 53%,
            75% 24%,
            86% 38%,
            40% 78%,
            15% 50%
          );
        }
      }

      > input[type="radio"] {
        display: none;
      }
    }
  }
}

/*RadioCard*/
.radio-card-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  .radio-card {
    height: 56px;
    width: 100%;
    padding: 16px;
    border: solid 1px $border-card-disabled;
    cursor: pointer;
    label {
      font-size: 16px;
      color: $middle-grey-pu;
    }
    &:has(input[type="radio"]:checked) {
      background-color: $blue-primary;
      border: 0;
      label {
        color: #ffffff;
      }
    }
  }

  input {
    &.not-visible {
      display: none;
    }
  }
}

.GreenCheckRadioContainer{
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 24px;

  .GreenCheckRadioItem{
    display: flex;
    flex-direction: row;
    gap: 16px;
    align-items: flex-start;
    cursor: pointer;

    input[type='radio']{
      display: none;
    }

    >.GreenCheck{
      aspect-ratio: 1 / 1;

      display: flex;
      justify-content: center;
      align-items: center;
      border: 2px solid $blue-primary;
      border-radius: 50%;

      width: 24px;

      &::after{
        content: '';
        aspect-ratio: 4 / 2;
        border: 2px solid white;
        border-top: 0;
        border-right: 0;
        width: 30%;
        transform: rotateZ(-45deg);
      }
    }

    &:has(input:checked){
      >.GreenCheck{
        background-color: $check-green-color;
        border-color: $check-green-color;
      }
    }

    >.GreenCheckContentContainer{
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }
  }
}
