<ng-container *ngIf="field">
  <ng-container *ngIf="!this.readOnly; else readOnlyField">
    <div
      [ngClass]="{
        'wrapper-field': radioOrientationClass === 'radio-horizontal'
      }"
      *ngIf="
        componentType === 'Community buttons' ||
        componentType === 'Community buttons S'
      "
    >
      <div class="{{ radioOrientationClass }}">
        <div class="d-flex fd-column" *ngIf="field.label">
          <div *ngIf="!setLabelCss" class="{{ labelFormat$ }}">
            {{ field.label }}
          </div>
          <!--Se arriva lo stile da PEGA-->
          <div *ngIf="setLabelCss">
            <div class="visible-desktop" [ngStyle]="labelCss?.desktopCss">
              {{ field.label }}
            </div>
            <div class="visible-tablet" [ngStyle]="labelCss?.tabletCss">
              {{ field.label }}
            </div>
            <div class="visible-mobile" [ngStyle]="labelCss?.mobileCss">
              {{ field.label }}
            </div>
          </div>
        </div>

        <div splitted_radio_container class="radio-layout">
          <ng-container *ngFor="let opt of options; let last = last">
            <div
              *ngIf="last && separator"
              class="text-radio-option text-align-Center"
            >
              {{ separator }}
            </div>

            <div
              *ngIf="
                radioOrientationClass === 'radio-vertical';
                else horizontal
              "
              class="radio-button radio-size cursor-pointer"
              (click)="onClick(opt)"
              [ngClass]="{ 'radio-selected': isSelected(opt.key) }"
            >
              <div class="radio-option-container" (mouseover)="onHover()">
                <div
                  class="text-radio-option"
                  [innerHTML]="opt.value | safeHtml"
                ></div>
              </div>
            </div>

            <ng-template #horizontal>
              <div
                class="radio-button cursor-pointer"
                (click)="onClick(opt)"
                [ngClass]="{
                  'radio-selected': isSelected(opt.key),
                  'radio-size-S': componentType === 'Community buttons S',
                  'radio-size': componentType === 'Community buttons'
                }"
              >
                <div
                  class="radio-option-container"
                  [ngClass]="{ 'container-option-community': community }"
                  (mouseover)="onHover()"
                >
                  <div
                    *ngIf="componentType === 'Community buttons'"
                    class="radio-button-container"
                  >
                    <div
                      class="inner-circle-container blue-border rounded-square"
                      [ngClass]="{
                        'rounded-square-selected': isSelected(opt.key)
                      }"
                    >
                      <div class="inner-circle"></div>
                    </div>
                  </div>

                  <div
                    class="text-radio-option"
                    [innerHTML]="opt.value | safeHtml"
                  ></div>
                </div>
              </div>
            </ng-template>
          </ng-container>
        </div>
      </div>
    </div>

    <div *ngIf="componentType === 'Radio'" class="pdn-label-input">
      <div class="container-radio {{ radioOrientationClass }}">
        <div
          class="{{ labelFormat$ }} mrg-label-input"
          [ngClass]="{ disable: isDisabled }"
        >
          {{ field.label }}
        </div>
        <div splitted_radio_container class="d-flex radio-layout inline-radio">
          <div class="d-flex" *ngFor="let opt of options; index as i">
            <input
              name="{{ field.fieldID }}"
              type="radio"
              id="{{ opt.value }}"
              [value]="opt.key"
              [formControl]="fieldControl"
              [required]="required"
              [placeholder]="label"
              class="input-radio-hidden"
              (click)="onClick(opt)"
            />
            <div>
              <span (click)="onClick(opt)" class="{{ labelOptStyle }}">{{
                opt.value | titlecase
              }}</span>
              <p *ngIf="subtitle && subtitle[i]" class="subtitle-radio">
                {{ subtitle[i] }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <ng-container *ngIf="componentType === 'TimeSlot'">
      <div class="wrapper-button-select">
        <div class="{{ labelFormat$ }} mrg-label-input text-align-Center">
          {{ field.label }}
        </div>
        <div splitted_radio_container class="timeSlot">
          <div
            class="item-orario"
            *ngFor="let opt of options"
            (click)="selectOrario(opt)"
            [ngClass]="{ selected: fieldControl.value === opt.key }"
          >
            <input
              name="{{ field.fieldID }}"
              type="radio"
              id="{{ opt.value }}"
              [value]="opt.key"
              [formControl]="fieldControl"
              [required]="required"
              [placeholder]="label"
            />
            <div class="icon-Tempo"></div>
            <span
              class="value"
              [ngClass]="{ medium: fieldControl.value === opt.key }"
              >{{ opt.value | titlecase }}</span
            >
          </div>
        </div>
      </div>

      <div class="wrapper-select-access">
        <div class="select">
          <div class="{{ labelFormat$ }} mrg-label-input text-align-Center">
            {{ field.label }}
          </div>
        </div>
        <div>
          <div class="tpd_selectAngular" [ngClass]="{ isDisabled: isDisabled }">
            <div class="triangle-down"></div>
            <div
              class="select-styled"
              [innerHtml]="
                itemSelected ? (itemSelected.value | titlecase) : placeholder
              "
              (click)="openSelect()"
              [ngClass]="{ active: opened, isDisabled: isDisabled }"
            ></div>
            <ul class="select-options" [hidden]="!opened">
              <li *ngFor="let opt of options" (click)="selectItem(opt)">
                {{ opt.value | titlecase }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </ng-container>

    <div *ngIf="componentType === 'Card'" class="pdn-label-input">
      <div class="container-radio {{ radioOrientationClass }}">
        <div class="{{ labelFormat$ }} mrg-label-input">{{ field.label }}</div>
        <div splitted_radio_container class="d-flex radio-layout">
          <div
            class="d-flex card-radio-option"
            *ngFor="
              let opt of this.options
                | slice : 0 : (vediTutte ? undefined : maxVisibleElement);
              index as i
            "
          >
            <div class="radio-input-container">
              <input
                name="{{ field.fieldID }}"
                type="radio"
                id="{{ opt.value }}"
                [value]="opt.key"
                [formControl]="fieldControl"
                [required]="required"
                [placeholder]="label"
                class="input-radio-hidden"
              />
            </div>
            <div>
              <span (click)="onClick(opt)" class="{{ labelOptStyle }}">{{
                opt.value | titlecase
              }}</span>
              <p *ngIf="subtitle && i === 0" class="subtitle-radio">
                {{ subtitle }}
              </p>
            </div>
          </div>
          <div
            class="{{ labelShowStyle }} cursor-pointer text-align-Center"
            *ngIf="
              customAttributes.textShowMore && options && options.length > 3
            "
            (click)="expandAgenzie()"
          >
            {{ vediTutte ? labelShowLess : labelShowMore }}
          </div>
        </div>
      </div>
    </div>

    <div
      splitted_radio_container
      *ngIf="this.componentType === 'FlatCard' && this.options"
      class="FlatCardContainer d-flex"
      [style.flex-direction]="this.radioOrientation.toLowerCase() === 'horizontal' ? 'row' : 'column'">
      <div
        *ngFor="let option of this.options; index as i"
        class="FlatCard"
        [style.flex-grow]="this.radioOrientation.toLowerCase() === 'horizontal' ? '1' : 'unset'">
        <label>
          <input
            name="{{ field.fieldID }}"
            type="radio"
            id="{{ option.value }}"
            value="{{ option.key }}"
            [formControl]="this.fieldControl" />
          <div *ngIf="!this.setMultipleCss">
            <span>{{ option?.value }}</span>
            <span>{{this.subtitle && this.subtitle[i] ? this.subtitle[i] : ""}}</span>
          </div>
          <div *ngIf="this.setMultipleCss" class="hiddenWhenChecked">
            <custom-text-style
              [textCss]="this.titleFormat"
              [content]="option?.value || ''">
            </custom-text-style>
            <custom-text-style
              [textCss]="this.subtitleFormat"
              [content]="this.subtitle && this.subtitle[i] ? this.subtitle[i] : ''">
            </custom-text-style>
          </div>
          <div class="marker"></div>
        </label>
      </div>
    </div>

    <div
      splitted_radio_container
      *ngIf="
        (this.componentType === 'Flat Card Inverted' ||
          this.componentType === 'FlatCardInverted') &&
        this.options
      "
      class="FlatInvertedCardContainer d-flex"
      [style.flex-direction]="
        this.radioOrientation.toLowerCase() === 'horizontal' ? 'row' : 'column'
      "
    >
      <div *ngFor="let option of this.options" class="FlatInvertedCard">
        <label>
          <div class="marker"></div>
          <input
            name="{{ field.fieldID }}"
            type="radio"
            id="{{ option.value }}"
            value="{{ option.key }}"
            [formControl]="this.fieldControl"
          />
          {{ option.value }}
        </label>
      </div>
    </div>

    <div
      splitted_radio_container
      class="radio-card-container"
      *ngIf="this.componentType === 'RadioCard' && this.options"
    >
      <div *ngFor="let option of options">
        <div class="radio-card" (click)="onClick(option)">
          <input
            type="radio"
            class="not-visible"
            [name]="this.field.fieldID"
            [id]="option.key"
            [value]="option.key"
            [formControl]="this.fieldControl" />
          <label>{{ option.value }}</label>
        </div>
      </div>
    </div>

    <div *ngIf="componentType === 'Package'" class="pdn-label-input">
      <div class="container-radio {{ radioOrientationClass }}">
        <div class="{{ labelFormat$ }} mrg-label-input">{{ field.label }}</div>
        <div splitted_radio_container class="radio-layout">
          <div
            class="package-radio-option"
            (click)="onClick(opt)"
            *ngFor="let opt of options; index as i"
          >
            <div class="radio-input-container">
              <input
                name="{{ field.fieldID }}"
                type="radio"
                id="{{ opt.value }}"
                [value]="opt.key"
                [formControl]="fieldControl"
                [required]="required"
                [placeholder]="label"
                class="input-radio-hidden"
              />
            </div>
            <div>
              <div class="{{ labelOptStyle }} color-opt">
                {{ opt.value | uppercase }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      *ngIf="this.componentType === 'GreenCheck'"
      class="GreenCheckRadioContainer">
      <label
        *ngFor="let opt of options; index as i"
        class="GreenCheckRadioItem"
        (click)="this.onClick(opt)">
        <input
          type="radio"
          [name]="this.field.fieldID"
          [id]="opt.key"
          [value]="opt.key"
          [formControl]="this.fieldControl" />
        <span class="GreenCheck"></span>
        <span class="GreenCheckContentContainer">
          <custom-text-style
            [content]="opt.value"
            [textCss]="this.titleFormat">
          </custom-text-style>
          <custom-text-style
            *ngIf="this.subtitle && this.subtitle[i]"
            [content]="this.subtitle[i]"
            [textCss]="this.subtitleFormat">
          </custom-text-style>
        </span>
      </label>
    </div>
  </ng-container>
  <ng-template #readOnlyField>
    <span
      *ngIf="!this.setLabelCss; else customPegaReadonly"
      class="{{ this.labelFormat$ }}"
      >{{ this.valueReadonly$ }}</span
    >
    <ng-template #customPegaReadonly>
      <custom-text-style
        [textCss]="this.labelCss"
        [content]="this.valueReadonly$"
      ></custom-text-style>
    </ng-template>
  </ng-template>
</ng-container>
