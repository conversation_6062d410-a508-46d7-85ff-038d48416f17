import { <PERSON>mpo<PERSON>, ElementRef, Input, On<PERSON><PERSON>roy } from "@angular/core";
import { AbstractControl, FormControl, FormGroup } from "@angular/forms";
import { TextCss, TextHandlerService } from "../../../services/text-handler.service";
import { IActionSet, IControl, IField } from "../../../models/models.commons";
import { AnalyticsInterprete } from "../../../services/analytics.interprete.service";
import { DocumentsService } from "../../../services/documents.interprete.service";
import { MessagesService } from "../../../services/messages.service";
import { StatusService } from "../../../services/status.service";
import { UtilsService } from "../../../services/utils.service";
import { EventsManaged } from "../../../utils/events.enum";
import { EventsMethod } from "../../../utils/eventsMethod";
import { Helpers } from "@tpd-web-common-libs/nodejs-library";
import { ButtonLinkType } from "./button.model";
import { CartService } from "../../../services/cart.service";
import UserDataService from "../../../services/user-data.service";
import ComponentReferenceStorageService from "../../../services/component-reference-storage.service";
import { TpdInterpreteDxApi } from "../../../tpd-interprete-angular-dx-api-pu/tpd-interprete-angular-dx-api-pu";
import { PEGA_API } from "../../../services/bffinterprete.service";
import { GenericDataType } from "../../../models/bff.interprete.model";
import {
  TpdRecaptchaFactoryService,
  TpdRecaptchaResponseClass,
  TpdRecaptchaStatus
} from '@tpd-web-angular-libs/angular-library'
import { Subscription } from "rxjs";

interface ButtonComponentCustomAttributes {
  labelFormatUnselected?: string;           //Formato della label quando il button non è selezionato con controlFormat Frazionamento
  labelFormatSelected?: string;             //Formato della label quando il button è selezionato con controlFormat Frazionamento
  subtitle?: string;                        //Sottotitolo da visualizzare nella situazione in cui ci sono bottoni di frazionamento
  subtitleLabelFormat?: string;             //Formato del sottotitolo quando presente
  subtitleLabelFormatSelected?: string;     //Formato del sottotitolo quando presente e selezionato con controlFormat Frazionamento
  typeButton?: string;                      //Nel controlFormat Frazionamento il typeButton indica il tipo di frazionamento 9Mensile, 1Annuale, ...
  frazionamento?: string;                   //Nel controlFormat Frazionamento se valorizzato ad 1 indica che è selezionato quel particolare button

  linkType?: string;                        //Tipo di link da aprire, ad esempio se si deve andare alla login al click o altro. Indica nei button con link dove bisogna essere reindirizzati
  link?: string;                            //Link dove essere reindirizzti. In mancanza di linkType si usa per effettuare il redirect o nel caso in cui CallService esiste per effettuare il download di un pdf
  pdfType?: string;                         //Tipo di documento che bisogna gestire
  linkWeb?: string;                         //Uguale per sopra ma valido solo per web
  CallService?: string;                     //Se impostato su true chiamiamo il servizio di donwload del servizio

  VediEModifica?: string;                   //Riferimento del Vedi e modifica

  captchaControl?: string;                  //Indica se il button deve essere usato come button di controllo per il captcha
  submitButton?: string;                    //Indica se il button deve essere usato come button di submit o submit per i form indipendenti

  redirect_login_button?: string;           //Indica se il button è un button di redirect alla login

  onCloseRefresh?: string;                  //Indica se invocare il refresh della pagina ad esempio alla chiusura di una modale
  additionalRefreshData?: string;           //Dati aggiuntivi per il refresh

  closeModal?: string;
  componentID?: string;                     //Id della tipologia di button

  FiscalCode?: string;                      //Codice fiscale da utilizzare per una nuova quotazione
  shouldCenterButton?: string;              //Se presente come CA farà in modo che il button venga visualizzato al centro

  failureReason?: string;                   //Ragione di fallimento
  replaceActionId?: string;                 //Replace dell'action id
  buttonIconsWeb?: string;                  //Icona da visualizzare al fianco del button

  GO_TO_QUOTATION?: string;                 //Indica su quale quotazione reindirizzare l'utente

  specialLoading?: string;                  //Indica se chiamare oppure no la special loading
  loadingList?: 'warranty' | 'showPrice';   //Indica il tipo di loading speciale da chiamare
}

@Component({
  selector: 'tpd-button',
  templateUrl: './button.component.html',
  styleUrls: ['./button.component.scss'],
})
export class ButtonComponent extends EventsMethod implements OnDestroy{
  private _data: { field: IField };
  public fg: FormGroup;

  public customAttributes: ButtonComponentCustomAttributes & any;
  public override actionsSet: IActionSet[] = [];
  public control: IControl;

  public formIndex = -1;
  public isButtonSubmit = false;
  public formArrayChild: AbstractControl;

  public isRichiediContattoButton = false;
  public richiediContattoData: GenericDataType;

  public isDisabled = false;
  public isVisible = false;
  public isCenter = false;

  public labelButton = '';
  public subtitle = '';
  public labelFormatClass = '';
  public labelFormatCss: TextCss;
  public labelFormatSelected: TextCss;
  public labelFormatUnselected: TextCss;
  public subtitleLabelFormat: TextCss;
  public subtitleLabelFormatSelected: TextCss;

  public labelFormatDownloadButtonCss = this.textHandlerService.getTextCss('TEXT APP BDB16 WEB BDL16 BDL16 BDL16');

  public captchaControl = false;
  public idRecaptcha = `${Date.now()}-Interprete-Pu`;
  public captchaSubscription: Subscription;
  public captchaValido = false;

  public mostraLoadingCustom = false;
  public stileLoadingCustom: "warranty" | "showPrice";
  public refreshOnClose = false;

  public link = '';
  public linkType: ButtonLinkType | undefined;
  public target: '_blank' | '_self' = '_blank';
  public docTitle = '';

  public goToQuotationTarget: string;

  public redirect_login_button = false; //Indica se il button è un button di redirect al login

  public callbackByComponentID: (arg?: boolean) => Promise<boolean>;
  public callbackAfterRetrieveByComponentID: (arg?: boolean) => void;

  public customClickCallback: {callback: () => void, ignoraFunzionamentoStandard: boolean};
  public additionalRefreshData: string;

  public vediEModifica: string;
  public vediEModificaReference: string;
  public garanzia = {};
  public garanzie: Array<typeof this.garanzia> = [];

  public pxIconVisualizzataButton: IField;

  constructor(
    private utilsService: UtilsService,
    public override messageService: MessagesService,
    public analyticsService: AnalyticsInterprete,
    private documentService: DocumentsService,
    private statusService: StatusService,
    private textHandlerService: TextHandlerService,
    private _cartService: CartService,
    private _userDataService: UserDataService,
    private _componentReferenceStorageService: ComponentReferenceStorageService,
    private _recaptchaFactoryService: TpdRecaptchaFactoryService,
    private _parentReference: ElementRef,
  ) {
    super(messageService, analyticsService);
  }

  @Input() set data(input: { field: IField }) {
    this._data = input;
    this.field = input.field;

    if (this.field) {
      this.control = this.field.control;
      this.customAttributes = this.field.customAttributes;

      this.isVisible = this.field.visible;
      this.isDisabled = this.field.disabled;
      this.fg = this.statusService.getFormGroup();

      //Registrazione della reference
      this.field?.elementUniqueId && this._componentReferenceStorageService.storeComponent(this?.field?.elementUniqueId, this);

      //Recupero dei custom attributes
      if (this.customAttributes) {
        this.redirect_login_button = !!this.customAttributes.redirect_login_button;
        this.goToQuotationTarget = this.customAttributes.GO_TO_QUOTATION;
        this.linkType = this.customAttributes.linkType as ButtonLinkType;
        this._initCaptchaControlButton();
        this._initSubmitButton();
        this._initRichiestaContattoButton();

        this.mostraLoadingCustom = this.customAttributes?.specialLoading === 'true';
        this.stileLoadingCustom = this.customAttributes?.loadingList || 'showPrice';

        this.link = this.customAttributes.link || this.utilsService.htmlDecode(this.customAttributes.linkWeb);
        if (this.customAttributes.linkWeb)
          this.docTitle = this.link.split('=')[1];

        this.vediEModifica = this.customAttributes.VediEModifica;
        if (this.field.reference && this.vediEModifica) {
          this.vediEModificaReference =
            this.field.reference.substring(0, this.field.reference.lastIndexOf('.')) + '.VediEModifica';
          this.garanzia[this.vediEModificaReference] = this.vediEModifica;
          this.garanzie.push(this.garanzia);
        }
        this.refreshOnClose = !!this.customAttributes.onCloseRefresh;

        this.additionalRefreshData = this.customAttributes.additionalRefreshData;
        if (this.additionalRefreshData)
          this.statusService.addControlFormGroup(this.additionalRefreshData, new FormControl(true));

        if(this.customAttributes.closeModal === 'true')
          this.callbackByComponentID = this.utilsService.getCustomCallbackByComponentID('closeModal');

        if (this.customAttributes.componentID) {
          this.callbackByComponentID = this.utilsService.getCustomCallbackByComponentID(this.customAttributes.componentID);
          this.callbackAfterRetrieveByComponentID = this.utilsService.getCallbackAfterRetrieveByComponentID(this.customAttributes.componentID);
        }

        this.isCenter = !!this.customAttributes.shouldCenterButton;
        if(this.isCenter && this._parentReference.nativeElement){
          const fieldComponent = (this._parentReference.nativeElement as HTMLElement).parentElement;
          fieldComponent.style.display = "flex";
          fieldComponent.style.flexDirection = "row";
          fieldComponent.style.justifyContent = "center";
          fieldComponent.style.width = "100%";
        }

        if(this.customAttributes.buttonIconsWeb){
          this.pxIconVisualizzataButton = {
            customAttributes: {
              resource: this.customAttributes.buttonIconsWeb,
              webResponsiveSize: '24C 24C 24C'
            }
          }
        }
      }

      if (this.control && this.control.modes) {
        this.control.modes.forEach((mode) => {
          if (mode && mode.controlFormat)
            this.labelFormatClass = this.utilsService.getClassFromFormat(
              mode.controlFormat
            );
        });
        this.labelButton = this.utilsService.htmlDecode(
          this.control.label ?? ''
        );
        this.actionsSet = this.control.actionSets ?? [];
      }

      if (this.field.labelFormat)
        this.labelFormatCss = this.textHandlerService.getTextCss(this.field.labelFormat);

      this.subtitle = this.customAttributes?.subtitle || '';
      this.labelFormatSelected = this.textHandlerService.getTextCss(this.customAttributes?.labelFormatSelected);
      this.labelFormatUnselected = this.textHandlerService.getTextCss(this.customAttributes?.labelFormatUnselected);
      this.subtitleLabelFormat = this.textHandlerService.getTextCss(this.customAttributes?.subtitleLabelFormat);
      this.subtitleLabelFormatSelected = this.textHandlerService.getTextCss(this.customAttributes?.subtitleLabelFormatSelected);

      this.setValueProcessManagement();
      this.settaValoriGestioneProcesso();
    }
  }

  private _initSubmitButton(){
    this.formIndex = this.statusService.currentStandAloneFormIndex;
    if(this.customAttributes){
      const submitButtonValue = this.customAttributes.submitButton;
      if(submitButtonValue){
        this.isButtonSubmit = true;
        if(submitButtonValue && this.statusService.standAloneForm && this.formIndex >= 0){
          this.formArrayChild = this.statusService.getFormArrayChildByIndex(this.formIndex);
          this.statusService.impostaStatoErroreInput(false, this.formIndex);
          this.statusService.deinizializzaFormIndipendente();
        }
      }
    }
  }

  private _captchaFunction(){
    if (this.statusService.isValidForm()) {
      this.statusService.impostaStatoErroreInput(false, this.formIndex);
      this.messageService.onEventExecuted(
        this.actionsSet,
        EventsManaged.click,
        this.processManagement,
        this.gestioneProcesso
      );
    } else {
      this.isButtonSubmit &&
      this.statusService.impostaStatoErroreInput(true, this.formIndex);
    }
  }

  private _captchaDispatcher(){
    if(this.statusService.modalitaRedirect){
      this._captchaFunction();
    }else{
      if(this.captchaValido){
        this._captchaFunction();
      }else{
        this._recaptchaFactoryService.executeReCaptcha(this.idRecaptcha);
      }
    }
  }

  private _initCaptchaControlButton(){
    this.captchaControl = !!this.customAttributes.captchaControl;
    if(this.captchaControl){
      this.captchaSubscription = this._recaptchaFactoryService.createRecaptcha(this.idRecaptcha).subscribe((response: TpdRecaptchaResponseClass) => {
        if(response.status === TpdRecaptchaStatus.OK){
          this.captchaValido = true;
          this.statusService.captchaToken = response.token;
          this._captchaDispatcher();
        } else{
          this.captchaValido = false;
        }
      });
    }
  }

  private _initRichiestaContattoButton(){
    if(this.customAttributes && this.customAttributes['GestioneProcesso.StepSuccessivo']){
      this.isRichiediContattoButton = this.customAttributes['GestioneProcesso.StepSuccessivo'] === 'RichiediContatto';
      if(Object.keys(this.customAttributes).length > 1){
        this.richiediContattoData = {};
        for(const key of Object.keys(this.customAttributes)){
          if(key !== 'GestioneProcesso.StepSuccessivo'){
            this.richiediContattoData[key] = this.customAttributes[key];
          }
        }
      }
    }
  }

  override async onClick() {
    this.settaValoriGestioneProcessoDaStorage();
    if(this.customClickCallback?.ignoraFunzionamentoStandard === undefined || !this.customClickCallback?.ignoraFunzionamentoStandard) {
      this.messageService.customLoadingFlag = this.mostraLoadingCustom;
      this.messageService.customLoadingType = this.stileLoadingCustom;

      if (this.customAttributes?.replaceActionId)
        this.statusService.setReplaceActionId(this.customAttributes.replaceActionId);

      this.checkSendAnalytics();

      let casoChiamataSpecifico = false;
      if(this.redirect_login_button){
        casoChiamataSpecifico = true;
        await this._onClickLoginButton();
      }

      if(!casoChiamataSpecifico && this.linkType){
        casoChiamataSpecifico = true;
        await this._onClickLinkButton();
      }

      if(!casoChiamataSpecifico && this.captchaControl){
        casoChiamataSpecifico = true;
        this._captchaDispatcher();
      }

      if(!casoChiamataSpecifico && this.isRichiediContattoButton){
        casoChiamataSpecifico = true;
        if(typeof this.callbackByComponentID === 'function')
          await this.callbackByComponentID();
        TpdInterpreteDxApi.TpdInterpreteDxApiContext.chiamaRichiestaDiContatto(this.richiediContattoData);
      }

      if(!casoChiamataSpecifico && typeof this.callbackByComponentID === 'function'){
        casoChiamataSpecifico = true;
        await this._onClickCallbackButton();
      }

      if(!casoChiamataSpecifico && this.goToQuotationTarget){
        casoChiamataSpecifico = true;
        this._onClickGoToQuotationTarget();
      }

      if(!casoChiamataSpecifico){
        if(this.isButtonSubmit){
          if (this.formIndex >= 0) {
            this.formArrayChild.updateValueAndValidity();
            this.formArrayChild.markAllAsTouched();
          } else {
            this.statusService.getFormArray().updateValueAndValidity();
            this.statusService.getFormGroup().updateValueAndValidity();
            this.statusService.getFormArray().markAllAsTouched();
            this.statusService.getFormGroup().markAllAsTouched();
          }

          const isFormValid = this.formIndex >= 0 ? this.formArrayChild.valid : this.statusService.isValidForm();
          if(isFormValid){
            this.statusService.impostaStatoErroreInput(false, this.formIndex);
            this.messageService.onEventExecuted(
              this.actionsSet,
              EventsManaged.click,
              this.processManagement,
              this.gestioneProcesso,
              undefined,
              this.callbackAfterRetrieveByComponentID,
            );
          }else{
            this.statusService.impostaStatoErroreInput(true, this.formIndex);
          }
        }else{
          this.statusService.impostaStatoErroreInput(false, this.formIndex);
          this.messageService.onEventExecuted(
            this.actionsSet,
            EventsManaged.click,
            this.processManagement,
            this.gestioneProcesso,
            undefined,
            this.callbackAfterRetrieveByComponentID,
            undefined,
            this.garanzie
          );
        }
      }
    }
    this.customClickCallback?.callback && this.customClickCallback.callback();
  }

  private async _onClickLoginButton(){
    this.messageService.setLoading(true);

    try {
      await Helpers.SessionHelper.setData(
        'redirect_login',
        JSON.stringify({
          assignmentId: this.statusService.assignmentId,
          caseId: this.statusService.caseID,
        })
      );
    } catch (e) {
      console.error('Errore nel salvataggio dei dati riguardanti il redirect_login', e);
    } finally {
      Helpers.EnvironmentHelper.isServerSide() && Helpers.RouterHelper.goTo(`/accesso?prodotto-unico=true&redirect-url=${window.location.pathname}`);
    }

    this.analyticsService.setStorageAnalytics('ANALYTICS_AFTER_LOGIN');
  }

  private async _onClickLinkButton(){
    switch (this.linkType) {
      case 'GO_TO_HOME':
        Helpers.RouterHelper.goTo('/');
        break;
      case 'EXIT_FLOW':
        Helpers.RouterHelper.goTo('/');
        break;
      case 'GO_TO_LOGIN':
        Helpers.RouterHelper.goTo('/accesso');
        break;
      case 'GO_TO_QUOTATION_HOME':
        if(Helpers.LoginHelper.userLogged)
          Helpers.RouterHelper.goTo('/area_riservata/preventivi');
        else Helpers.RouterHelper.goTo('/recupera-preventivo');
        break;
      case 'GO_TO_FAILURE_PAGE': {
        const gestioneProcesso: GenericDataType = {};
        Object.keys(this.customAttributes)
          .filter(chiave => chiave.startsWith("GestioneProcesso"))
          .forEach(chiave => gestioneProcesso[chiave] = this.customAttributes[chiave]);
        TpdInterpreteDxApi.TpdInterpreteDxApiContext.chiamaPaginaErroreInterna(gestioneProcesso, this.customAttributes?.failureReason, this.customAttributes?.replaceActionId);
        break;
      }case 'GO_TO_REGISTRATION':
        Helpers.RouterHelper.goTo('/registrazione-cliente?uri_from=accesso');
        break;
      case 'GO_TO_APP':
        Helpers.RouterHelper.goTo('/app');
        break;
      case 'GO_TO_NEW_QUOTATION': {
        if (this.customAttributes.FiscalCode) {
          const data = { codiceFiscaleContraente: this.customAttributes.FiscalCode };
          try {
            await Helpers.SessionHelper.setData('tpd_disambiguazione_widget_data_prop', JSON.stringify(data));
          } catch (e) {
            console.error('Errore nel salvataggio dei dati da spedire a disambiguazione', e);
          }
        }
        Helpers.RouterHelper.goTo('/disambiguazione');
        break;
      }
      default:
        if (this.link) {
          if (this.customAttributes?.CallService) {
            this.documentService.downloadPdf(this.link, this.docTitle);
          } else if(this.customAttributes.pdfType === 'OUTBOUND_DYNAMIC') {
            const escapedLink = encodeURIComponent(this.utilsService.htmlDecode(this.link));
            this.documentService.downloadDocumentoPu(escapedLink, this.labelButton);
          }else{
            Helpers.RouterHelper.goTo(this.link, { openNewTab: true });
          }
        }
    }
  }

  private async _onClickCallbackButton(){
    if (this.customAttributes?.componentID === 'confirmAddressButton') {
      this.callbackByComponentID().then((response: boolean) => {
        if (response) {
          this.messageService.onEventExecuted(
            this.actionsSet,
            EventsManaged.click,
            this.processManagement,
            this.gestioneProcesso,
            undefined,
            this.callbackAfterRetrieveByComponentID
          );
        }
      });
    } else if ( ['closeModalAndRefreshPage', 'closeModalAndReloadPage'].includes(this.field?.customAttributes.componentID)) {
      this.messageService.onEventExecuted(
        this.actionsSet,
        EventsManaged.click,
        this.processManagement,
        this.gestioneProcesso,
        undefined,
        this.field?.customAttributes.componentID
      );
    } else {
      await this.callbackByComponentID(this.refreshOnClose);
      this.messageService.onEventExecuted(
        this.actionsSet,
        EventsManaged.click,
        this.processManagement,
        this.gestioneProcesso,
        undefined,
        (arg?: boolean) => {
          this.callbackAfterRetrieveByComponentID && this.callbackAfterRetrieveByComponentID(arg);
        }
      );
    }
  }

  private async _onClickGoToQuotationTarget(){
    const productType = {
      'AUTO': 'PUAUTO',
      'CASA': 'PUCASA',
      'FAMIGLIA': 'PUFAMIGLIA',
      'INFORTUNI': 'PUINFORTUNI',
      'MOBILITA': 'PUMOBILITA',
      'PET': 'PUPET',
      'SALUTE': 'PUSALUTE',
      'VIAGGIA': 'PUVIAGGIA',
      'VIAGGIT': 'PUVIAGGIT',
      'Casa': 'PUCASA',
      'Veicoli': 'PUAUTO',
      'Infortuni': 'PUINFORTUNI',
      'Salute': 'PUSAL',
      'Viaggi': 'PUVIAGGIA',
      'Viaggio': 'PUVIAGGIA',
      'Famiglia': 'PUFAMIGLIA',
      'Pet': 'PUPET',
      'Mobilità': 'PUMOBILITA',
      'Mobilita': 'PUMOBILITA',
    }[this.goToQuotationTarget] || '';

    if(this.customAttributes['GestioneProcesso.flagBundle'] && this.customAttributes['GestioneProcesso.caseBundle']){
      const gestioneProcesso = {
        'GestioneProcesso.flagBundle': this.customAttributes['GestioneProcesso.flagBundle'],
        'GestioneProcesso.caseBundle': this.customAttributes['GestioneProcesso.caseBundle']
      }

      TpdInterpreteDxApi.TpdInterpreteDxApiContext.chiamaCustomCreate(productType, gestioneProcesso);
    }else{
      const errorePresenteChiamata = await TpdInterpreteDxApi.TpdInterpreteDxApiContext.retrieveRequest(PEGA_API.nextPage, 'Chiamata silente pega', true)
      if(!errorePresenteChiamata){
        try{
          const datiPerDisambiguazione = {
            ambitoQuotazioneRichiesto: productType
          }
          if(!Helpers.LoginHelper.userLogged)
            datiPerDisambiguazione['codiceFiscaleContraente'] = await this._userDataService.storedCodiceFiscale();

          await Helpers.SessionHelper.setData('tpd_disambiguazione_widget_data_props', datiPerDisambiguazione);
          Helpers.RouterHelper.goTo('/disambiguazione');
        }catch (e){
          console.error("Errore nel trasferimento alla nuova quotazione", e);
        }
      }
    }
  }

  public gestisciClickCaptchaControl(tipoLink: 'privacy' | 'termini'){
    switch (tipoLink){
      case "privacy":
        Helpers.RouterHelper.goTo('https://www.google.com/intl/it/policies/privacy', {openNewTab: true});
        break;
      case "termini":
        Helpers.RouterHelper.goTo('https://www.google.com/intl/it/policies/terms', {openNewTab: true});
        break;
    }
  }

  //region FrazionamentoButton

  public get isFrazionamentoButton(): boolean{
    return !!this.customAttributes?.typeButton;
  }

  public get formatoTestoFrazionamentoButton(): TextCss{
    let esito = undefined;

    if(this.isFrazionamentoButton)
      esito = this.customAttributes?.frazionamento === this.customAttributes?.typeButton ?
        this.labelFormatSelected : this.labelFormatUnselected;

    return esito;
  }

  public get isFrazionamentoButtonSelected(): boolean{
    return this.isFrazionamentoButton && this.customAttributes?.frazionamento === this.customAttributes?.typeButton;
  }

  public get formatoSottotitoloFrazionamentoButton(): TextCss{
    let esito = undefined;

    if(this.isFrazionamentoButton && this.subtitle && this.subtitleLabelFormat && this.subtitleLabelFormatSelected){
      esito = this.customAttributes?.frazionamento === this.customAttributes?.typeButton ?
        this.subtitleLabelFormatSelected : this.subtitleLabelFormat;
    }

    return esito;
  }

  //endregion

  public ngOnDestroy(): void {
    if(this.captchaControl){
      this.captchaSubscription && this.captchaSubscription.unsubscribe();
      this._recaptchaFactoryService.resetRecaptcha(this.idRecaptcha);
      this._recaptchaFactoryService.destroyIds(this.idRecaptcha);
    }
  }
}
