@import "../../../../styles.scss";

.InlineIconContainer{
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
}

.Positive-Button,
.PositiveTPD-Button {
  width: 206px;
  height: 50px;
  background-color: $main_red;
  object-fit: contain;
  cursor: pointer;
  border: none;

  &.disabled {
    opacity: 0.3;
    cursor: default;
  }

  .button-text {
    font-family: $font-family-bold;
    font-size: $font-text-promo-size;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: white;
  }
}

.Full-Positive-Button {
  @extend .PositiveTPD-Button;
  width: 100%;
  min-width: 240px;
  max-width: 100%;
  text-align: center !important;
  @media #{$bkp_mobile_only} {
    min-width: 120px;
  }
}

.Cancel-button {
  display: flex;
  justify-content: center;
  align-items: center;

  margin-top: 1rem;
  margin-bottom: 1rem;
  width: 260px;
  height: 50px;
  background-color: $dark-light_blue;
  object-fit: contain;
  cursor: pointer;
  border: none;

  &.disabled {
    opacity: 0.3;
  }

  .button-text {
    font-family: $font-family-bold;
    font-size: $font-text-size;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: white;
  }
}

.Full-Cancel-Button,
.Full-Cancel-button {
  @extend .Cancel-button;
  margin-top: 0;
  margin-bottom: 0;
  width: 100%;
  min-width: 240px;
  @media #{$bkp_mobile_only} {
    min-width: 120px;
  }
}

.Community-buttons,
.CommunityTPD-buttons {
  width: 500px;
  height: 100px;

  @media only screen and (max-width: 550px) {
    width: 100%;
    height: 70px !important;
  }

  border: white;
  box-shadow: 0 2px 20px 2px rgb(26 26 26 / 16%);
  background-color: white;
  margin-top: 2rem;
  margin-bottom: 2rem;

  .button-text {
    font-weight: normal;
    font-family: $font-family-default;
    color: $main_color;

    @media #{$bkp_mobile} {
      font-size: $font-radio-vertical-mobile-size;
    }

    @media #{$bkp_tablet} {
      font-size: $font-radio-vertical-tablet-size;
    }

    @media #{$bkp_desktop} {
      font-size: $font-radio-vertical-desktop-size;
    }
  }
}

.Back {
  background-color: transparent;
  border: none;

  .button-text {
    font-family: $font-family-bold;
    font-size: $font-text-size;
    color: $main_color;
    font-weight: 500;
    line-height: 1.25;
    cursor: pointer;
    text-decoration: underline;
  }
}

.Simple,
.SimpleTPD,
.SimpleGrey,
.SimpleSmall,
.Simple-NegativeApp {
  background-color: transparent;
  border: none;
  margin: 0;
  padding: 0;

  .button-text {
    font-family: $font-family-medium;
    font-size: $font-text-size;
    color: $main_color;
    font-weight: 500;
    line-height: 1.25;
    cursor: pointer;
    text-decoration: underline;
  }
}

.SimpleGrey {
  .button-text {
    color: $middle-grey-pu;
  }
}

.SimpleSmall {
  .button-text {
    font-family: $font-family-medium;
    color: $main_color;
    font-size: $font-text-xs-mobile-size;
  }
}

.AssuranceButton {
  background-color: transparent;
  border: none;

  .button-text {
    font-family: $font-family-bold;
    font-size: $font-text-size;
    color: $main_color;
    font-weight: 500;
    line-height: 1.25;
    cursor: pointer;
    text-decoration: underline;
  }
}

.Highlight {
  width: 206px;
  height: 50px;
  border: solid 0 #979797;
  background-color: white;
  border-bottom-color: $sub-button-color;
  border-bottom-width: 6px;

  .button-text {
    color: $main_color;
    font-family: $font-family-bold;
    font-size: $font-text-promo-size;
    cursor: pointer;
  }
}

.Simple-Negative-Carousel {
  display: flex;
  justify-content: center;
  align-items: center;

  height: 50px;
  border: solid 0 #979797;
  background-color: white;

  border-bottom-color: $sub-button-color;
  border-bottom-width: 6px;

  @media #{$bkp_mobile} {
    width: 100%;
  }

  @media #{$bkp_tablet} {
    width: 208px;
  }

  @media #{$bkp_desktop} {
    width: 208px;
  }

  .button-text {
    color: $main_color;
    font-family: $font-family-bold;
    font-size: $font-text-promo-size;
    cursor: pointer;
  }
}

.Rounded-positive-button {
  background-color: $main_red;
  border-radius: 25px;
  border: none;

  @media #{$bkp_mobile} {
    width: 130px;
    height: 40px;
  }

  @media #{$bkp_tablet} {
    width: 206px;
    height: 45px;
  }

  @media #{$bkp_desktop} {
    width: 206px;
    height: 45px;
  }

  &.disabled {
    cursor: default;
    opacity: 0.3;

    .button-text {
      cursor: default;
    }
  }

  .button-text {
    color: white;
    font-family: $font-family-bold;
    font-size: $font-text-size;
    cursor: pointer;
  }
}

.Rounded-cancel-button {
  background-color: white;
  border-radius: 25px;
  border: none;

  @media #{$bkp_mobile} {
    width: 130px;
    height: 40px;
  }

  @media #{$bkp_tablet} {
    width: 206px;
    height: 45px;
  }

  @media #{$bkp_desktop} {
    width: 206px;
    height: 45px;
  }

  .button-text {
    color: $main_color;
    font-family: $font-family-bold;
    font-size: $font-text-size;
    cursor: pointer;
  }
}

.mr-10 {
  margin-right: 10px;
}

.container {
  @media #{$bkp_desktop} {
    width: 100%;
    display: flex;
    justify-content: center;
  }
}

.DocumentationButton {
  padding: 19px 16px;
  margin: 15px 0;
  background-color: white;
  border: solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media #{$bkp_mobile} {
    width: $width_container_mobile;
  }

  @media #{$bkp_tablet} {
    width: $width_container_tablet;
  }

  @media #{$bkp_desktop} {
    width: $width_container_desktop;
  }

  .button-text {
    color: $main_color;
    font-family: $font-family-bold;
    font-size: 18px;

    @media #{$bkp_320_only} {
      font-size: $font-text-size;
    }
  }
}

.download {
  width: 92px;
  height: 40px;
  font-family: $font-family-default;
  color: white;
  background-color: $medium-light-blue;
  border: none;
}

.icon-pdf-size {
  font-size: 40px !important;
  margin-right: 12px;
  color: $dot_slick_bg;

  @media #{$bkp_mobile} {
    display: none;
  }

  @media #{$bkp_tablet} {
    display: block;
  }

  @media #{$bkp_desktop} {
    display: block;
  }
}

.Footer-button-WEB {
  width: 280px;
  height: 45px;
  padding: 13px 0.3px 13px 0.7px;
  background-color: $light-blue-color;
  border: none;
  cursor: pointer;

  .button-text {
    color: $dark-light_blue;
    font-family: $font-family-bold;
    font-size: $font-text-size;
  }

  &.disabled {
    opacity: 0.3;
    cursor: default;
  }
}

.SimpleTPD-responsive-white {
  background-color: transparent;
  border: none;
  margin: 0;
  padding: 0 1em 0 0;

  .button-text {
    font-family: $font-family-medium;
    color: white;
    font-weight: 500;
    line-height: 1.25;
    cursor: pointer;
    text-decoration: underline;

    @media #{$bkp_mobile} {
      font-size: $font-text-responsive-mobile-size;
    }

    @media #{$bkp_tablet} {
      font-size: $font-text-responsive-tablet-size;
    }

    @media #{$bkp_desktop} {
      font-size: $font-text-responsive-desktop-size;
    }
  }
}

.DiscountButton {
  display: flex;
  justify-content: space-between;
  align-items: center;

  width: 100%;
  background-color: rgba(255, 255, 255, 0.5);
  border: 0;

  border-radius: 8px;
  padding: 16px 8px;

  cursor: pointer;
  outline: unset !important;

  &::after {
    content: ">";
    color: $blue-primary;
    font-size: inherit;

    transform: scaleY(2) scaleX(0.5);
  }
}

.ButtonFrazionamento {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 48px;
  padding: 0;

  border: 0;
  border-radius: 24px;
  background-color: transparent;

  cursor: pointer;

  &.frazionamentoSelected {
    position: relative;
    background-color: $blue-primary;

    &::before{
      content: "";
      aspect-ratio: 1 / 1;
      width: 24px;
      border-radius: 50%;
      background-color: #1F5B8E;

      position: absolute;
      top: 50%;
      left: 12px;
      transform: translateY(-50%);
    }

    &::after{
      content: "";
      aspect-ratio: 2 / 1;
      width: 24px;
      border-bottom: white 3px solid;
      border-left: white 3px solid;

      position: absolute;
      top: 50%;
      left: 12px;
      transform-origin: center;
      transform: translateY(-50%) rotateZ(-45deg) scaleX(0.4) scaleY(0.4);
    }
  }
}

.DocumentationButtonPuContainer{
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 4px;

  >.ButtonTitle{
    align-self: flex-start;
  }

  >.DownloadButton{
    cursor: pointer;
    align-self: flex-end;
  }
}
