<ng-container *ngIf="isVisible">
  <ng-container [ngSwitch]="this.labelFormatClass">
    <ng-container
      *ngSwitchDefault>
      <button
        [disabled]="this.isDisabled"
        [class]="this.labelFormatClass"
        (click)="onClick()"
        [ngClass]="{
          frazionamentoSelected: this.isFrazionamentoButtonSelected,
          disabled: this.isDisabled,
          InlineIconContainer: this.pxIconVisualizzataButton
        }">
        <span *ngIf="this.labelFormatClass === 'Back'" class="Standard-bold mr-10">&lt;</span>
        <ng-container *ngIf="!this.isFrazionamentoButton; else frazionamentoButtonText">
          <span
            *ngIf="!this.labelFormatCss"
            class="button-text">
            {{ labelButton }}
          </span>
          <custom-text-style
            *ngIf="this.labelFormatCss"
            class="cursor-pointer"
            [textCss]="this.labelFormatCss"
            [content]="this.labelButton">
          </custom-text-style>
        </ng-container>
        <ng-template #frazionamentoButtonText>
          <custom-text-style
            [textCss]="this.formatoTestoFrazionamentoButton"
            [content]="this.labelButton">
          </custom-text-style>
          <custom-text-style
            *ngIf="this.subtitle"
            [textCss]="this.formatoSottotitoloFrazionamentoButton"
            [content]="this.subtitle">
          </custom-text-style>
        </ng-template>
        <tpd-px-icon style="cursor: pointer" [data]="{field: this.pxIconVisualizzataButton}"></tpd-px-icon>
      </button>

      <div
        *ngIf="this.captchaControl"
        style="display: flex; flex-direction: row; align-items: center; justify-content: center; gap: 5px; margin: 20px auto;">
        <custom-text-style
          style="cursor:pointer;"
          [content]="'Protetto da reCaptcha Privacy'"
          [textCssString]="'TEXT APP GDB0 WEB BLL10 BLL10 BLL10'"
          (click)="this.gestisciClickCaptchaControl('privacy')">
        </custom-text-style>
        <custom-text-style
          [content]="'-'"
          [textCssString]="'TEXT APP GDB0 WEB GDL10 GDL10 GDL10'">
        </custom-text-style>
        <custom-text-style
          style="cursor:pointer;"
          [content]="'Termini'"
          [textCssString]="'TEXT APP GDB0 WEB BLL10 BLL10 BLL10'"
          (click)="this.gestisciClickCaptchaControl('termini')">
        </custom-text-style>
      </div>

    </ng-container>


    <div
      *ngSwitchCase="'DocumentationButtonPU'"
      class="DocumentationButtonPuContainer">
      <custom-text-style
        class="ButtonTitle"
        [textCss]="this.labelFormatDownloadButtonCss"
        [content]="this.labelButton">
      </custom-text-style>

      <custom-text-style
        *ngIf="this.labelFormatCss"
        class="DownloadButton"
        [textCss]="this.labelFormatCss"
        [content]="'Leggi di più'"
        (mouseover)="onHover()"
        (click)="onClick()">
      </custom-text-style>
    </div>
  </ng-container>
</ng-container>
