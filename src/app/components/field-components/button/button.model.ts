export type ButtonLinkType =
  | '_URL'
  | 'Pdf'
  | 'GO_TO_LOGIN'
  | 'GO_TO_REGISTRATION'
  | 'GO_TO_HOME'
  | 'GO_TO_QUOTATION_HOME'
  | 'EXIT_FLOW'
  | 'GO_TO_APP'
  | 'GO_TO_NEW_QUOTATION'
  | 'GO_TO_FAILURE_PAGE';

export enum ButtonTipoFrazionamento {
  mensile = 'Mensile',
  annuale = 'Annuale',
}

export function ButtonTipoFrazionamentoMapper(
  frazionamento: string
): ButtonTipoFrazionamento {
  let esito = ButtonTipoFrazionamento.annuale;

  switch (frazionamento) {
    case '9':
      esito = ButtonTipoFrazionamento.mensile;
      break;
  }

  return esito;
}
