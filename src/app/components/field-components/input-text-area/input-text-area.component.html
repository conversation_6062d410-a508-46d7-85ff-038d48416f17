<ng-container *ngIf="field" class="layout-input">
  <div
    *ngIf="this.field.labelReserveSpace || this.label"
    class="{{ labelFormat }} mrg-label-input">
    <ng-container
      *ngIf="!this.labelCss">
      {{ this.label }}
    </ng-container>
    <custom-text-style
      *ngIf="this.labelCss"
      [content]="this.label"
      [textCss]="this.labelCss">
    </custom-text-style>
  </div>
  <textarea
    *ngIf="!this.readonly"
    class="input-dati-utente"
    [formControl]="this.fieldControl"
    [maxLength]="this.maxChars"
    [placeholder]="this.placeholder"
    [required]="this.required"
    [ngClass]="{
      'disabled-input-box': this.disabled
    }"
  >
  </textarea>
  <custom-text-style
    *ngIf="this.readonly"
    [content]="this.value"
    [textCss]="this.labelCss">
  </custom-text-style>
</ng-container>
