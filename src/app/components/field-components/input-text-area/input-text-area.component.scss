@import "../../../../styles.scss";

.layout-input {
  /* @media #{$bkp_mobile} {
    max-width: 90vw;
  } */

  @media #{$bkp_tablet} {
    max-width: 500px;
  }

  @media #{$bkp_desktop} {
    max-width: 550px;
  }
}

.input-dati-utente {
  width: 100%;
  min-height: 80px;
  border-radius: 0;
  padding: 12px 16px;
  background-color: white;
  border: solid 1px $main_color;
  color: $middle-grey-pu;
  font-family: $font-family-medium;
  font-size: 16px;
  resize: none;
}

textarea::placeholder {
  font-family: $font-family-medium;
  font-size: 16px;
  font-weight: 500;
  color: $grey;
}
