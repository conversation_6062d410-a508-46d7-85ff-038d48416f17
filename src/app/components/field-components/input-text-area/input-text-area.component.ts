import { Component, Input, OnDestroy } from '@angular/core';
import { FormControl } from '@angular/forms';
import { IField } from '../../../models/models.commons';
import {
  TextCss,
  TextHandlerService,
} from '../../../services/text-handler.service';
import { UtilsService } from '../../../services/utils.service';
import { StatusService } from "../../../services/status.service";

interface RequiredCustomAttributes {
  required: string;
  Required: string;
  disabled: string;
}

@Component({
  selector: 'tpd-input-text-area',
  templateUrl: './input-text-area.component.html',
  styleUrls: ['./input-text-area.component.scss'],
})
export class InputTextAreaComponent implements OnDestroy{
  public fieldControl = new FormControl('', null, null);

  public field: IField;

  public label = '';
  public labelFormat = '';
  public labelCss: TextCss;

  public required = false;
  public disabled = false;
  public readonly = false;

  public customAttributes: RequiredCustomAttributes;
  public placeholder = 'Inserisci qui le tue note';
  public maxChars = 400;
  public value = '';

  constructor(
    private _statusService: StatusService,
    private utilsService: UtilsService,
    private textHandlerService: TextHandlerService
  ) {}

  @Input() set data(input: { field: IField }) {
    if (input && input.field) {
      this.field = input.field;
      this.required = this.field?.required || false;
      this.value = this.field?.value || '';
      this.label = this.utilsService.htmlDecode(this.field?.label || '');
      this.readonly = this.field?.readOnly || false;

      this.labelCss = this.textHandlerService.getTextCss(this.field?.labelFormat);
      if(!this.labelCss){
        this.labelFormat = this.utilsService.getClassFromFormat(this.field?.labelFormat);
      }

      if (this.customAttributes) {
        if (!this.required)
          this.required = this.customAttributes.required === 'true' || this.customAttributes.Required === 'true';
        this.disabled = this.field.disabled || (this.customAttributes && this.customAttributes.disabled === 'true');
      }

      if(!this.readonly){
        this.fieldControl.setValue(this.value);
        this._statusService.addControlFormGroup(this.field.reference, this.fieldControl);

        //Casistica in cui il field non è di sola lettura
        if (this.field.control?.modes) {
          const modes = this.field.control.modes.find(
            (item) => item.modeType === 'editable'
          );
          if (modes && modes.maxChars) {
            this.maxChars = Number(modes.maxChars);
          }
        }
      }
    }
  }

  public ngOnDestroy(): void {
    if(!this.readonly)
      this._statusService.removeControlFormGroup(this.field.reference);
  }
}
