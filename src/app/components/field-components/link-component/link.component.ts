import { Component, Input } from '@angular/core';
import {
  TextCss,
  TextHandlerService,
} from '../../../services/text-handler.service';
import { IActionSet, IControl, IField } from '../../../models/models.commons';
import { AnalyticsInterprete } from '../../../services/analytics.interprete.service';
import { MessagesService } from '../../../services/messages.service';
import { UtilsService } from '../../../services/utils.service';
import { EventsManaged } from '../../../utils/events.enum';
import { EventsMethod } from '../../../utils/eventsMethod';
import { Helpers } from '@tpd-web-common-libs/nodejs-library';

@Component({
  selector: 'tpd-link',
  templateUrl: './link.component.html',
  styleUrls: ['./link.component.scss'],
})
export class LinkComponent extends EventsMethod {
  constructor(
    private utilsService: UtilsService,
    public override messageService: MessagesService,
    public analyticsService: AnalyticsInterprete,
    private textHandlerService: TextHandlerService
  ) {
    super(messageService, analyticsService);
  }

  override field!: IField;
  control?: IControl;
  customAttributes!: {
    link?: string;
    linkType?: '_URL' | 'PDF';
    Target?: '_blank' | '_self';
  };
  public label = '';
  public labelClass = '';
  public labelCss?: TextCss | null;
  public setCss = false;

  public callbackAfterRetrieveByComponentID: any;
  public override actionsSet!: IActionSet[];
  public retrieveType: string;

  @Input() set data(input: { field: IField }) {
    this.field = input.field;
    this.control = this.field.control;
    if (this.control && this.control.label) {
      this.label = this.utilsService.htmlDecode(this.control.label);
    }

    const mode =
      this.control &&
      this.control.modes &&
      this.control.modes.find((e) => e.controlFormat);
    this.labelClass = this.utilsService.getClassFromFormat(
      mode?.controlFormat ?? ''
    );
    if (mode && mode.controlFormat) {
      this.labelClass = this.utilsService.getClassFromFormat(
        mode.controlFormat
      );
      this.labelCss = this.textHandlerService.getTextCss(mode.controlFormat);

      this.setCss = !!this.labelCss;
      this.actionsSet = this.control.actionSets ?? [];
    } else {
      this.labelClass = '';
    }

    if (!this.setCss && this.field.labelFormat) {
      //Ricerca sul labelformat
      this.labelCss = this.textHandlerService.getTextCss(
        this.field.labelFormat
      );
      this.setCss = !!this.labelCss;
    }

    this.customAttributes = this.field.customAttributes;

    if (this.field.customAttributes?.componentID) {
      this.callbackAfterRetrieveByComponentID =
        this.utilsService.getCallbackAfterRetrieveByComponentID(
          this.field.customAttributes?.componentID
        );
    }

    if (this.field.customAttributes?.retrieveType)
      this.retrieveType = this.field.customAttributes.retrieveType;

    this.setValueProcessManagement();
    this.settaValoriGestioneProcesso();
  }

  public openLink() {
    this.checkSendAnalytics();
    if (this.customAttributes?.linkType === '_URL' && this.customAttributes?.link) {
      const target = this.customAttributes.Target ? this.customAttributes.Target : '_blank';
      Helpers.RouterHelper.goTo(this.customAttributes.link, {openNewTab: target === '_blank'});
    } else {
      this.messageService.onEventExecuted(
        this.actionsSet,
        EventsManaged.click,
        this.processManagement,
        this.gestioneProcesso,
        undefined,
        this.callbackAfterRetrieveByComponentID,
        this.retrieveType
      );
    }
  }
}
