<div *ngIf="field && field.visible">
  <div
    *ngIf="!setCss"
    class="{{ labelClass }} cursor-pointer color-link"
    (click)="openLink()"
  >
    {{ label }}
  </div>
  <!--Se arriva lo stile da PEGA-->
  <div *ngIf="setCss">
    <div
      class="visible-desktop cursor-pointer color-link"
      [ngStyle]="labelCss?.desktopCss"
      (click)="openLink()"
    >
      {{ label }}
    </div>
    <div
      class="visible-tablet cursor-pointer color-link"
      [ngStyle]="labelCss?.tabletCss"
      (click)="openLink()"
    >
      {{ label }}
    </div>
    <div
      class="visible-mobile cursor-pointer color-link"
      [ngStyle]="labelCss?.mobileCss"
      (click)="openLink()"
    >
      {{ label }}
    </div>
  </div>
</div>
