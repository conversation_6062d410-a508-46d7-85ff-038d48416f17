import { Component, Input, OnDestroy } from '@angular/core';
import { RouterHelper } from '@tpd-web-common-libs/nodejs-library/dist/src/helpers';
import { Subscription } from 'rxjs';
import { IField, IGroup, IView } from '../../../models/models.commons';
import { StatusService } from '../../../services/status.service';
import {
  SupportedCustomType,
  UtilsService,
} from '../../../services/utils.service';

@Component({
  selector: 'tpd-px-hidden',
  templateUrl: './px-hidden.component.html',
  styleUrls: ['./px-hidden.component.scss'],
})
export class PxHiddenComponent implements OnDestroy {
  private _data: CustomData | undefined = undefined;
  public field: IField | undefined = undefined;
  public groups: IGroup[] = [];
  public customType: SupportedCustomType | undefined = undefined;
  public supportedCustomType = SupportedCustomType;

  public viewCarousel?: IView;

  private _boxDisclaimerData: any = undefined;
  private _boxReasonWhyconfig: any = undefined;
  private _retrievedBoxReasonWhy = false;
  public statusBoxReasonWhyCallSub: Subscription | undefined = undefined;

  constructor(
    public utilsService: UtilsService,
    private statusService: StatusService
  ) {}

  @Input() set data(input: CustomData | undefined) {
    if (input) {
      this._data = input;

      this.groups = this._data.groups ?? [];
      this.field = this._data.field;
      if (
        this.field &&
        this.field.customAttributes &&
        this.field.customAttributes.customType
      ) {
        this.customType =
          this.utilsService.mapperCustomType[
            this.field.customAttributes
              .customType as keyof typeof this.utilsService.mapperCustomType
          ];

        if (this.customType === SupportedCustomType.carouselWeb)
          this.viewCarousel = this.groups[1].view;
      }
    }

    /*
    if (this.customType === SupportedCustomType.cmsBoxDisclaimer || this.customType === SupportedCustomType.cmsBoxReasonWhy) {
       this.statusBoxReasonWhyCallSub =
         this.utilsService.reasonWhyCallStatus.subscribe(e => {
           switch (e) {
             case 'NOT_EXECUTED':
               this.retrieveBoxReasonWhy();
               break;
             case 'OK':
               this._retrievedBoxReasonWhy = true;
               break;
           }
         });
     }
     */
  }

  get data() {
    return this._data;
  }

  /*
   async retrieveBoxReasonWhy() {
     await this.utilsService.retrieveBoxReasonWhy(
       this.statusService.configInterprete?.productBoxReasonWhy
     );
   }
   */

  ngOnDestroy() {
    this.statusBoxReasonWhyCallSub &&
      this.statusBoxReasonWhyCallSub.unsubscribe();
  }

  /**
   * Nella situazione di carousel card al click sul button esegue il redirect su disambiguazione
   */
  public onClickOfferHandler() {
    RouterHelper.goTo('/disambiguazione');
  }
}

export interface CustomData {
  groups?: IGroup[];
  field: IField;
}
