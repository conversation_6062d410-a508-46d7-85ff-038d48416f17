<div *ngIf="this.field" [ngSwitch]="customType">
  <toast-card *ngSwitchCase="supportedCustomType.toastCard" [field]="field">
  </toast-card>
  <carrello-pu
    *ngSwitchCase="supportedCustomType.carrelloPu"
    [data]="{ groups: groups, field: field }">
  </carrello-pu>
  <unico-protezione
    *ngSwitchCase="supportedCustomType.unicoProtezione"
    [data]="{ groups: groups, field: field }">
  </unico-protezione>
  <circular-stepper
    *ngSwitchCase="supportedCustomType.circularStepper"
    [field]="field">
  </circular-stepper>
  <tpd-card-garanzia
    *ngSwitchCase="supportedCustomType.cardGaranzie"
    [data]="{ groups: groups, field: field }">
  </tpd-card-garanzia>
  <header-dx-api
    *ngSwitchCase="supportedCustomType.header"
    [data]="{ groups: groups, field: field }">
  </header-dx-api>
  <footer-dx-api-pu
    *ngSwitchCase="supportedCustomType.footerStickyPU"
    [data]="{ groups: groups, field: field }">
  </footer-dx-api-pu>
  <tpd-payment
    *ngSwitchCase="supportedCustomType.paymentPage"
    [data]="{ field: field, groups: groups }">
  </tpd-payment>
  <tpd-stepper
    *ngSwitchCase="supportedCustomType.stepper"
    [field]="field"
  ></tpd-stepper>
  <address-component
    *ngSwitchCase="supportedCustomType.addressAutoComplete"
    [data]="{ groups, field }">
  </address-component>
  <tpd-line
    *ngSwitchCase="supportedCustomType.separator"
    [field]="field">
  </tpd-line>
  <vertical-space
    *ngSwitchCase="supportedCustomType.verticalSpace"
    [field]="field">
  </vertical-space>
  <box-indirizzo
    *ngSwitchCase="supportedCustomType.boxIndirizzo"
    [data]="{ groups: groups, field: field }">
  </box-indirizzo>
  <tpd-otp
    *ngSwitchCase="supportedCustomType.customActionModal"
    [data]="{ groups: groups, field: field }">
  </tpd-otp>
  <tpd-modal-underwriting
    *ngSwitchCase="supportedCustomType.modalUnderwriting"
    [data]="{ field: field }">
  </tpd-modal-underwriting>
  <tpd-card-protezione
    *ngSwitchCase="supportedCustomType.cardProtezione"
    [data]="{ groups: groups, field: field }">
  </tpd-card-protezione>
  <carousel-layout
    *ngSwitchCase="supportedCustomType.carouselWeb"
    [fieldData]="this.viewCarousel">
  </carousel-layout>
  <assurance-package
    *ngSwitchCase="supportedCustomType.assurancePackage"
    [data]="{ groups: groups, field: field }">
  </assurance-package>
  <card-sezione
    *ngSwitchCase="supportedCustomType.cardSezione"
    [data]="{ groups: groups, field: field }">
  </card-sezione>
  <configuratore-marketing-card
    *ngSwitchCase="supportedCustomType.marketingCard"
    [data]="{ field: field }">
  </configuratore-marketing-card>
  <card-garanzie-header
    *ngSwitchCase="supportedCustomType.cardGaranzieHeader"
    [data]="{ groups: groups, field: field }">
  </card-garanzie-header>
  <card-protezione-dettaglio-garanzia
    *ngSwitchCase="supportedCustomType.cardProtezioneDettaglioGaranzia"
    [data]="{ groups: groups, field: field }">
  </card-protezione-dettaglio-garanzia>
  <carousel-card
    *ngSwitchCase="supportedCustomType.carouselCard"
    [data]="{ groups: groups, field: field }">
  </carousel-card>
  <box-privacy
    *ngSwitchCase="supportedCustomType.boxPrivacy"
    [data]="{groups, field}">
  </box-privacy>
  <box-pagamento
    *ngSwitchCase="supportedCustomType.boxPagamento"
    [data]="{groups, field}">
  </box-pagamento>
  <card-telematica
    *ngSwitchCase="supportedCustomType.CardTelematica"
    [data]="{groups, field}">
  </card-telematica>
  <locator-component
    *ngSwitchCase="supportedCustomType.WorkshopLocator"
    [data]="{groups, field}">
  </locator-component>
  <locator-component
    *ngSwitchCase="supportedCustomType.AgencyLocator"
    [data]="{groups, field}">
  </locator-component>
  <accordion-checkbox
    *ngSwitchCase="supportedCustomType.AccordionCheckbox"
    [data]="{groups, field}">
  </accordion-checkbox>
  <tab-bar-frazionamento
    *ngSwitchCase="supportedCustomType.TabBarFrazionamento"
    [data]="{groups, field}">
  </tab-bar-frazionamento>
  <multi-stepper
    *ngSwitchCase="supportedCustomType.MultiStepper"
    [data]="{groups, field}">
  </multi-stepper>
</div>
