@import "../../../../styles.scss";

.AddressComponentContainer{
  display: flex;
  flex-direction: column;
  gap: 8px;

  .hidden{
    display: none !important;
  }

  .InputContainer{
    display: flex;
    flex-direction: column;
    width: 100%;
    align-items: flex-start;
    gap: 4px;

    >.InputUtente{
      display: block;
      width: 100%;
      height: 48px;
      padding: 12px 16px;
      background-color: white;
      color: $middle-grey-pu;
      border: solid 1px $color-primary;
      outline: none;

      &.error{
        border: solid 1px $alert-color !important;
      }
    }
  }

  >.ContainerInserimentoCivico{
    display: flex;
  }

  >.ContainerCambioInserimento{
    display: flex;
    flex-direction: row;
    gap: 4px;

    @media #{$bkp_mobile_only} {
      flex-direction: column;
    }
  }

  >.ContainerInserimentoManuale{
    display: flex;
    flex-direction: column;
    max-width: 500px;
    width: 100%;
    align-self: center;

    @media #{$bkp_mobile} {
      gap: 16px;
    }

    @media #{$bkp_tablet} {
      gap: 24px;
    }

    @media #{$bkp_desktop} {
      gap: 32px;
    }
  }
}






