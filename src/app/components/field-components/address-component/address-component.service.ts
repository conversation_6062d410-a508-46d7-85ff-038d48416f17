import { Injectable } from "@angular/core";
import {
  CreaStrutturaInformazioniGooglePlace,
  InformazioniIndirizzo,
  StrutturaInformazioniGooglePlace
} from "./addres-component.model";

@Injectable({
  providedIn: 'root'
})
export default class AddressComponentService{
  public recuperaInformazioniDalPlace(place: google.maps.places.PlaceResult): InformazioniIndirizzo {
    const esito: InformazioniIndirizzo = {};

    if (place) {
      const strutturaDati = CreaStrutturaInformazioniGooglePlace();
      place.address_components.forEach((component) => {
        for (const typeName of component.types) {
          let value, requiredType;
          if (StrutturaInformazioniGooglePlace.includes(typeName)) {
            requiredType = strutturaDati[typeName].format;
            value = component[requiredType];
            strutturaDati[typeName].value = value;
          }
        }
      });

      esito['nomeStrada'] = strutturaDati.route.value;
      esito['civico'] = strutturaDati.street_number.value;
      esito['cap'] = strutturaDati.postal_code.value;
      esito['comune'] = strutturaDati.administrative_area_level_3.value;
      esito['provincia'] = strutturaDati.administrative_area_level_2.value;
      esito['stato'] = strutturaDati.country.value;
    }

    return esito;
  };
}
