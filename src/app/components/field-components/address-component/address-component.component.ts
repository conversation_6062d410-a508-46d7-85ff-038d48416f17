import { AfterViewInit, ChangeDetector<PERSON>ef, Component, ElementRef, Input, NgZone, OnDestroy, ViewChild } from "@angular/core";
import { Constants } from "@tpd-web-angular-libs/angular-library";
import RetrieverService from "src/app/services/retriever.service";
import { TextCss, TextHandlerService } from "src/app/services/text-handler.service";
import { CustomData } from "../px-hidden/px-hidden.component";
import MapsService from "../../../services/maps.service";
import { IActionSet, ICaption, IField, IView } from "../../../models/models.commons";
import AddressComponentService from "./address-component.service";
import { InformazioniIndirizzo } from "./addres-component.model";
import { ListenerErroreFormInterface, StatusService } from "../../../services/status.service";
import { MessagesService } from "../../../services/messages.service";
import { UtilsService } from "../../../services/utils.service";
import { EventsManaged } from "../../../utils/events.enum";

type AddresModeType = 'MANUALE' | 'INDIRIZZO' | 'AUTOCOMPLETAMENTO'

@Component({
  selector: 'address-component',
  templateUrl: './address-component.component.html',
  styleUrls: ['./address-component.component.scss']
})
export default class AddressComponentComponent implements AfterViewInit, OnDestroy{
  private static _statoCorrenteResidenza: AddresModeType = 'AUTOCOMPLETAMENTO';
  private static _statoCorrenteDomicilio: AddresModeType = 'AUTOCOMPLETAMENTO';

  private _standAloneFormIndex = -1;

  public processoDiInvioDati = false;

  public labelAutocomplete: string;
  public placeholder: string;
  public stileLabels: TextCss;
  public required = false;
  public errorStatus = false;
  public errorMessage = '';

  public inputPrincipaleUtente = '';
  public inputSecondarioUtente = '';

  public nonTroviIlTuoIndirizzoLabel: ICaption;
  public nonTroviIlTuoIndirizzoCta: IField;

  public buttonConfermaInserimentoCivico: IField;

  public viewInserimentoManuale: IView;
  public buttonConfermaIndirizzoManuale: IField;
  public buttonAnnullaInserimentoManuale: IField;

  private _actionSet: IActionSet[] = [];
  private _customAttributes: {[key: string]: string} = {};

  private _autocomplete: google.maps.places.Autocomplete;
  private _informazioniPlaceAttuali: InformazioniIndirizzo = {};
  private _referenceBoxManuale: InformazioniIndirizzo = {};

  private _tipoAutocomplete: 'residenza' | 'domicilio' = 'residenza';

  private _errorListener: ListenerErroreFormInterface;

  @ViewChild('inputPrincipale', {read: ElementRef}) inputPrincipale: ElementRef

  constructor(
    private _retrieverService: RetrieverService,
    private _textHandlerService: TextHandlerService,
    private _addressComponentService: AddressComponentService,
    private _mapsService: MapsService,
    private _statusService: StatusService,
    private _messagesService: MessagesService,
    private _utilsService: UtilsService,
    private _zone: NgZone,
    private _cdr: ChangeDetectorRef
  ) {
    this._standAloneFormIndex = this._statusService.currentStandAloneFormIndex;
    this._errorListener = this._statusService.registraListenerDiErroreInputInvalido(this._standAloneFormIndex, statoErrore => {
      if(this.required && this._fullData.length === 0){
        this.errorStatus = statoErrore;
        this.errorMessage = "Questo campo é obbligatorio";
      }
    });
  }

  @Input() set data(data: CustomData){
    this.stileLabels = this._textHandlerService.getTextCss(this._retrieverService.getCustomAttributeFromField(data.field, 'styleLabelAutocomplete', 'TEXT APP GDM16 WEB BAB16 BAB16 BAB16'));
    this.labelAutocomplete = this._retrieverService.getCustomAttributeFromField(data.field, 'labelAutocomplete', 'Indirizzo');
    this.placeholder = this._retrieverService.getCustomAttributeFromField(data.field, 'placeholder', 'es: Via delle vie, 100, Bologna');
    this.required = this._retrieverService.getCustomAttributeFromField(data.field, 'required', 'false') === 'true';

    if(data.field.customAttributes['GestioneProcesso.MostraBoxDomicilio'])
      this._tipoAutocomplete = 'domicilio';
    if(data.field.customAttributes['GestioneProcesso.MostraBoxResidenza'])
      this._tipoAutocomplete = 'residenza';

    this._customAttributes = data.field.customAttributes;
    this._actionSet = data.field.control.actionSets;

    this._retrieveComponenti(data);
  }

  public ngAfterViewInit(): void {
    this._inizializzaAutocomplete();
  }

  private _retrieveComponenti(data: CustomData){
    const responsive2col = this._retrieverService.getFirstLayoutInGroupsByGroupFormat(data.groups, 'Responsive 2col');
    if(responsive2col){
      this.nonTroviIlTuoIndirizzoLabel = responsive2col.groups[0]?.caption;
      this.nonTroviIlTuoIndirizzoCta = responsive2col.groups[1]?.field;
      if(this.nonTroviIlTuoIndirizzoCta)
        this.nonTroviIlTuoIndirizzoCta.control.actionSets = [];
    }

    this.buttonConfermaInserimentoCivico = this._retrieverService.getFirstFieldInGroupsByComponentId(data.groups, 'Conferma civico');
    if(this.buttonConfermaInserimentoCivico)
      this.buttonConfermaInserimentoCivico.control.actionSets = [];

    let viewIndirizzoManuale = this._retrieverService.getFirstViewInGroupsByViewId(data.groups, 'InserimentoManualeIndirizzo');
    if(!viewIndirizzoManuale)
      viewIndirizzoManuale = this._retrieverService.getFirstViewInGroupsByViewId(data.groups, 'InserimentoManualeIndirizzoDomicilio');

    if(viewIndirizzoManuale){
      this.viewInserimentoManuale = viewIndirizzoManuale;
      const fields = this._retrieverService.getAllFieldInGroupsNotCloned(viewIndirizzoManuale.groups);
      for(const field of fields){
        if(field.visible === true && field.control.type === 'pxButton'){
          if(field.customAttributes.componentID === 'cancelAddressButton'){
            field.control.actionSets = [];
            this.buttonAnnullaInserimentoManuale = JSON.parse(JSON.stringify(field));
            field.visible = false;
          }else if(field.customAttributes.componentID === 'confirmAddressButton'){
            field.control.actionSets = [];
            this.buttonConfermaIndirizzoManuale = JSON.parse(JSON.stringify(field));
            field.visible = false;
          }
        }else{
          if(field?.reference?.toLowerCase().includes('nomestrada')) {
            this._referenceBoxManuale["nomeStrada"] = field.reference;
            if(this.required)
              field.required = true;
          }if(field?.reference?.toLowerCase().includes('numerocivico'))
            this._referenceBoxManuale['civico'] = field.reference;
          if(field?.reference?.toLowerCase().includes('provincia')) {
            this._referenceBoxManuale["provincia"] = field.reference;
            field.disabled = false;
          }
          if(field?.reference?.toLowerCase().includes('comune')) {
            this._referenceBoxManuale["comune"] = field.reference;
          }
          if(field?.reference?.toLowerCase().includes('cap'))
            this._referenceBoxManuale['cap'] = field.reference;
          if(field?.reference?.toLowerCase().includes('stato'))
            this._referenceBoxManuale['stato'] = field.reference;
        }
      }
    }
  }

  private _ricercaIndirizzoGooglePlace(){
    const place = this._autocomplete.getPlace();
    if(place){
      this.processoDiInvioDati = true;
      this._cdr.detectChanges();
      this._informazioniPlace = this._addressComponentService.recuperaInformazioniDalPlace(place);
      if(!this._informazioniPlaceAttuali.civico){
        this.errorStatus = true;
        this.errorMessage = 'Inserisci il numero civico';
        this.statoCorrente = 'INDIRIZZO';
        this.processoDiInvioDati = false;
        this._cdr.detectChanges();
      }else{
        this._normalizza();
      }
    }
  }

  private async _normalizza(){
    const richiestaNormalizza = {
      indirizzo: `${this._informazioniPlaceAttuali.nomeStrada}, ${this._informazioniPlaceAttuali.civico}`,
      comune: this._informazioniPlaceAttuali.comune,
      cap: this._informazioniPlaceAttuali.cap,
      provincia: this._informazioniPlaceAttuali.provincia
    };

    this._messagesService.setLoading(true);
    try {
      const response = this._statusService.modalitaRedirect ? {esito: {stato: 'OK'}} : await this._utilsService.normalizzaIndirizzi(richiestaNormalizza);
      if (response) {
        if (response.esito.stato === 'KO' && response.localita) {
          this.errorMessage = 'Per procedere è necessario inserire un indirizzo corretto';
          this.errorStatus = true;
          this.statoCorrente = 'MANUALE';
          this._informazioniPlace = {
            nomeStrada: '',
            civico: '',
            cap: '',
            comune: '',
            provincia: '',
            stato: ''
          }
          this.inputPrincipaleUtente = '';
          this.inputSecondarioUtente = '';
        } else {
          const codiceCatastaleComune = response?.localita?.codiceBelfiore || '';

          this.errorStatus = false;
          this.errorMessage = '';
          this.statoCorrente = 'AUTOCOMPLETAMENTO';
          this._concludiFlussoAutocomplete(codiceCatastaleComune);
        }
      }
    } catch(e){
      console.error("Errore nella normalizzazione dell'indirizzo dell'utente", String(e));
      this.errorMessage = "Errore nella procedura di normalizzazione dell'indirizzo";
      this.errorStatus = true;
    }

    this._messagesService.setLoading(false);
    this.processoDiInvioDati = false;
    this._cdr.detectChanges();
  }

  private _concludiFlussoAutocomplete(codiceCatastaleComune: string){
    const datiProcessoIndirizzo: {[key: string]: string} = {};
    if(this._customAttributes['GestioneProcesso.MostraBoxResidenza'])
      datiProcessoIndirizzo["GestioneProcesso.MostraBoxResidenza"] = this._customAttributes['GestioneProcesso.MostraBoxResidenza'];
    if(this._customAttributes['GestioneProcesso.MostraBoxDomicilio'])
      datiProcessoIndirizzo["GestioneProcesso.MostraBoxDomicilio"] = this._customAttributes['GestioneProcesso.MostraBoxDomicilio'];
    datiProcessoIndirizzo["GestioneProcesso.StepSuccessivo"] = this._customAttributes['GestioneProcesso.StepSuccessivo_change'];

    if(codiceCatastaleComune){
      const reference = `${this._referenceBoxManuale.nomeStrada.replace('NomeStrada', '')}CodiceCatastaleComune`
      datiProcessoIndirizzo[reference] = codiceCatastaleComune;
    }

    //this._statusService.referenceIndirizzo = Object.values(this._referenceBoxManuale);
    this._messagesService.onEventExecuted(
      this._actionSet,
      EventsManaged.change,
      {},
      datiProcessoIndirizzo,
      undefined,
      () => {
        this.inputPrincipaleUtente = '';
        this.inputSecondarioUtente = '';
      }
    );
  }

  private _inizializzaAutocomplete(){
    this._mapsService.loadService().then(() => {
      if (this.inputPrincipale.nativeElement) {
        try {
          this._autocomplete = new google.maps.places.Autocomplete(
            this.inputPrincipale.nativeElement,
            {
              types: ['address'],
              componentRestrictions: { country: Constants.geolocationRestriction, },
            }
          );
          this._autocomplete.addListener('place_changed', () => this._zone.run(() => this._ricercaIndirizzoGooglePlace()));
        } catch (e) {
          console.error("Errore nell'inizializzazione dell'autocomplete google", e);
        }
      }
    });
  }

  public handleConfermaManuale(){
    const formGroup = this._statusService.getFormGroup();
    if(formGroup){
      const nuovoStato: InformazioniIndirizzo = {};
      for(const key in this._referenceBoxManuale){
        const control = formGroup.controls[this._referenceBoxManuale[key]];
        nuovoStato[key] = control.value;
      }
      this._informazioniPlace = nuovoStato;
      this._normalizza();
    }
  }

  public handleConfermaCivico(){
    if(this.inputSecondarioUtente.length !== 0){
      const informazioniPlaceAttuali = this._informazioniPlaceAttuali;
      informazioniPlaceAttuali.civico = this.inputSecondarioUtente;
      this._informazioniPlace = informazioniPlaceAttuali;
      this.errorStatus = false;
      this.errorMessage = '';
      this._normalizza();
    }
  }

  public handlePassaggioIndirizzoManuale(){
    this.statoCorrente = 'MANUALE';
  }

  public handlePassaggioAutocompletamento(){
    this.statoCorrente = 'AUTOCOMPLETAMENTO';
    this.errorStatus = false;
    this.errorMessage = "";
  }

  public ngOnDestroy(): void {
    this._errorListener.rimuoviListenerDiErroreInputInvalido();
  }

  private get _fullData(): string{
    let esito = "";

    for(const key in this._informazioniPlaceAttuali){
      esito += this._informazioniPlaceAttuali[key];
    }

    return esito.trim();
  }

  private set _informazioniPlace(informazioniPlaceAttuale: InformazioniIndirizzo){
    this._informazioniPlaceAttuali = informazioniPlaceAttuale;
    const formGroup = this._statusService.getFormGroup();
    for(const key in this._referenceBoxManuale){
      const control = formGroup.controls[this._referenceBoxManuale[key]];
      if(control)
        control.setValue(informazioniPlaceAttuale[key]);
    }
  }

  public set statoCorrente(statoCorrente: AddresModeType){
    if(this._tipoAutocomplete === 'residenza')
      AddressComponentComponent._statoCorrenteResidenza = statoCorrente;
    else if(this._tipoAutocomplete === 'domicilio')
      AddressComponentComponent._statoCorrenteDomicilio = statoCorrente;
  }

  public get statoCorrente(): AddresModeType{
    if(this._tipoAutocomplete === 'residenza')
      return AddressComponentComponent._statoCorrenteResidenza;
    else if(this._tipoAutocomplete === 'domicilio')
      return AddressComponentComponent._statoCorrenteDomicilio;
  }

  public get blockSubmit(): boolean{
    return this.required && this._fullData.length === 0;
  }
}
