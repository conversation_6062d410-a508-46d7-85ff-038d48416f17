export const StrutturaInformazioniGooglePlace: string[] = [
  'street_number',
  'route',
  'administrative_area_level_2',
  'administrative_area_level_3',
  'country',
  'political',
  'postal_code',
];

export const CreaStrutturaInformazioniGooglePlace = () => {
  return {
    street_number: { format: 'short_name', value: '' },
    route: { format: 'long_name', value: '' },
    administrative_area_level_2: { format: 'short_name', value: '' },
    administrative_area_level_3: { format: 'short_name', value: '' },
    country: { format: 'long_name', value: '' },
    political: { format: 'short_name', value: '' },
    postal_code: { format: 'short_name', value: '' },
  };
}

export interface InformazioniIndirizzo {
  nomeStrada?: string,
  civico?: string,
  cap?: string,
  comune?: string,
  provincia?: string,
  stato?: string
}
