<submit-blocker *ngIf="this.blockSubmit"></submit-blocker>
<div class="AddressComponentContainer">
  <div
    class="ContainerInserimentoCivico"
    [ngClass]="{hidden: this.statoCorrente === 'MANUALE'}">
    <div class="InputContainer">
      <custom-text-style [content]="this.labelAutocomplete" [textCss]="stileLabels"></custom-text-style>
      <input
        #inputPrincipale
        type="text"
        class="InputUtente"
        [ngClass]="{
          error: this.errorStatus
        }"
        [placeholder]="this.placeholder"
        [(ngModel)]="this.inputPrincipaleUtente">
    </div>
    <div
      class="InputContainer"
      [ngClass]="{hidden: this.statoCorrente !== 'INDIRIZZO'}"
      style="width: 30%">
      <custom-text-style [content]="'Civico'" [textCss]="stileLabels"></custom-text-style>
      <input
        type="text"
        class="InputUtente"
        [ngClass]="{
          error: this.errorStatus
        }"
        [placeholder]="'es: 21'"
        [(ngModel)]="this.inputSecondarioUtente">
    </div>
  </div>
  <tpd-button
    *ngIf="this.buttonConfermaInserimentoCivico"
    [data]="{field: this.buttonConfermaInserimentoCivico}"
    [ngClass]="{hidden: this.statoCorrente !== 'INDIRIZZO'}"
    (click)="this.handleConfermaCivico()">
  </tpd-button>
  <div
    *ngIf="this.nonTroviIlTuoIndirizzoLabel && this.nonTroviIlTuoIndirizzoCta"
    class="ContainerCambioInserimento"
    [ngClass]="{hidden: this.statoCorrente === 'MANUALE'}">
    <caption-elem [caption]="this.nonTroviIlTuoIndirizzoLabel"></caption-elem>
    <tpd-button (click)="this.handlePassaggioIndirizzoManuale()" [data]="{field: this.nonTroviIlTuoIndirizzoCta}"></tpd-button>
  </div>
  <span
    *ngIf="this.errorStatus && this.errorMessage.length > 0"
    class="testo-non-valido"
    [ngClass]="{hidden: this.statoCorrente === 'MANUALE'}">
    <i class="icon-Attenzione-pieno bd-icona"></i>
    {{this.errorMessage}}
  </span>
  <div
    *ngIf="this.viewInserimentoManuale && (this.statoCorrente === 'MANUALE' || this.processoDiInvioDati)"
    class="ContainerInserimentoManuale"
    [ngClass]="{hidden: this.statoCorrente !== 'MANUALE'}">
    <view [view]="this.viewInserimentoManuale"></view>
    <span
      *ngIf="this.errorStatus && this.errorMessage.length > 0"
      class="testo-non-valido">
      <i class="icon-Attenzione-pieno bd-icona"></i>
      {{this.errorMessage}}
    </span>
    <tpd-button
      [data]="{field: this.buttonConfermaIndirizzoManuale}"
      (click)="this.handleConfermaManuale()">
    </tpd-button>
    <tpd-button
      [data]="{field: this.buttonAnnullaInserimentoManuale}"
      (click)="this.handlePassaggioAutocompletamento()">
    </tpd-button>
  </div>
</div>
