<div class="OtpSplittedBoxContainer">
  <span class="InputContainer">
    <input
      *ngFor="let n of this.counterArray; index as i"
      [id]="this.referenceName + '-' + i"
      type="text"
      maxlength="1"
      minlength="1"
      (keyup)="this.onInput($event, i)"
      (keydown)="$event.preventDefault()"
      (input)="$event.preventDefault()"/>
  </span>
  <div
    class="ZonaErrori">
    <span *ngFor="let errore of this.erroriTemporanei" class="Errore">
      <i class="icon-Attenzione-pieno bd-icona"></i>
      <span class="testo-non-valido">{{ errore }}</span>
    </span>
    <span *ngFor="let errore of this.erroriPermanenti" class="Errore">
      <i class="icon-Attenzione-pieno bd-icona"></i>
      <span class="testo-non-valido">{{ errore }}</span>
    </span>
  </div>
</div>
