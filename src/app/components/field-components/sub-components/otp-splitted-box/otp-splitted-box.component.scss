@import "../../../../../styles.scss";

.OtpSplittedBoxContainer{
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;

  >.InputContainer{
    display: flex;
    flex-direction: row;
    gap: 16px;

    @media #{$bkp_mobile_only} {
      gap: 8px;
    }

    >input[type='text']{
      aspect-ratio: 1 / 1;
      display: block;
      width: 40px;

      flex-shrink: 0;

      background-color: transparent;
      border: 0;
      outline: 0;

      border-bottom: 2px solid black;


      color: black;
      font-family: 'Unipol Medium';
      font-size: 16px;

      text-align: center;
    }
  }

  >.ZonaErrori{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
