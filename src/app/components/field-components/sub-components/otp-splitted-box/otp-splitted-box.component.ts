import { Component, ElementRef, Input, OnD<PERSON>roy } from "@angular/core";
import { AbstractControl, FormControl, ValidationErrors, ValidatorFn } from "@angular/forms";
import { IField } from "src/app/models/models.commons";
import { ListenerErroreFormInterface, StatusService } from "../../../../services/status.service";
import OtpManagerService from "../../../../services/otp-manager.service";
import { Helpers } from "@tpd-web-common-libs/nodejs-library";
import ComponentReferenceStorageService from "../../../../services/component-reference-storage.service";

@Component({
  selector: 'otp-splitted-box',
  templateUrl: './otp-splitted-box.component.html',
  styleUrls: ['./otp-splitted-box.component.scss']
})
export default class OtpSplittedBoxComponent implements OnDestroy{
  private _field: IField;
  private _formControl = new FormControl('');
  private _errorListener: ListenerErroreFormInterface;
  private _formIndex = -1;

  private _required = false;

  public mostraErrore = false;
  public referenceName: string;

  public erroriPermanenti: string[] = [];
  public erroriTemporanei: string[] = [];

  public constructor(
    private _currentRef: ElementRef,
    private _statusService: StatusService,
    private _otpManagerService: OtpManagerService,
    private _componentReferenceStorageService: ComponentReferenceStorageService
  ) {
    this._formIndex = this._statusService.currentStandAloneFormIndex;
    this._errorListener = this._statusService.registraListenerDiErroreInputInvalido(this._formIndex, statoErrore => {
      this.mostraErrore = statoErrore;
      if(statoErrore)
        this.calcolaErrore();
    });
    if(this._otpManagerService.ultimoTentativoInErrore) {
      this._otpManagerService.ultimoTentativoInErrore = false;
      this.erroriTemporanei.push("Il codice OTP é errato, si prega di riprovare");
      if(Helpers.EnvironmentHelper.isClientSide())
        window.setTimeout(() => this.erroriTemporanei = [], 2000);
    }
  }

  @Input() set data(data: {field: IField}){
    this._field = data.field
    this._required = data?.field?.required || data?.field?.customAttributes?.required || false;
    this.referenceName = this._field.reference;
    this._formControl.setValidators([this._calcolaErrore])
    this._statusService.addControlFormGroup(this.referenceName, this._formControl, this._formIndex);
    this._componentReferenceStorageService.storeComponent(this._field.elementUniqueId || 'otp-splitted-box', this);
    this.calcolaErrore();
  }

  public onInput(event: KeyboardEvent, formIndex: number){
    event.preventDefault();
    const input = event.target as HTMLInputElement;
    if(!isNaN(parseInt(event.key)) || event.key === 'Backspace'){
      input.value = event.key !== 'Backspace' ? event.key : '';
      if(this._currentRef.nativeElement){
        const fields = (this._currentRef.nativeElement as HTMLElement).querySelectorAll('input[type=text]');
        const otp = Array.from(fields).map(element => (element as HTMLInputElement).value).join('');

        this._formControl.setValue(otp);
        let nextFormIndex = -1;
        if(event.key === 'Backspace' && formIndex > 0)
          nextFormIndex = formIndex - 1;
        if(event.key !== 'Backspace' && formIndex < 5)
          nextFormIndex = formIndex + 1;

        if(nextFormIndex === -1)
          input.blur();
        else{
          const nextInput = document.getElementById(`${this.referenceName}-${nextFormIndex}`);
          nextInput && nextInput.focus();
        }
      }
    }
  }

  private _calcolaErrore: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {
    let hasError = false;
    this.erroriPermanenti = [];
    const errorObject = {};
    if(this._required && control.value.length === 0) {
      hasError = true;
      errorObject['required'] = true;
      this.mostraErrore && this.erroriPermanenti.push('Il campo otp non può essere vuoto');
    }
    if(this._otpManagerService.tenativiRimasti === '0') {
      hasError = true;
      errorObject['numeroTentativi'] = true;
      this.erroriPermanenti.push('Nessun tentativo rimasto, si prega di riprovare');
    }
    if(this._otpManagerService.tempoRimastoNumber === 0){
      hasError = true;
      errorObject['tempoRimasto'] = true;
      this.erroriPermanenti.push('Il tempo a disposizione é scaduto, si prega di riprovare');
    }

    return hasError ? errorObject : null;
  }

  public calcolaErrore(){
    this._formControl.updateValueAndValidity({onlySelf: true});
  }

  public ngOnDestroy(): void {
    this._statusService.removeControlFormGroup(this.referenceName);
    this._errorListener && this._errorListener.rimuoviListenerDiErroreInputInvalido();
  }

  public get counterArray(): number[]{
    return new Array(5).fill(0);
  }
}
