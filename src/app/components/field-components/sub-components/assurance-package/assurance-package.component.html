<div
  *ngIf="true"
  class="AssurancePackageContainer growMobile"
  [ngClass]="{selected: this.checked}">
  <span
    *ngIf="this.assurancePackageHeader"
    class="AssurancePackageHeader">{{this.assurancePackageHeader}}</span>
  <div class="AssurancePackageBody">
    <span class="AssurancePackageBodyHead">
      <span class="PackageName">{{this.pacakgeName}}</span>
      <span
        *ngIf="checkPackageCheckbox"
        class="PackageCheckbox"
        [ngClass]="{checked: this.checked}"
        (click)="this.handleClickCheckbox()"></span>
    </span>
    <div
      class="AssurancePackageContent">
      <span
        class="PrezzoAlMese">
        {{this.formattaValuta(this.premioRataScontato)}}€
        <span class="LabelAlMese">{{this.periodoInstallazione}}</span>
      </span>
      <div class="PrezzoAllAnno">
        <span *ngIf="this.premioAnno.length !== 0" class="PrezzoAnno">{{this.formattaValuta(this.premioAnno)}}€</span>
        <span *ngIf="this.premioScontatoAnno.length !== 0" class="PrezzoAnnoScontato">
          {{this.formattaValuta(this.premioScontatoAnno)}}€
          <span *ngIf="this.visualizzaLabelAllAnno" class="LabelAllAnno">all'anno</span>
        </span>
      </div>
    </div>
  </div>
  <span
    class="AssurancePackageSconto"
    [ngClass]="{
      scontoAssente: this.precentualeScontoNumber < 5
    }">
      sconto {{this.percentualeSconto}}%
    </span>
  <div class="AssurancePackageFooter"></div>
</div>
