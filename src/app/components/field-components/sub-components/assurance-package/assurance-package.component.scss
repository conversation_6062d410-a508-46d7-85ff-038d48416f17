@import "../../../../../variables.scss";

.AssurancePackageContainer {
  --AssurancePackageContainerSize: 470px;
  --HeaderFontSize: 20px;
  --PackageNameSize: 28px;
  --PrezzoAlMeseSize: 40px;
  --LabelAlMeseSize: 20px;
  --PrezzoAnnoSize: 18px;
  --PrezzoAnnoScontatoSize: 22px;
  --LabelAllAnnoSize: 14px;
  --ScontoSize: 18px;

  --AssurancePackageContainerColor: #{$secondary-opaque};
  --AssurancePackageRibbonColor: #{$secondary-lightest};

  display: flex;
  flex-direction: column;
  width: var(--AssurancePackageContainerSize);

  @media #{$bkp_mobile} {
    --AssurancePackageContainerSize: 100%;
    --HeaderFontSize: 10px;
    --PackageNameSize: 16px;
    --PrezzoAlMeseSize: 20px;
    --LabelAlMeseSize: 11pxpx;
    --PrezzoAnnoSize: 12px;
    --PrezzoAnnoScontatoSize: 14px;
    --LabelAllAnnoSize: 11px;
    --ScontoSize: 14px;
  }
  @media #{$bkp_tablet} {
    --AssurancePackageContainerSize: 350px;
    --HeaderFontSize: 16px;
    --PackageNameSize: 22px;
    --PrezzoAlMeseSize: 28px;
    --LabelAlMeseSize: 16px;
    --PrezzoAnnoSize: 13px;
    --PrezzoAnnoScontatoSize: 16px;
    --LabelAllAnnoSize: 13px;
    --ScontoSize: 16px;
  }
  @media #{$bkp_desktop} {
    --AssurancePackageContainerSize: 470px;
    --HeaderFontSize: 20px;
    --PackageNameSize: 28px;
    --PrezzoAlMeseSize: 40px;
    --LabelAlMeseSize: 20px;
    --PrezzoAnnoSize: 18px;
    --PrezzoAnnoScontatoSize: 22px;
    --LabelAllAnnoSize: 14px;
    --ScontoSize: 18px;
  }

  >.AssurancePackageHeader {
    display: flex;
    flex-direction: row;
    justify-content: center;
    text-transform: uppercase;
    font-family: "Unipol Bold";
    font-weight: 600;
    font-size: var(--HeaderFontSize);

    background-color: $blue-primary;
    color: white;

    padding: 4px 16px;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;

    position: relative;
    &::after{
      content: "";
      width: 100%;
      background-color: inherit;
      height: 20px;
      position: absolute;
      top: 100%;
    }
  }

  >.AssurancePackageBody {
    display: flex;
    flex-direction: column;

    background-color: var(--AssurancePackageContainerColor);
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    padding: 16px;
    color: white;
    z-index: 10;

    gap: 12px;

    @media #{$bkp_mobile_only}{
      padding: 12px;
    }

    >.AssurancePackageBodyHead {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      gap: 10px;

      >.PackageName {
        text-transform: uppercase;
        font-family: 'Unipol Bold';
        font-weight: 600;
        font-size: var(--PackageNameSize);
      }

      >.PackageCheckbox {
        aspect-ratio: 1 / 1;
        display: flex;
        justify-content: center;
        align-items: center;

        background-color: white;
        border-radius: 50%;
        border: 2px solid $blue-primary;
        width: 24px;

        cursor: pointer;

        &.checked{
          background-color: #38C661;
          border: 0;

          &::after{
            content: "";
            aspect-ratio: 5/3;
            display: block;
            width: 40%;

            border-bottom: 2px solid white;
            border-left: 2px solid white;

            transform: rotateZ(-45deg);
          }
        }

        .checkmark {
          aspect-ratio: 1 / 1;
          border-radius: 50%;

          border-width: 1px;
          width: 24px;

          &::after {
            width: 30%;
            height: 50%;
            border-right-width: 3px;
            border-bottom-width: 3px;
          }
        }
      }
    }

    >.AssurancePackageContent {
      display: flex;
      flex-direction: column;

      >.PrezzoAlMese {
        display: flex;
        flex-direction: row;
        align-items: flex-end;
        font-family: 'Unipol Bold';
        font-weight: 600;
        font-size: var(--PrezzoAlMeseSize);

        >.LabelAlMese {
          font-family: 'Unipol';
          font-weight: 100;
          font-size: var(--LabelAlMeseSize);
        }
      }

      >.PrezzoAllAnno {
        display: flex;
        flex-direction: row;
        align-items: flex-end;
        gap: 4px;

        @media #{$bkp_mobile_only}{
          flex-direction: column;
          align-items: flex-start;
        }

        >.PrezzoAnno {
          font-family: 'Unipol';
          font-weight: 100;
          text-decoration: line-through;
          font-size: var(--PrezzoAnnoSize);
        }

        >.PrezzoAnnoScontato {
          display: flex;
          flex-direction: row;
          align-items: flex-end;
          gap: 4px;

          font-family: 'Unipol Bold';
          font-weight: 600;
          font-size: var(--PrezzoAnnoScontatoSize);

          >.LabelAllAnno {
            font-family: 'Unipol';
            font-weight: 100;
            font-size: var(--LabelAllAnnoSize);
          }
        }
      }
    }
  }

  >.AssurancePackageSconto {
    display: flex;
    flex-direction: row;
    justify-content: center;
    padding: 4px 16px;
    font-family: 'Unipol Bold';
    font-weight: 600;
    font-size: var(--ScontoSize);
    color: $blue-primary;
    background-color: var(--AssurancePackageRibbonColor);

    &.scontoAssente{
      user-select: none !important;
      color: transparent !important;
      background-color: var(--AssurancePackageContainerColor) !important;
    }
  }

  >.AssurancePackageFooter {
    padding: 16px;
    border-bottom-right-radius: 16px;
    border-bottom-left-radius: 16px;
    background-color: white;
  }

  &.selected{
    --AssurancePackageContainerColor: #{$assurance-package-container-color};
    --AssurancePackageRibbonColor: #{$ribbon-color};

    >.AssurancePackageFooter{
      position: relative;
      border: 2px solid $assurance-package-container-color;
      &::before{
        content: "";
        aspect-ratio: 1 / 1;
        position: absolute;
        top: calc(100% + 1px);
        left: 50%;
        width: 6%;

        border-bottom: 2.4px solid $assurance-package-container-color;
        border-left: 2.4px solid $assurance-package-container-color;

        transform-origin: center;
        transform: translateX(-50%) translateY(-50%) scaleY(0.6) rotateZ(-45deg);
        background-color: white;
      }
    }
  }
}
