import { Component, Input} from '@angular/core';
import { CustomData } from '../../px-hidden/px-hidden.component';
import { IField, ILayout } from "../../../../models/models.commons";
import RetrieverService from "../../../../services/retriever.service";
import { MessagesService } from "../../../../services/messages.service";
import { EventsManaged } from "../../../../utils/events.enum";
import { TextHandlerService } from "../../../../services/text-handler.service";

@Component({
  selector: 'assurance-package',
  templateUrl: './assurance-package.component.html',
  styleUrls: ['./assurance-package.component.scss'],
})
export default class AssurancePackageComponent {
  constructor(
    private _retrieverService: RetrieverService,
    private _messagesService: MessagesService,
    private _textHandlerService: TextHandlerService
  ) {
  }

  public assurancePackageHeader: string;
  public checkPackageCheckbox: IField;
  public pacakgeName: string;
  public premioRataScontato: string;
  public periodoInstallazione: string;
  public premioScontatoLabel: ILayout;
  public percentualeSconto: string;

  public premioScontatoAnno: string;
  public premioAnno: string;

  public visualizzaLabelAllAnno = false
  public checked = false;

  @Input() set data(data: CustomData) {
    if (data) {
      this.assurancePackageHeader = this._retrieverService.getCustomAttributeFromField(data.field, 'assurancePackageHeader');
      this.checkPackageCheckbox = this._retrieverService.getFirstFieldInGroupsByCustomAttributeValue(data.groups, 'pxCheckbox', 'ComponentPurpose', 'CheckPackage');
      this.pacakgeName = this._retrieverService.captionToValue(this._retrieverService.getFirstCaptionByControlFormat(data.groups, 'PackageName')).value;
      this.premioRataScontato = this._retrieverService.fieldToValue(this._retrieverService.getFirstFieldInGroupsByFieldId(data.groups, 'pxCurrency', 'RataPremioLordoScontato'));
      this.periodoInstallazione = this._retrieverService.captionToValue(this._retrieverService.getFirstCaptionByControlFormat(data.groups, 'PackageInstallmentsPerion')).value;
      this.premioScontatoLabel = this._retrieverService.getFirstLayoutInGroupsByGroupFormat(data.groups, 'Mimic a sentence center');
      this.percentualeSconto = this._retrieverService.fieldToValue(this._retrieverService.getFirstFieldInGroupsByFieldId(data.groups, 'pxInteger', 'PercentualeSconto'));

      const layoutPrezzoScontato = this._retrieverService.getFirstLayoutInGroupsByGroupFormat(data.groups, 'Mimic a sentence');
      if(layoutPrezzoScontato){
        this.premioScontatoAnno = this._retrieverService.fieldToValue(this._retrieverService.getFirstFieldInGroupsByFieldId(layoutPrezzoScontato.groups, 'pxCurrency', 'PremioLordoScontato'));
        this.premioAnno = this._retrieverService.fieldToValue(this._retrieverService.getFirstFieldInGroupsByFieldId(layoutPrezzoScontato.groups, 'pxCurrency', 'PremioLordo'));
        this.visualizzaLabelAllAnno = this._retrieverService.getFirstCaptionInGroups(layoutPrezzoScontato.groups)?.visible || false;
      }

      if(this.checkPackageCheckbox)
        this.checked = this.checkPackageCheckbox.value === "true";
    }
  }

  public formattaValuta(valuta: string): string{
    return this._textHandlerService.formattaValuta(valuta);
  }

  public handleClickCheckbox(){
    if(!this.checked && this.checkPackageCheckbox){
      this._messagesService.onEventExecuted(
        this.checkPackageCheckbox?.control?.actionSets,
        EventsManaged.click,
        {},
        {
          [this.checkPackageCheckbox.reference]: true
        }
      )
    }
  }

  public get precentualeScontoNumber(): number{
    return parseInt(this.percentualeSconto || '0') || 0;
  }
}
