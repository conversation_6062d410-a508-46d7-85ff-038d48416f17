import { Component, Input } from '@angular/core';
import { IField, IGroup } from "../../../../models/models.commons";
import { CustomData } from '../../px-hidden/px-hidden.component';
import OtpManagerService from "../../../../services/otp-manager.service";
import RetrieverService from "../../../../services/retriever.service";
import ComponentReferenceStorageService from "../../../../services/component-reference-storage.service";
import { Caption } from "../../../caption/caption";
import OtpSplittedBoxComponent from "../otp-splitted-box/otp-splitted-box.component";

@Component({
  selector: 'tpd-otp',
  templateUrl: './otp.component.html',
  styleUrls: ['./otp.component.scss'],
})
export class OtpComponent {
  private _data: CustomData | undefined;
  private _field: IField | undefined;

  public visibleGroups: IGroup[] | undefined;

  public customAttributes: {
    outcomeValidation: string;
    StartTimer: string;
    MaxAttempts: string;
    purpose: string;
  }

  public constructor(
    private _otpManagerService: OtpManagerService,
    private _retrieverService: RetrieverService,
    private _componentReferenceStorageService: ComponentReferenceStorageService
  ) {
  }

  @Input() set data(input: CustomData) {
    this._data = input;
    this._field = input?.field;
    this.customAttributes = this._field?.customAttributes;

    const captionTempoResiduo = this._retrieverService.getFirstCaptionInGroupsByValueNotCloned(this._data.groups, 'Tempo residuo otp');
    if(captionTempoResiduo){
      captionTempoResiduo.elementUniqueId = 'otp-caption-tempo-residuo';
      captionTempoResiduo.value = '0:00';
    }
    if(this.customAttributes.outcomeValidation.length === 0){
      //Startiamo un nuovo manager
      this._otpManagerService.inizializzaManagerOtp(
        parseInt(this.customAttributes?.MaxAttempts || '3'),
        parseInt(this.customAttributes.StartTimer || '90'),
        () => this._aggiornaStatoCaption()
      );
    }else{
      this.customAttributes.outcomeValidation !== 'OK' &&
        this._otpManagerService.collegaTimer(() => this._aggiornaStatoCaption())
      switch (this.customAttributes.outcomeValidation){
        case 'KO':
          this._otpManagerService.scalaTentativo();
          break;
      }
    }

    const captionTentativiResidui = this._retrieverService.getFirstCaptionInGroupsByValueNotCloned(this._data.groups, 'Numero di tentativi');
    if(captionTentativiResidui)
      captionTentativiResidui.value = this._otpManagerService.tenativiRimasti;

    this.visibleGroups = this._data.groups?.filter(
      (e) => e && e.field?.control?.type !== 'pxHidden' && e.layout
    );
  }

  public get data() {
    return this._data;
  }

  private _aggiornaStatoCaption(){
    const caption: Caption = this._componentReferenceStorageService.getStoredComponent('otp-caption-tempo-residuo');
    if(caption)
      caption.captionValue = this._otpManagerService.tempoRimasto;
    if(this._otpManagerService.tempoRimastoNumber === 0){
      const otpSplittedBox: OtpSplittedBoxComponent = this._componentReferenceStorageService.getStoredComponent('otp-splitted-box');
      otpSplittedBox && otpSplittedBox.calcolaErrore();
    }
  }
}
