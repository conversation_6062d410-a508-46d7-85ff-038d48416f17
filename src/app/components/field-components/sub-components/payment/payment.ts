import { Component, Input } from '@angular/core';
import {
  CONFIG,
  CartaDiCredito,
  CommonFunctions,
  CommonLabels,
  Constants,
  DatiDiPagamentoXPayBuild,
  TpdNetworkService,
} from '@tpd-web-angular-libs/angular-library';
import { Helpers } from '@tpd-web-common-libs/nodejs-library';
import { LoginHelper } from '@tpd-web-common-libs/nodejs-library/dist/src/helpers';
import { IField, IGroup, ILayout } from "../../../../models/models.commons";
import { AnalyticsInterprete } from '../../../../services/analytics.interprete.service';
import { MessagesService } from '../../../../services/messages.service';
import { PaymentInterpreteService } from '../../../../services/payment.interprete.service';
import { StatusService } from '../../../../services/status.service';
import { UtilsService } from '../../../../services/utils.service';
import { EventsMethod } from '../../../../utils/eventsMethod';
import {
  appleMerchantID,
} from '../../../../utils/payment-utils';
import RetrieverService from "../../../../services/retriever.service";
import { TextHandlerService } from "../../../../services/text-handler.service";

interface ICustomAttributesPagamenti {
  Prezzo: string;
  SimboloValuta: string;
  IdPreventivo: string;
  VersionePreventivo: string;
  CodiceFiscale: string;
  uniboxPresente: string;

  CodiceCampagna?: string;
  DisclaimerCampagna?: string;
  DurataCampagna?: string;
  PrezzoCampagna?: string;

  NomeProdotto?: string;
  BrandProdotto?: string;
  IconaProdotto?: string;
  DescrizioneProdotto?: string;
  LabelTotaleApp?: string;

  mailContraente?: string;
  descrizioneAgenzia?: string;
  indirizzoAgenzia?: string;

  periodoRibbon?: string;
  nomeContraente?: string;
}
@Component({
  selector: 'tpd-payment',
  templateUrl: './payment.html',
  styleUrls: ['./payment.scss'],
})
export class PaymentComponent extends EventsMethod {
  public layoutInformazioniPagamento: ILayout;

  private customAttributes: ICustomAttributesPagamenti | undefined;
  public prezzoNumber: number | undefined;
  public prezzo: string | undefined;
  private idPreventivo: string | undefined;
  private versione: string | undefined;
  private codiceFiscale: string | undefined;
  public simboloValuta: string | undefined;
  public codiceCampagna: string | undefined;
  public disclaimerCampagna: string | undefined;
  public prezzoCampagna: string | undefined;
  public durataCampagna: string | undefined;
  public creditCards: CartaDiCredito[] = [];
  public isSuperUser = false;
  public suMessage: string | undefined;
  public userLogged = LoginHelper.userLogged;
  public disablePulsantePagamento = false;
  public proseguiButton = false;
  public imgPath: string | undefined;
  public pagamentoInCorso = false;
  public paymentService: string;
  public selectedCreditCard: CartaDiCredito;
  public salvaCartaPagamentiFuturi: boolean;

  public env: 'coll' | 'inte' | 'prod' = 'prod';
  public applePaymentRequest: any;

  public periodoRibbon: string;

  nonceRequestIdFromValidation: string;
  xpayNonceFromValidation: string;

  private requestPagamento = {
    PAYPAL: () => {
      return {
        importo: String(this.prezzoNumber),
        mezzoPagamentoOneShot: {
          mezzoPagamento: 'PAYPAL',
          url: {
            callbackClient:
              typeof window === 'object' &&
              window.location.origin + window.location.pathname,
            annullaErrore:
              typeof window === 'object' &&
              `${window.location.origin + window.location.pathname}?esito=KO`,
          },
          directPaymentTokenize: this.salvaCartaPagamentiFuturi
        },
        paymentService: this.paymentService
      };
    },
    ONE_SHOT: () => {
      return {
        importo: String(this.prezzoNumber),
        mezzoPagamentoOneShot: {
          mezzoPagamento: 'CC',
          url: {
            callbackClient:
              typeof window === 'object' &&
              window.location.origin + window.location.pathname,
            annullaErrore:
              typeof window === 'object' &&
              `${window.location.origin + window.location.pathname}?esito=KO`,
          },
          directPaymentTokenize: this.salvaCartaPagamentiFuturi
        },
        paymentService: this.paymentService
      };
    },
    APPLE_PAY: (token: any) => {
      return {
        importo: String(this.prezzoNumber),
        mezzoPagamentoRicorrente: {
          externalPaymentToken: token,
          mezzoPagamento: 'applePay'
        },
        paymentService: this.paymentService
      };
    },
    //...
  };

  constructor(
    public override messageService: MessagesService,
    public analyticsService: AnalyticsInterprete,
    private statusService: StatusService,
    private netWorkService: TpdNetworkService,
    private commonFunctions: CommonFunctions,
    private utilsService: UtilsService,
    private paymentInterpreteService: PaymentInterpreteService,
    private _retrieverService: RetrieverService,
    private _textHandlerService: TextHandlerService
  ) {
    super(messageService, analyticsService);
    this.imgPath = CONFIG.READ_CONFIG_FROM_SERVER
      ? '/TpdPortalCommons/build/assets/'
      : '/TpdPortalCommons/TpdPortalCommonsWeb/build/assets/';

    if (location.href.indexOf(`https://sit.${Helpers.BrandNameHelper.urlName()}.it`) > -1) {
      this.env = 'inte';
    }
    if (location.href.indexOf(`https://qa.${Helpers.BrandNameHelper.urlName()}.it`) > -1) {
      this.env = 'coll';
    }
  }

  async ngOnInit() {
    if (Helpers.EnvironmentHelper.isClientSide()) {
      await Helpers.SessionHelper.getData(Constants.lsMapObject._data5).then(
        (response) => {
          if (response) {
            const jsonObject = JSON.parse(response);
            if (jsonObject.value0 == '0') {
              this.isSuperUser = true;
              this.suMessage = CommonLabels.superUserMessage;
            }
          }
        }
      );

      this.paymentInterpreteService.abilitaPagamenti();
    }
  }

  @Input() set data(input: { field: IField, groups: IGroup[] }) {
    this.field = input.field;
    this.layoutInformazioniPagamento = this._retrieverService.getFirstLayoutInGroups(input.groups);

    this.customAttributes = this.field.customAttributes;
    if (this.customAttributes) {
      this.prezzo =
        this.customAttributes?.CodiceCampagna &&
        this.customAttributes?.CodiceCampagna.length > 0
          ? this.customAttributes.PrezzoCampagna
          : this.customAttributes.Prezzo;
      this.periodoRibbon = this.customAttributes.periodoRibbon;

      if (this.prezzo) this.prezzoNumber = parseFloat(this.prezzo);
      this.simboloValuta = this.utilsService.htmlDecode(
        this.customAttributes.SimboloValuta
      );
      this.idPreventivo = this.customAttributes.IdPreventivo;
      this.versione = '001';
      this.codiceFiscale = this.customAttributes.CodiceFiscale;

      const {
        mailContraente,
        descrizioneAgenzia,
        indirizzoAgenzia,
        nomeContraente,
        uniboxPresente
      } = this.customAttributes;

      if (
        mailContraente !== undefined &&
        descrizioneAgenzia !== undefined &&
        indirizzoAgenzia !== undefined
      )
        this.utilsService.setInputSuccess(
          mailContraente,
          descrizioneAgenzia,
          indirizzoAgenzia,
          nomeContraente,
          uniboxPresente
        );

      if (this.customAttributes.CodiceCampagna) {
        this.codiceCampagna = this.customAttributes.CodiceCampagna;
        this.durataCampagna = this.customAttributes.DurataCampagna;
        this.disclaimerCampagna =
          this.customAttributes?.DisclaimerCampagna?.replace(
            '{durataCampagna}',
            `${this.durataCampagna}`
          );
        this.prezzoCampagna = this.customAttributes.PrezzoCampagna;
      }
    }

    this.paymentService = 'PRODOTTO-UNICO';
  }

  showPayOrNextStepButton() {
    return this.creditCards.length !== 0;
  }

  onCreditCardsReceived(creditCards: CartaDiCredito[] = []) {
    this.creditCards = creditCards;
  }

  onSlideChange(index: number) {
    if (index === -1) {
      this.proseguiButton = false;
    } else {
      this.proseguiButton = true;
      this.selectedCreditCard = this.creditCards[index];
    }
  }

  async authorizePay() {
    this.messageService.setLoading(true);
    this.disablePulsantePagamento = true;
    const paymentMethodKey = this.selectedCreditCard ? this.selectedCreditCard.idEsterno : '';
    const nonceRequestId = this.netWorkService.generateRandomRequestId();
    const importo = String(this.prezzoNumber);
    const urlRisposta = window.location.href;
    this.storeData(true, paymentMethodKey, nonceRequestId);
    this.paymentInterpreteService.autorizzaPagamento(
      nonceRequestId,
      this.paymentService,
      paymentMethodKey,
      importo,
      urlRisposta
    );
  }

  /**
   * Abilita il pagamento con altra carta xpay
   * @param datiDiPagamentoXPayBuild Dati di pagamento XpayBuild
   */
  public async payWithOtherCard(datiDiPagamentoXPayBuild?: DatiDiPagamentoXPayBuild) {
    //this.disablePulsantePagamento = true;
    await this.storeData();
    this.callAnalytics('click paga con carta');
    this.paymentInterpreteService.acquista(
      this.requestPagamento['ONE_SHOT'](),
      this.versione,
      this.idPreventivo,
      this.codiceFiscale,
      false,
      datiDiPagamentoXPayBuild
    );
  }

  public salvaCarta(event: MouseEvent) {
    this.salvaCartaPagamentiFuturi = (event.target as HTMLInputElement).checked;
  }

  sendAnalyticsErrorCreditCards($event: any) {
    /*to do nothing*/
  }

  // apple pay

  setApplePayRequest() {
    const amount = String(this.prezzoNumber);

    this.applePaymentRequest = {
      merchantID: appleMerchantID[this.env],
      countryCode: 'IT',
      currencyCode: 'EUR',
      supportedNetworks: ['visa', 'masterCard'],
      merchantCapabilities: ['supports3DS'],
      total: { label: 'Unipol', amount: amount },
    };
  }

  async payWithAPay(token: any) {
    await this.storeData();
    this.callAnalytics('click paga con applepay');
    this.paymentInterpreteService.acquista(
      this.requestPagamento['APPLE_PAY'](token),
      this.versione,
      this.idPreventivo,
      this.codiceFiscale
    );
  }

  payWithPayPal(flag) {
    console.log("Pay with paypal: ", flag);
    this.storeData();
    this.callAnalytics('paga con paypal');
    this.paymentInterpreteService.acquista(
      this.requestPagamento['PAYPAL'](),
      this.versione,
      this.idPreventivo,
      this.codiceFiscale
    )
  }

  async storeData(
    returnFromAutorizza = false,
    aliasToken?: string,
    nonceRequestId?: string
  ) {
    await Helpers.SessionHelper.setData(
      'Payment_ProductType',
      this.statusService.configInterprete.productType
    );
    const paymentStorageKey = `Payment_Data_${this.statusService.configInterprete.productType}`;
    const paymentStorageData = {
      assignmentId: this.statusService.assignmentId,
      returnFromAutorizza: returnFromAutorizza,
      importo: String(this.prezzoNumber),
      idPreventivo: this.idPreventivo,
      versione: this.versione,
      aliasToken: aliasToken,
      nonceRequestId: nonceRequestId,
      codiceFiscale: this.codiceFiscale,
    };
    await this.paymentInterpreteService.setStorageDataPayment(paymentStorageKey, paymentStorageData);
    this.analyticsService.setStorageAnalytics('dataAnalyticsPagamentoOk');
  }

  callAnalytics(actionName: string) {
    const payload = {
      action_name: actionName,
      action_type: 'paga',
      paymentmethod_saved: this.salvaCartaPagamentiFuturi ? '1' : '0',
      action_effect: '',
    };

    switch (actionName) {
      case 'click paga con carta':
        payload.action_effect = 'paga con nuova carta';
        break;
      case 'click paga con applepay':
        payload.action_effect = 'paga con applepay';
        break;
      case 'click paga con googlepay':
        payload.action_effect = 'paga con googlepay';
        break;
    }

    this.analyticsService.sendAnalyticsAction(payload);
    this.analyticsService.setStorageAnalytics('dataAnalyticsPagamentoOk');
  }

  public formattaValuta(valuta: string): string{
    return this._textHandlerService.formattaValuta(valuta);
  }

  public onPayButtonClick() {
    console.log("On pay button click Payment");
    console.log("selected card: ", this.selectedCreditCard);
    this.authorizePay();
  }
}
