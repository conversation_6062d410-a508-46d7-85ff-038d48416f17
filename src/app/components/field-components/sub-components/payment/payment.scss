@import "../../../../../styles.scss";

.payment {
  font-size: 22px;
  height: 400px;
  color: #0f3250;
}

.first-section {
  border: 2px solid #f0f0f0;
  @media #{$bkp_mobile} {
    padding: 24px;
    margin: 16px;
    margin-top: 0px;
    flex-direction: column;
    gap: 24px;
  }

  @media #{$bkp_tablet} {
    padding: 24px;
    margin: 24px;
    margin-top: 0px;
    flex-direction: column;
    gap: 12px;
  }

  @media #{$bkp_desktop} {
    padding: 20px 24px;
    margin: 0 145px 16px 145px;
    flex-direction: column;
    gap: 12px;
  }
  .total {
    color: #5393bc;
    font-family: "Unipol Bold";
    font-weight: bold;
    font-size: 24px;
    display: flex;
    width: 100%;
    .ribbon {
      font-size: 13px;
      font-weight: normal;
      color: #fff;
      background-color: $dark-light_blue;
      padding: 8px;
      border-radius: 16px;
      width: fit-content;
    }
    @media #{$bkp_mobile} {
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 24px;
    }

    @media #{$bkp_tablet} {
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }

    @media #{$bkp_desktop} {
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
  }

  .price {
    font-size: 24px;
    font-family: "Unipol Bold";
    color: #0f3250;
    font-weight: bold;
    @media #{$bkp_mobile} {
      font-size: 28px;
    }

    @media #{$bkp_tablet} {
      font-size: 40px;
    }

    @media #{$bkp_desktop} {
      font-size: 40px;
    }
  }
}

.disclaimer {
  margin: 2rem;
  text-align: center;
  padding: 1rem;
  margin-top: -2rem;
  background-color: #e2f0f9;
  color: #0f3250;
  .prezzoCampagna {
    font-family: "Unipol Bold";
  }
}

.redButton {
  width: 206px;
  margin: 30px auto;
}

.tpd_button_red {
  margin: 30px auto;
  width: 206px;
  height: 45px;
  background-color: #c4151c;
  font-family: "Unipol Bold";
  cursor: pointer;
  color: white;
  border: none;
}

.hideThis {
  display: none;
}

.payments-container {
  max-width: 350px;
  text-align: center;
  margin: auto;

  .payment-label-container {
    display: block;
    margin-right: 10px;
  }

  border: 1px solid #282828;
  margin-top: 24.5px;
  padding: 20px 35px;

  cursor: pointer;
  display: flex;

  .payment-icons-container {
    padding-right: 8px;
    margin-left: 25px;

    .payment-icon {
      margin: 2px;
      max-width: 80px;
      pointer-events: none;
    }
  }

  .payment-caret-container {
    margin-left: 38px;
    margin-right: 15px;
  }
}

.salva-futuri {
  justify-content: center;
  margin-top: 24.5px;
  display: flex;

  :nth-child(2) {
    @media only screen and (max-width: 600px) {
      font-size: 85%;
    }
  }
}

.Checkbox {
  position: relative;
  margin-right: 50px;

  > * {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }

  &-visible {
    border: 1px solid $main-color;
    width: 24px;
    height: 24px;
    margin: 2px;
    background: #fff;
  }

  > input {
    z-index: 1;
    opacity: 0;
    left: 50%;
    top: 50%;
    transform: translatex(0%) translatey(-36%);
    display: block;
    cursor: pointer;
    width: 24px;
    height: 24px;
  }

  > input:checked + .Checkbox-visible {
    background: $main-color;
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMTQnIGhlaWdodD0nMTQnIHZpZXdCb3g9JzAgMCAxNCAxNCcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJz48dGl0bGU+d2hpdGU8L3RpdGxlPjxwYXRoIGQ9J001LjM3OCAxMS4zN0wyIDcuNTlsMS40MzgtMS4yODZMNS4zNzQgOC40N2w1LjE4NS01Ljg0TDEyIDMuOTFsLTYuNjIyIDcuNDYnIGZpbGw9JyNmZmYnLz48L3N2Zz4=);
    background-repeat: no-repeat;
    background-size: contain;
  }

  > input:hover + .Checkbox-visible {
    border-color: hsl(0, 2%, 80%);
  }

  > input:hover:checked + .Checkbox-visible {
    border-color: $main-color;
  }

  > input:focus + .Checkbox-visible {
    border-color: $main-color;
  }
}

.container-gpay {
  margin-top: 24.5px;
}

.container-apple-pay {
  justify-content: center;
  align-items: center;
  width: 349.25px;

  us-apple-pay-button {
    display: flex;
    align-items: center;
    height: 40px;
  }
}

.slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.slick-dots li,
.slick-dots li button {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

:host ::ng-deep {
  .slick-dots {
    position: absolute;
    bottom: -25px;
    list-style: none;
    display: block;
    text-align: center;
    padding: 0;
    //margin: 0;
    width: 100%;

    li {
      position: relative;
      display: inline-block;
      height: 20px;
      width: 20px;
      margin: 0 5px;
      padding: 0;
      cursor: pointer;

      button {
        border: 0;
        background: transparent;
        display: block;
        height: 20px;
        width: 20px;
        outline: none;
        line-height: 0;
        font-size: 0;
        color: transparent;
        padding: 5px;
        cursor: pointer;

        &:hover,
        &:focus {
          outline: none;

          &:before {
            opacity: $slick-opacity-on-hover;
          }
        }

        &:before {
          position: absolute;
          top: 0;
          left: 0;
          content: $slick-dot-character;
          width: 20px;
          height: 20px;
          font-family: $slick-font-family;
          font-size: $slick-dot-size !important;
          line-height: 20px;
          text-align: center;
          color: $slick-dot-color;
          opacity: $slick-opacity-not-active;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }
      }

      &.slick-active button:before {
        color: $slick-dot-color-active;
        opacity: $slick-opacity-default;
      }
    }
  }
}

.XpayBuildContainer::ng-deep{
  *{
    box-sizing: border-box !important;
  }

  .section-title{
    font-weight: bold !important;
  }

  @media #{$bkp_mobile} {
    padding: 0 16px 16px 16px;
  }

  @media #{$bkp_tablet} {
    padding: 0 24px 16px 24px;
  }

  @media #{$bkp_desktop} {
    padding: 0 145px 16px 145px;
  }
}
