<layout
  *ngIf="this.layoutInformazioniPagamento"
  [layout]="this.layoutInformazioniPagamento">
</layout>

<div *ngIf="pagamentoInCorso" class="payment color-darker align-items-c m-t-50">
  Pagamento in corso...
</div>

<div
  *ngIf="!this.pagamentoInCorso"
  class="first-section d-flex fd-column align-center">
  <div class="total">
    <span>TOTALE DA PAGARE</span>
    <span *ngIf="periodoRibbon" class="ribbon">{{periodoRibbon}}</span>
  </div>
  <div class="price color-darker bold">{{this.formattaValuta(prezzo)}} {{simboloValuta}}</div>
</div>

<div
  class="disclaimer background-color-light color-darker"
  *ngIf="codiceCampagna">
  {{disclaimerCampagna}}
  <span class="prezzoCampagna">{{prezzoCampagna}}</span>
</div>

<div
  class="XpayBuildContainer"
  *ngIf="this.paymentService && this.prezzoNumber">
  <xpay-build
    [paymentRequest]="{paymentService: this.paymentService, amount: this.prezzoNumber}"
    [applePayRequest]="applePaymentRequest"
    (onFlussoConlcuso)="!this.disablePulsantePagamento && this.payWithOtherCard($event)"
    (onSalvaMetodoPagamento)="this.salvaCarta($event)"
    (onApplePayClick)="payWithAPay($event)"
    (onPayPalClick)="payWithPayPal($event)"
    (onCreditCardsReceived)="onCreditCardsReceived($event)"
    (onSlideChange)="onSlideChange($event)"
    (errorOutput)="sendAnalyticsErrorCreditCards($event)"
    (onPayButtonClick)="onPayButtonClick()">
  </xpay-build>
</div>
