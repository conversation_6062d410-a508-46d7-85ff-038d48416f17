import { Component, Input } from '@angular/core';
import { IField } from '../../../../models/models.commons';

@Component({
  selector: 'vertical-space',
  templateUrl: './vertical-space.html',
  styleUrls: ['./vertical-space.scss'],
})
export class VerticalSpaceComponent {
  private _field: IField | undefined;
  public sizeClass: string | undefined;

  @Input() set field(input: IField | undefined) {
    this._field = input;
    this.sizeClass = this.field?.customAttributes?.spaceSizeWeb
      ? `size-${this.field?.customAttributes?.spaceSizeWeb}`
      : `size-${this.field?.customAttributes?.spaceSize}` || undefined;
  }

  get field() {
    return this._field;
  }
}
