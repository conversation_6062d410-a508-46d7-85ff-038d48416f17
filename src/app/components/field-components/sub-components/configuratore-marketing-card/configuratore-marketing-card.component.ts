import {
  Component,
  ElementRef,
  Input,
  ViewChild,
  Inject,
  OnInit,
} from '@angular/core';
import { AngularMFE, CommonMFE } from '@tpd-web-common-libs/nodejs-library';
import { BehaviorSubject } from 'rxjs';
import { IField } from '../../../../models/models.commons';
import { StatusService } from '../../../../services/status.service';
import { AppProps } from '../../../../app.props';
import { ProductType } from '../../../../models/config-interprete';

const AngularMicrofrontendClass: AngularMFE.Class.ClassType =
  AngularMFE.Class.AngularMicrofrontendFactory({ BehaviorSubject, AppProps });

@Component({
  selector: 'configuratore-marketing-card',
  templateUrl: './configuratore-marketing-card.component.html',
  styleUrls: ['./configuratore-marketing-card.component.scss'],
})
export class ConfiguratoreMarketingCard
  extends AngularMicrofrontendClass
  implements OnInit
{
  @Input() set data(input: { field: IField }) {
    if (!input) return;
    this._productType = input.field?.customAttributes?.productType;
  }

  private _productType: ProductType;
  private _props: AppProps | undefined;

  @ViewChild('MarketingCardContainer', { read: ElementRef })
  marketingCardContainer;

  get remoteDependencies(): CommonMFE.Types.TRemoteDependency[] {
    return [
      {
        elementRef: this.marketingCardContainer.nativeElement,
        props: {
          ...this._props,
          __mfeName__: 'tpdMarketingCard',
          productType: this._productType,
          dataContentId:
            this.statusService.configInterprete.marketingCardDataContentId,
        },
        updateProps: (newProps) => {
          return {
            ...newProps,
            __mfeName__: 'tpdMarketingCard',
            productType: this._productType,
            editMode: newProps?.editMode,
            dataContentId:
              this.statusService.configInterprete.marketingCardDataContentId,
          };
        },
      },
    ];
  }

  constructor(
    @Inject(AppProps) private appProps: AppProps,
    private statusService: StatusService
  ) {
    super(appProps);
  }

  ngOnInit(): void {
    this.statusService.props.subscribe((val) => {
      this._props = val;
      this.setProps(val);
    });
  }
}
