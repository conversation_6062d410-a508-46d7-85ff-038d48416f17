@import "../../../../../styles.scss";

.ToastCardContainer {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: stretch;

  position: relative;
  background-color: white;

  width: 100%;
  top: 0;
  left: 0;

  @media #{$bkp_mobile} {
    position: fixed;
    z-index: 10;
  }

  @media #{$bkp_tablet} {
    position: fixed;
    z-index: 10;
  }

  @media #{$bkp_desktop} {
    position: relative;
    z-index: 0;
  }

  > .toastType {
    display: flex;
    align-items: center;
    justify-content: center;

    flex-shrink: 0;

    padding: 40px 18px;
    width: auto;
    min-height: auto;

    background-color: lightgray;

    &::before {
      content: "";
      aspect-ratio: 1 / 1;
      display: block;
      width: 20px;
    }

    //Gestiamo il caso del warning
    &.warning {
      background-color: return-color("arancione-warning");
      &::before {
        background-image: url("/icons/icon-FeedbackError.svg");
      }
    }
  }

  > .toastContent {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    padding: 35px 10px;
    min-height: auto;

    flex-grow: 1;

    font-family: Unipol;
    font-size: 18px;
    font-weight: 500;
    color: $main_color;

    border: 1px solid $border-card-disabled;

    gap: 8px;

    @media #{$bkp_mobile_only} {
      font-size: 13px;
    }

    > .xIcon {
      aspect-ratio: 1 / 1;
      display: block;
      width: 20px;
      flex-shrink: 0;
      position: relative;

      cursor: pointer;

      &::before,
      &::after {
        content: "";
        background-color: $main_color;
        width: 100%;
        height: 5%;

        position: absolute;
        top: 50%;
      }

      &::before {
        transform: translateY(-50%) rotateZ(45deg);
      }

      &::after {
        transform: translateY(-50%) rotateZ(-45deg);
      }
    }
  }
}
