<ng-container *ngIf="this.field.visible">
  <span *ngIf="this.toastType === this.ToastType.warning && this.isOpen" class="ToastCardContainer">
    <span class="{{ 'toastType ' + this.toastType }}"></span>
    <span class="toastContent">
      {{ this.toastMessage }}
      <span class="xIcon" (click)="this.onClickHandler()"></span>
    </span>
  </span>
  <tpd-feedback-popup
    *ngIf="this.toastType === this.ToastType.accepted"
    [visualizzaFeedbackPopup]="this.isOpen"
    [contenutoPopup]="this.toastMessage"
    (onClickClose)="this.onClickHandler()">
  </tpd-feedback-popup>
</ng-container>


