import { Component, Input } from '@angular/core';
import { IField } from '../../../../models/models.commons';
import { ToastCardCustomAttributes, ToastType } from './toast-card.model';

@Component({
  templateUrl: './toast-card.component.html',
  styleUrls: ['./toast-card.component.scss'],
  selector: 'toast-card',
})
export default class ToastCardComponent {
  private static _ToastcardIdStatus = new Map<string, boolean>();

  public ToastType = ToastType;

  private _toastMessage = '';
  private _toastType: ToastType;
  private _isOpen: boolean;
  private _toastId: string;

  private _customAttributes: ToastCardCustomAttributes;
  private _field: IField;

  @Input() public set field(field: IField) {
    this._field = field;
    if (this._field.customAttributes) {
      this._customAttributes = this._field.customAttributes;
      this._toastMessage = this._customAttributes.toastMessage ?? '';
      this._toastType = this._customAttributes.toastType ?? ToastType.warning;
      this._toastId = this._customAttributes.toastcardId;
    }

    if(this._toastId !== undefined && ToastCardComponent._ToastcardIdStatus.has(this._toastId)){
      const lastStatus = ToastCardComponent._ToastcardIdStatus.get(this._toastId);
      this._isOpen = this._field && !lastStatus;
    }else this._isOpen = this._field.visible;
  }

  public get field(): IField {
    return this._field;
  }

  /**
   * Gestiamo il click sulla X
   */
  public onClickHandler() {
    this._isOpen = false;
    if(this._toastId)
      ToastCardComponent._ToastcardIdStatus.set(this._toastId, true);
  }

  public get toastMessage(): string {
    return this._toastMessage;
  }

  public get toastType(): ToastType {
    return this._toastType;
  }

  public get isOpen(): boolean {
    return this._isOpen;
  }
}
