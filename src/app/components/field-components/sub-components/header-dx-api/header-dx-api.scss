@import "../../../../../styles.scss";

// height header
$height_header_mobile: 48px;
$height_header_tablet: 76px;
$height_header_desktop: 84px;

// font header
$font-header-mobile: 16px;
$font-header-tablet: 22px;
$font-header-desktop: 24px;

// padding box header
$padding-header-mobile: 0 24px;
$padding-header-tablet: 24px 32px;
$padding-header-desktop: 24px 140px;

@media #{$bkp_mobile} {
  .header-dimension {
    height: $height_header_mobile;
    padding: $padding-header-mobile;
    font-size: $font-header-mobile;
  }
}

@media #{$bkp_tablet} {
  .header-dimension {
    height: $height_header_tablet;
    padding: $padding-header-tablet;
    font-size: $font-header-tablet;
  }
}

@media #{$bkp_desktop} {
  .header-dimension {
    height: $height_header_desktop;
    padding: $padding-header-desktop;
    font-size: $font-header-desktop;
  }
}

.header-dimension {
  position: relative;
  display: flex;
  align-items: center;
  font-family: $font-family-medium;
  color: white;
  justify-content: space-between;
  @media #{$bkp_tablet} {
    justify-content: flex-start;
  }
}

.column1 {
  display: flex;
  align-items: center;
}

.column2 {
  @media #{$bkp_tablet} {
    position: absolute;
    right: 32px;
  }
  @media #{$bkp_desktop} {
    right: 140px;
  }
}

.column-center {
  display: flex;
  justify-content: center;
}

button {
  &.icon-Chiudi {
    cursor: pointer;
    padding: 0;
    border: 0;
    background: none;
    font-size: 24px;
  }
}
