<tpd-navigation-header-container #navigationHeader>
  <!-- Navigation Header Widget remote -->
</tpd-navigation-header-container>
<button *ngIf="this.redirectMode" (click)="this.goBack()">Indietro</button>
<button *ngIf="this.redirectMode" (click)="this.stampaStatusService()">Stampa status service</button>
<button *ngIf="this.redirectMode" (click)="this.stampaStoredGestioneProcesso()">Stampa stored Gestione processo</button>
<button *ngIf="this.redirectMode" (click)="this.avviaRoc()">Avvia ROC</button>
