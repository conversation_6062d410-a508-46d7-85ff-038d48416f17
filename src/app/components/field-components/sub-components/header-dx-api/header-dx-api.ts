import {
  Component,
  Input,
  Inject,
  ViewChild,
  ElementRef,
  OnInit,
  OnDestroy,
} from '@angular/core';
import { AngularMFE, CommonMFE, Helpers } from '@tpd-web-common-libs/nodejs-library';
import {
  WidgetsBridgeService,
  HEADER_EVENTS,
} from '@tpd-web-angular-libs/angular-library';
import {
  IActionSet,
  ICaption,
  IControl,
  IField,
  IGestioneProcesso,
  IGroup,
  IParagraph,
  IProcessManagement,
} from '../../../../models/models.commons';
import { BehaviorSubject } from 'rxjs';
import { AppProps } from '../../../../app.props';
import { CustomData } from '../../px-hidden/px-hidden.component';
import { StatusService } from 'src/app/services/status.service';
import { MessagesService } from 'src/app/services/messages.service';
import { EventsManaged } from 'src/app/utils/events.enum';
import { TpdInterpreteDxApi } from "../../../../tpd-interprete-angular-dx-api-pu/tpd-interprete-angular-dx-api-pu";
import { EventsMethod } from "../../../../utils/eventsMethod";

interface HeaderCustomAttributes {
  background: 'Property' | 'Secondary' | 'Mobility';
}

const AngularMicrofrontendClass: AngularMFE.Class.ClassType =
  AngularMFE.Class.AngularMicrofrontendFactory({ BehaviorSubject, AppProps });

@Component({
  selector: 'header-dx-api',
  templateUrl: './header-dx-api.html',
  styleUrls: ['./header-dx-api.scss'],
})
export class HeaderDXAPIComponent
  extends AngularMicrofrontendClass
  implements OnInit, OnDestroy
{
  private _data: CustomData | undefined;
  private groups: IGroup[] = [];
  private field: IField | undefined;
  private control: IControl | undefined;
  private customAttributes: HeaderCustomAttributes | undefined;
  public background = '';
  public fieldIconBack: IField | undefined;
  public fieldIconClose: IField | undefined;
  public captionTitle: ICaption | undefined;
  public paragraphTitle: IParagraph | undefined;
  public processManagement?: IProcessManagement;
  public gestioneProcesso?: IGestioneProcesso;
  public eventHeaderSubscription: any;
  public hideBackBtn = true;
  public hideCloseBtn = true;

  @ViewChild('navigationHeader', { read: ElementRef })
  navigationHeader;



  @Input() set data(input: CustomData | undefined) {
    if (input) {
      this._data = input;
      if (this._data.groups) this.groups = this._data.groups;
      if (this._data.field) {
        this.field = this._data.field;
        this.control = this.field.control;
        this.customAttributes = this.field.customAttributes;
      }

      if (this.customAttributes)
        this.background = `background-${this.customAttributes.background}`;

      this.readCustomGroup(this.groups);
    }
  }

  get data() {
    return this._data;
  }

  private _props: AppProps | undefined;
  constructor(
    @Inject(AppProps) private appProps: AppProps,
    private statusService: StatusService,
    private messagesService: MessagesService,
    private navigationHeaderService: WidgetsBridgeService
  ) {
    super(appProps);
  }

  ngOnInit() {
    this.statusService.props.subscribe((val) => {
      this._props = val;
      this.setProps(val);
    });
    this.eventHeaderSubscription =
      this.navigationHeaderService.headerEvents.subscribe((val) => {
        if (val !== HEADER_EVENTS.NULL) {
          const event = val;
          console.log('click on: ', event);
          this.navigationHeaderService.resetEventHeader();
          this.onClick(event);
        }
      });
  }

  get remoteDependencies(): CommonMFE.Types.TRemoteDependency[] {
    const dataRef = {
      __mfeName__: 'tpdNavigationHeader',
      title: this.captionTitle
        ? this.captionTitle.value
        : this.paragraphTitle
          ? this.paragraphTitle.value
          : '',
      hideBackBtn: this.hideBackBtn,
      hideCloseBtn: this.hideCloseBtn,
      onBackBtnClicked: () =>
        this.navigationHeaderService.setEventHeader(HEADER_EVENTS.BACK),
      onCloseBtnClicked: () =>
        this.navigationHeaderService.setEventHeader(HEADER_EVENTS.CLOSE),
    }

    return [
      {
        elementRef: this.navigationHeader.nativeElement,
        props: {
          ...this._props,
          ...dataRef
        },
        updateProps: (newProps) => {
          return {
            ...newProps,
            ...dataRef
          };
        },
      },
    ];
  }

  public readCustomGroup(groups: IGroup[]) {
    this.statusService.disengageAssignmentOrigineRoc();
    groups.forEach((group) => {
      if (group.caption) this.captionTitle = group.caption;
      if (group.paragraph) this.paragraphTitle = group.paragraph;

      if (group?.field?.visible) {
        const componentId = group?.field?.customAttributes?.componentID;
        if (componentId === '_back') {
          this.fieldIconBack = group.field;
          if(this.fieldIconBack.customAttributes.assignmentOrigine === 'true'){
            this.statusService.engageAssignmentOrigineRoc();
          }
          this.hideBackBtn = false;
        }
        if (['close', 'closeWeb'].includes(componentId)) {
          this.fieldIconClose = group.field;
          this.hideCloseBtn = false;
        }
      }
    });
  }

  public onClick(action: HEADER_EVENTS) {
    let actionSet: IActionSet[];
    if (action === HEADER_EVENTS.BACK) {
      actionSet = this.fieldIconBack?.control?.actionSets;
      this.messagesService.onEventExecuted(
        actionSet,
        EventsManaged.click,
        this.processManagement,
        this.gestioneProcesso
      );
    } else if (action === HEADER_EVENTS.CLOSE) {
      this.onClose();
    }
  }

  public goBack(){
    this.onClick(HEADER_EVENTS.BACK);
  }

  public stampaStatusService(){
    console.log("StatusService:", this.statusService);
  }

  public stampaStoredGestioneProcesso(){
    console.log("StoredGestioneProcesso", (EventsMethod as any)._storedGestioneProcessoStoredValues);
  }

  public avviaRoc(){
    TpdInterpreteDxApi.TpdInterpreteDxApiContext.chiamaRichiestaDiContatto({});
  }

  public onClose() {
    Helpers.RouterHelper.goTo('/')
  }

  public get redirectMode(): boolean{
    return this.statusService.modalitaRedirect;
  }

  public ngOnDestroy(): void {
    this.eventHeaderSubscription && this.eventHeaderSubscription.unsubscribe();
  }
}
