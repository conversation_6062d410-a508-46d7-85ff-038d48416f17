<div class="UnicoPortezioneContainer unicoProtezioneType{{ this.productType }}">
  <groups
    *ngIf="this.renderGroup"
    [groups]="this.renderGroup">
  </groups>
  <span *ngIf="this.testoConvenzione && this.convenzione" class="UnicoPortezioneRibbon">
    <custom-text-style
      [content]="this.testoConvenzione"
      [textCss]="this.stileTestoConvenzione">
    </custom-text-style>
    <custom-text-style
      [content]="this.convenzione"
      [textCss]="this.stileConvenzione">
    </custom-text-style>
  </span>
</div>
