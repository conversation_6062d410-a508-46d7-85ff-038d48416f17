@import "../../../../../styles.scss";

.UnicoPortezioneContainer {
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 16px;
  border-radius: 16px;
  gap: 16px;

  > .UnicoPortezioneRibbon {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 4px;
  }

  &.unicoProtezioneTypeAUTO {
    background-color: $unico-protezione-auto-color;
  }

  &.unicoProtezioneTypeCASA {
    background-color: $unico-protezione-casa-color;
  }

  &.unicoProtezioneTypeFAMIGLIA {
    background-color: $unico-protezione-casa-color;
  }

  &.unicoProtezioneTypeSALUTE {
    background-color: $unico-protezione-salute-color;
  }

  &.unicoProtezioneTypeVIAGGI {
    background-color: $unico-protezione-viaggi-color;
  }

  &.unicoProtezioneTypeMOBILITA, &.unicoProtezioneTypeMOBILITÀ {
    background-color: $unico-protezione-mobilita-color;
  }

  &.unicoProtezioneTypePET {
    background-color: $unico-protezione-pet-color;
  }

  &.unicoProtezioneTypeINFORTUNI {
    background-color: $unico-protezione-infortuni-color;
  }

  &.unicoProtezioneTypeMALATTIA {
    background-color: $unico-protezione-malattia-color;
  }
}
