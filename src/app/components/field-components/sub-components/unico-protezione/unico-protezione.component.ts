import { Component, Input } from '@angular/core';
import { CustomData } from '../../px-hidden/px-hidden.component';
import { IGroup, ILayout } from "../../../../models/models.commons";
import RetrieverService from 'src/app/services/retriever.service';
import { TextCss, TextHandlerService } from "../../../../services/text-handler.service";

@Component({
  templateUrl: './unico-protezione.component.html',
  styleUrls: ['./unico-protezione.component.scss'],
  selector: 'unico-protezione',
})
export default class UnicoProtezioneComponent {
  public productType: string;
  public renderGroup: IGroup[];

  public stileTestoConvenzione: TextCss;
  public stileConvenzione: TextCss;
  public testoConvenzione: string;
  public convenzione: string;

  public constructor(
    private _retrieverService: RetrieverService,
    private _textHandlerService: TextHandlerService
  ) {}

  @Input() set data(input: CustomData) {
    this.productType = input?.field?.customAttributes?.productType;
    const unicoProtezioneRibbon = this._retrieverService.getFirstLayoutInGroupsByGroupFormat(input?.groups, 'UnicoProtezioneRibbon');

    if(unicoProtezioneRibbon){
      this.stileTestoConvenzione = this._textHandlerService.getTextCss('TEXT APP BDB16 WEB WHL14 WHL14 WHL14');
      this.stileConvenzione = this._textHandlerService.getTextCss('TEXT APP BDB16 WEB WHB14 WHB14 WHB14 WHB14');
      const caption = this._retrieverService.getEachCaptionInGroups(unicoProtezioneRibbon.groups);
      if(caption.length === 2){
        this.testoConvenzione = caption[0].value;
        this.convenzione = caption[1].value;
      }
    }

    this.renderGroup = input?.groups.filter(group => {
      let esito = true;
      if(group?.field?.control?.type === 'pxHidden')
        esito = false;
      if(group?.layout?.groupFormat === 'UnicoProtezioneRibbon')
        esito = false;

      return esito;
    })
  }
}
