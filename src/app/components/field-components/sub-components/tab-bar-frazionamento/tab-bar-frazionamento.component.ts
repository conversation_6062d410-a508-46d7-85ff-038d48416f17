import { Component, Input } from "@angular/core";
import { CustomData } from "../../px-hidden/px-hidden.component";
import { IGroup } from "../../../../models/models.commons";

@Component({
  templateUrl: './tab-bar-frazionamento.component.html',
  styleUrls: ['./tab-bar-frazionamento.component.scss'],
  selector: 'tab-bar-frazionamento'
})
export default class TabBarFrazionamentoComponent{
  public renderGroup: IGroup[] = [];

  @Input() set data(data: CustomData) {
    if(data.groups){
      this.renderGroup = data.groups.filter(group => group?.field?.control?.type !== 'pxHidden');
    }
  }
}
