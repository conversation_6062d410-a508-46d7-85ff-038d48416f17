<div *ngIf="field">
  <div class="unipol-modal-background"></div>
  <div class="unipol-modal-content">
    <div class="inlineMiddle-layout">
      <div class="flex-column">
        <div class="cerchio"><span class="icon-Punto-esclamativo"></span></div>
        <h2 class="Text-responsive-bold-M">{{ title }}</h2>
        <p class="Text-responsive">{{ text }}</p>
        <div class="web1colSwapApp2col">
          <button class="Cancel-button" (click)="goHome()">
            <span class="button-text">{{ goToHomeText }}</span>
          </button>
          <button class="Positive-Button" (click)="contactRequest()">
            <span class="button-text">{{ rocButtonText }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
