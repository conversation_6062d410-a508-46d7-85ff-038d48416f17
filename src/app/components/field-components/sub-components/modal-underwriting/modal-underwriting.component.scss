@import "../../../../../styles.scss";

.unipol-modal-content {
  overflow-x: hidden;
  overflow-y: auto;
  background-color: white;
  margin: auto;
  width: 70vw;
  max-width: 850px;
  max-height: 90vh;
  padding-bottom: 2%;
  z-index: 1000;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  position: fixed;
  text-align: center;

  @media #{$bkp_mobile} {
    width: 90vw;
    padding: 2%;
  }
  @media #{$bkp_tablet} {
    width: 80vw;
    padding: 7%;
  }
}

.Positive-Button {
  margin-top: 1rem;
  margin-bottom: 1rem;
  width: 206px;
  height: 45px;
  background-color: $main_red;
  object-fit: contain;
  cursor: pointer;
  border: none;
  &.disabled {
    opacity: 0.3;
  }

  .button-text {
    font-family: $font-family-bold;
    font-size: $font-text-size;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: white;
  }
}

.Cancel-button {
  margin-top: 1rem;
  margin-bottom: 1rem;
  width: 206px;
  height: 45px;
  background-color: $dark-light_blue;
  object-fit: contain;
  cursor: pointer;
  border: none;
  &.disabled {
    opacity: 0.3;
  }

  .button-text {
    font-family: $font-family-bold;
    font-size: $font-text-size;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: white;
  }
}

.cerchio {
  background-color: #f6a623;
  border-radius: 100%;
  width: 40px;
  height: 40px;
  padding: 1rem;
  font-size: 30px;
  position: relative;
  .icon-Punto-esclamativo {
    color: white;
    position: absolute;
    top: 7px;
    left: 5px;
  }
}

.unipol-modal-background {
  background-color: #183a56;
  opacity: 0.9;
  position: fixed;
  display: flex;
  top: 0;
  left: 0;
  margin: auto;
  width: 100%;
  height: 100%;
  z-index: 999;
}
.Text-responsive-bold-M {
  @media #{$bkp_mobile} {
    font-size: 20px;
  }
}
.flex-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  row-gap: 3vh;
}

.web1colSwapApp2col {
  @media #{$bkp_mobile} {
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
  }

  @media #{$bkp_tablet} {
    display: flex;
    flex-direction: row;
    justify-content: center;
    column-gap: 4vw;
  }

  @media #{$bkp_desktop} {
    display: flex;
    flex-direction: row;
    justify-content: center;
    column-gap: 3vw;
  }
}
