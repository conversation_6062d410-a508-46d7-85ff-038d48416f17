import { Component, EventEmitter, Input, Output } from '@angular/core';
import { AnalyticsInterprete } from '../../../../services/analytics.interprete.service';
import { EventsMethod } from '../../../../utils/eventsMethod';
import { IActionSet, IField } from '../../../../models/models.commons';
import { MessagesService } from '../../../../services/messages.service';
import { UtilsService } from '../../../../services/utils.service';

@Component({
  selector: 'tpd-modal-underwriting',
  templateUrl: './modal-underwriting.component.html',
  styleUrls: ['./modal-underwriting.component.scss'],
})
export class ModalUnderwritingComponent extends EventsMethod {
  @Output() closeModalEmitter: EventEmitter<any> = new EventEmitter();

  public override field!: IField;
  public title = '';
  public text = '';
  public rocButtonText = '';
  public goToHomeText = '';
  public override actionsSet!: IActionSet[];

  @Input() set data(input: { field: IField }) {
    this.field = input.field;
    this.title = this.utilsService.htmlDecode(
      this.field?.customAttributes?.Title
    );
    this.text = this.utilsService.htmlDecode(
      this.field?.customAttributes?.Text
    );
    this.rocButtonText = this.utilsService.htmlDecode(
      this.field?.customAttributes?.ROCButtonText
    );
    this.goToHomeText = this.utilsService.htmlDecode(
      this.field?.customAttributes?.GoToHomeText
    );
    if (this.field?.control?.actionSets) {
      this.actionsSet = this.field?.control?.actionSets;
    }
    this.setValueProcessManagement();
    this.settaValoriGestioneProcesso();
  }

  constructor(
    private utilsService: UtilsService,
    public override messageService: MessagesService,
    public analyticsService: AnalyticsInterprete
  ) {
    super(messageService, analyticsService);
  }

  contactRequest() {
    this.onClick();
  }

  goHome() {
    location.href = '/';
  }
}
