import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'tpd-feedback-popup',
  templateUrl: './feedback-popup.component.html',
  styleUrls: ['./feedback-popup.component.scss'],
})
export class FeedbackPopupComponent {
  @Input() visualizzaFeedbackPopup = false;
  @Input() contenutoPopup: string;

  @Output() onClickClose = new EventEmitter<void>();

  constructor() {
    //TODO: nothing
  }

  @Input() set contenuto(data: number) {
    /**/
    window.setTimeout(() => {
      this.onCloseClickHandler();
    }, 3000);
  }

  public onCloseClickHandler(){
    this.visualizzaFeedbackPopup = false;
    this.onClickClose.emit();
  }
}
