@import "../../../../../styles.scss";

.FeedbackContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 300px;
  max-width: 90vw;
  height: 80px;

  background-color: white;

  border: 1px solid $border-card-disabled;
  border-radius: 24px;
  overflow: hidden;

  gap: 16px;

  position: fixed;
  z-index: 100;

  left: 50%;
  transform: translateX(-50%);
  top: -100px;

  @keyframes animazioneDiscesa {
    from{
      top: -100px;
    }

    to{
      top: 60px;
    }
  }

  animation-duration: 0.2s;
  animation-fill-mode: forwards;
  animation-name: animazioneDiscesa;

  >.FeedbackCheckmark {
    display: block;
    position: relative;
    background-color: $check-green-color;
    height: 100%;
    width: 60px;
    flex-shrink: 0;

    &::before{
      content: '';
      aspect-ratio: 1 / 1;

      display: block;
      flex-shrink: 0;
      width: 20px;

      background-color: transparent;
      border: white 2px solid;
      border-radius: 50%;

      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateX(-50%) translateY(-50%);
    }

    &::after{
      content: '';
      aspect-ratio: 2 / 1;

      display: block;
      flex-shrink: 0;
      width: 6px;

      background-color: transparent;
      border: white 2px solid;
      border-top: 0;
      border-right: 0;

      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateX(-50%) translateY(-50%) rotateZ(-45deg);
    }
  }

  >.FeedbackClose {
    aspect-ratio: 1 / 1;
    display: block;
    flex-shrink: 0;
    width: 24px;
    position: relative;
    cursor: pointer;
    margin-left: auto;
    margin-right: 20px;

    &::before, &::after{
      content: '';
      background-color: $blue-primary;
      width: 2px;
      border-radius: 10px;
      height: 100%;

      position: absolute;
      top: 50%;
      left: 50%;
    }

    &::before{
      transform: translateX(-50%) translateY(-50%) rotateZ(-45deg);
    }

    &::after{
      transform: translateX(-50%) translateY(-50%) rotateZ(45deg);
    }
  }
}
