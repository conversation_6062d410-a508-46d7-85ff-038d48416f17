import { Component, Input, OnDestroy } from "@angular/core";
import { IField, IGroup } from "../../../../models/models.commons";
import RetrieverService from "../../../../services/retriever.service";
import { MessagesService } from "../../../../services/messages.service";
import { Agency, BaseCommunicator, Request } from "@tpd-web-angular-libs/angular-library";
import { StatusService } from "../../../../services/status.service";
import { FormControl, Validators } from "@angular/forms";
import { Helpers } from "@tpd-web-common-libs/nodejs-library";
import { mockAgenzia, mockInstallatore } from "./locator.mock";
import { EventsMethod } from "../../../../utils/eventsMethod";
import { AnalyticsInterprete } from "src/app/services/analytics.interprete.service";

@Component({
  selector: 'locator-component',
  templateUrl: './locator.component.html',
  styleUrls: ['./locator.component.scss']
})
export default class LocatorComponent extends EventsMethod implements OnDestroy{
  public mockInstallatore = mockInstallatore;
  public mockAgenzia = mockAgenzia;

  public stepCorrente: 'scelta_agenzia' | 'scelta_installatore' = "scelta_agenzia";
  public areDataSetted = false;
  public localhostMock = false;

  private _lastDataToSend: {[key: string]: string};
  private _locatorAction = false;

  constructor(
    private _retriverService: RetrieverService,
    private _messagesService: MessagesService,
    private _analyticsService: AnalyticsInterprete,
    private _statusService: StatusService,
    private _baseCommunicator: BaseCommunicator,
  ) {
    super(_messagesService, _analyticsService);
    if(Helpers.EnvironmentHelper.isClientSide()){
      if(!window.location.href.includes('localhost')){
        this._messagesService.setLoading(true);
      }else{
        this.localhostMock = true;
      }
    }
  }

  @Input() set data(data: {field: IField, groups: IGroup[]}){
    this.field = data.field;
    const customType = this._retriverService.getCustomAttributeFromField(data.field, 'customType', '');
    if(customType === 'WorkshopLocator') {
      this.stepCorrente = "scelta_installatore";
    }else{
      if(data?.field?.customAttributes?.AgenziaLocator){
        Helpers.SessionHelper.setData('QUOTAZIONE_PEGA', {datiAnagrafici: {prodottoLocator: data?.field?.customAttributes?.AgenziaLocator}});
      }
    }
    this.actionsSet = data?.field?.control?.actionSets;
    this._locatorAction = data?.field?.customAttributes?.componentID === 'locatorSubmitAction';

    this._addFormGroupValue({'testValidatore': ''});
    this.areDataSetted = true;
  }

  public handleComponentLoading(){
    this._messagesService.setLoading(false);
  }

  private async _arricchisciDati(agency: Agency): Promise<Agency & { email?: string, provinceAcronym?: string }>{
    let esito = agency;
    if(agency){
      this._messagesService.setLoading(true);
      try{
        const request = new Request(`poiLocator/v1/poi/${agency.code}`, false);
        const response = await this._baseCommunicator.get(request);
        if(response && response.POIArray && response.POIArray[0])
          esito = response.POIArray[0];
      }catch(e){
        console.error("Errore arricchimento dati", String(e));
      }
      this._messagesService.setLoading(false);
    }
    return esito;
  }

  private _addFormGroupValue(dataToSend: {[key: string]: string}){
    const formGroup = this._statusService.getFormGroup();
    if(formGroup){
      //Rimuoviamo i vecchi controlli
      if(this._lastDataToSend){
        for(const key in this._lastDataToSend)
          formGroup.removeControl(key);
      }
      this._lastDataToSend = dataToSend;
      for(const key in dataToSend){
        const control = new FormControl(dataToSend[key]);
        control.setValidators(Validators.required);
        formGroup.addControl(key, control);
      }
    }
  }

  public async handleSelezioneAgenzia(agency?: Agency){
    const dati = await this._arricchisciDati(agency);
    const dataToSend = {
      'Agenzia.DescrizioneAgenzia': dati.management || dati.name || '-',
      'Agenzia.Telefono': dati.phoneNumber || '-',
      'Agenzia.Email': dati.email || dati.mail || '-',
      'Agenzia.Indirizzo.NomeStrada': dati.address || '-',
      'Agenzia.Indirizzo.Provincia': dati.provinceAcronym || '-',
      'Agenzia.Indirizzo.NumeroCivico': '-' ,
      'Agenzia.Indirizzo.Cap': dati.zipCode || '-',
      'Agenzia.Indirizzo.Comune': dati.town || '-',
      'Agenzia.CodiceAgenzia': dati.code || '-'
    }
    this._addFormGroupValue(dataToSend);
    this._callNextPage();
  }

  public async handleSelezioneInstallatore(agency?: Agency){
    const dati = await this._arricchisciDati(agency);
    const dataToSend = {
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.NomeOfficina' : dati.code_ext || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.TelefonoOfficina' : dati.phoneNumber || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.EmailOfficina' : dati.email || dati.mail || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.Indirizzo.NomeStrada' : dati.address || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.Indirizzo.Provincia' : dati.provinceAcronym || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.Indirizzo.NumeroCivico' : '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.Indirizzo.Cap' : dati.zipCode || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.Indirizzo.Comune' : dati.town || '-',
      'GestioneProcesso.ContattiUniboxTemp.InstallatoreUnibox.CodiceOfficina' : dati.code || '-'
    }
    this._addFormGroupValue(dataToSend);
    this._callNextPage();
  }

  private _callNextPage(){
    if(this._locatorAction) {
      this.onChange();
    }
  }

  public ngOnDestroy(): void {
    const formGroup = this._statusService.getFormGroup();
    if(formGroup) {
      if (this._lastDataToSend) {
        for (const key in this._lastDataToSend)
          formGroup.removeControl(key);
      }
    }
    Helpers.SessionHelper.deleteData('QUOTAZIONE_PEGA');
  }
}
