<ng-container
  *ngIf="this.localhostMock">
  <button (click)="handleSelezioneInstallatore(this.mockInstallatore)">Prova mock installatore</button>
  <button (click)="handleSelezioneAgenzia(this.mockAgenzia)">Prova mock agenzia</button>
</ng-container>
<locator-wrapper
  *ngIf="this.areDataSetted"
  [stepCorrente]="this.stepCorrente"
  (onComponentReady)="this.handleComponentLoading()"
  (onAgencySelected)="this.handleSelezioneAgenzia($event)"
  (onInstallerSelected)="this.handleSelezioneInstallatore($event)">
</locator-wrapper>
