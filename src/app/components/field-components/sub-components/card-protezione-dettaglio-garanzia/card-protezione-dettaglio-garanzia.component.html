<div
  class="CardProtezioneDettaglioGaranziaContainer"
  [ngClass]="{selected: this.selected}">
  <span *ngIf="this.selected" class="CardProtezioneCheck"></span>
  <div
    class="CardProtezioneDettaglioGaranziaBody"
    [ngClass]="{selected: this.selected}">
    <div class="CardProtezioneDettaglioGaranziaContent">
      <span class="CardProtezioneDettaglioGaranziaHeader">
        <caption-elem *ngIf="this.nomeGaranzia" [caption]="this.nomeGaranzia"></caption-elem>
        <tpd-px-icon *ngIf="this.tooltipIcon && !this.selected" [data]="{field: this.tooltipIcon}"></tpd-px-icon>
      </span>
      <caption-elem *ngIf="this.descrizioneGaranzia && !this.selected" [caption]="this.descrizioneGaranzia"></caption-elem>
      <caption-elem *ngIf="this.descrizioneGaranziaSelezionata && this.selected" [caption]="this.descrizioneGaranziaSelezionata"></caption-elem>
    </div>
    <tpd-px-checkbox
      *ngIf="this.checkboxGaranzia"
      #checkboxGaranziaField
      [ngClass]="{hidden: this.selected}"
      [data]="{field: this.checkboxGaranzia}"></tpd-px-checkbox>
    <custom-text-style
      *ngIf="this.selected"
      style="cursor: pointer"
      (click)="this.handleClickRimozione()"
      [textCss]="this.checkboxTextStyle"
      [content]="this.checkboxLabel"></custom-text-style>
  </div>
</div>
