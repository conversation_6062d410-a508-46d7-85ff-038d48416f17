import { Component, Input, ViewChild } from '@angular/core';
import { CustomData } from '../../px-hidden/px-hidden.component';
import { ICaption, IField} from "../../../../models/models.commons";
import RetrieverService from "../../../../services/retriever.service";
import { TextCss, TextHandlerService } from "../../../../services/text-handler.service";
import { CheckboxComponent } from "../../px-checkbox/checkbox.component";


@Component({
  templateUrl: './card-protezione-dettaglio-garanzia.component.html',
  styleUrls: ['./card-protezione-dettaglio-garanzia.component.scss'],
  selector: 'card-protezione-dettaglio-garanzia',
})
export default class CardProtezioneDettaglioGaranzia {
  @ViewChild('checkboxGaranziaField') checkboxGaranziaField: CheckboxComponent;

  public nomeGaranzia: ICaption;
  public descrizioneGaranzia: ICaption;
  public descrizioneGaranziaSelezionata: ICaption;
  public checkboxGaranzia: IField;
  public checkboxLabel: string;
  public checkboxTextStyle: TextCss;
  public tooltipIcon: IField;


  constructor(
    private _retriverService: RetrieverService,
    private _textHandelrService: TextHandlerService
  ) {
  }

  @Input() set data(input: CustomData | undefined) {
    this.nomeGaranzia = this._retriverService.getFirstCaptionByControlFormat(input.groups, 'NomeGaranzia');
    this.descrizioneGaranzia = this._retriverService.getFirstCaptionByControlFormat(input.groups, 'DescrizioneGaranzia');
    this.descrizioneGaranziaSelezionata = this._retriverService.getFirstCaptionByControlFormat(input.groups, 'DescrizioneGaranziaSelezionata');
    this.checkboxGaranzia = this._retriverService.getFirstFieldInGroupsByType(input.groups, 'pxCheckbox');
    this.tooltipIcon = this._retriverService.getFirstFieldInGroupsByType(input.groups, 'pxIcon');

    if(this.nomeGaranzia)
      this.nomeGaranzia.control.format = this.selected ? 'TEXT APP BDB16 WEB BDB20 BDB20 BDB16' : 'TEXT APP BDB16 WEB BDB16 BDB16 BDB13';
    if(this.descrizioneGaranzia)
      this.descrizioneGaranzia.control.format = 'TEXT APP BDB16 WEB BDL13 BDL13 BDL13';
    if(this.descrizioneGaranziaSelezionata)
      this.descrizioneGaranziaSelezionata.control.format = 'TEXT APP BDB16 WEB BDL13 BDL13 BDL13';
    if(this.checkboxGaranzia) {
      this.checkboxGaranzia.label = '';
      this.checkboxLabel = this.checkboxGaranzia?.customAttributes?.removeCheckboxLabel?? 'Rimuovi';
      this.checkboxTextStyle = this._textHandelrService.getTextCss('TEXT APP BDB16 WEB BDBN14 BDBN14 BDBN14');
    }
    if(this.tooltipIcon){
      this.tooltipIcon.customAttributes.resource = 'lightInfo';
      this.tooltipIcon.customAttributes.webResponsiveSize = '24L 24L 24L';
    }
  }

  public handleClickRimozione(){
    if(this.checkboxGaranziaField){
      this.checkboxGaranziaField.onClick(!this.selected);
    }
  }

  public get selected(): boolean{
    return this.checkboxGaranzia?.value === 'true';
  }
}
