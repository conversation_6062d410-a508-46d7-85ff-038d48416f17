@import "../../../../../styles.scss";

.CardProtezioneDettaglioGaranziaContainer {
  display: flex;
  flex-direction: row;
  width: 100%;
  background-color: white;
  border-radius: 24px;

  min-height: 125px;
  height: auto;

  .hidden{
    display: none;
  }

  >.CardProtezioneCheck {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: $blue-primary;
    padding: 0 20px;
    border-top-left-radius: 24px;
    border-bottom-left-radius: 24px;

    &::after{
      content: "";
      aspect-ratio: 1 / 1;
      display: block;
      width: 24px;
      flex-shrink: 0;

      background-image: url("/NextAssets/interprete-pu/GreenCheck.svg");
      background-size: 90%;
      background-position: center center;
      background-repeat: no-repeat;
    }
  }

  >.CardProtezioneDettaglioGaranziaBody {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 24px;
    flex-grow: 1;
    gap: 5px;

    @media #{$bkp_mobile_only}{
      padding: 16px;
    }

    &.selected{
      flex-direction: column;
      justify-content: flex-start;
      gap: 12px
    }

    >.CardProtezioneDettaglioGaranziaContent {
      display: flex;
      flex-direction: column;
      gap: 12px;

      >.CardProtezioneDettaglioGaranziaHeader {
        display: flex;
        flex-direction: row;
        gap: 4px;
      }
    }
  }
}
