@import "../../../../../styles.scss";

.CardProtezioneContainer{
  display: flex;
  flex-direction: column;

  border: 2px solid $background-card-disabled;
  border-radius: 24px;
  gap: 12px;
  padding: 24px;
  height: auto;
  min-height: 150px;

  max-width: calc(100vw - 32px);

  @media #{$bkp_mobile} {
    width: 340px;
  }

  @media #{$bkp_tablet} {
    width: 340px;
  }

  @media #{$bkp_desktop} {
    width: 360px;
  }

  >.CardProtezioneHeader{
    display: flex;
    flex-direction: row;
    gap: 4px;
    align-items: center;

    >.CardProtezioneTitolo{
      color: $blue-primary;
      font-family: "Unipol Bold";
      font-size: 16px;
    }
  }

  >.CardProtezioneBody{
    color: $blue-primary;
    font-family: "Unipol Medium";
    font-size: 13px;
  }

  >.CardProtezioneFooter{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-end;

    @media #{$bkp_mobile} {
      margin-top: 2px;
    }

    @media #{$bkp_tablet} {
      margin-top: 8px;
    }

    @media #{$bkp_desktop} {
      margin-top: 18px;
    }

    >.CardProtezioneFooterValori{
      display: flex;
      flex-direction: column;
      gap: 5px;

      >.ValoreElement::ng-deep{
        display: flex;
        flex-direction: row;
        gap: 4px;
        font-family: "Unipol Bold";
        color: $blue-primary;
        font-size: 13px;

        .mrg-label-input{
          margin: 0 !important;
          padding: 0 !important;
          line-height: unset;
        }
      }
    }

    >.CardProtezionePremioLordo{
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      >.PremioLordo{
        color: $blue-primary;
        font-family: "Unipol Bold";
        font-size: 18px;
      }

      >.Occorrenza{
        color: $blue-primary;
        font-family: Unipol;
        font-size: 11px;
      }
    }
  }
}
