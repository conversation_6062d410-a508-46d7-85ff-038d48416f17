<div
  class="CardProtezioneContainer">
  <span class="CardProtezioneHeader">
    <span class="CardProtezioneTitolo">{{this.nomeGaranzia}}</span>
    <field [data]="{field: this.tooltipGaranzia}"></field>
    <field
      style="margin-left: auto"
      [data]="{field: checkGaranzia}"></field>
  </span>
  <div class="CardProtezioneBody">
    {{this.descrizioneGaranzia}}
  </div>
  <div class="CardProtezioneFooter">
    <div class="CardProtezioneFooterValori">
      <span
        *ngFor="let attributo of this.attributiGaranzia"
        class="ValoreElement">
        {{attributo.label}}:
        <ng-container *ngFor="let valore of attributo.values">
          <field [data]="{field: valore}"></field>
          <span *ngIf="this.isCurrency(valore)">€</span>
        </ng-container>
      </span>
    </div>
    <div class="CardProtezionePremioLordo">
      <span class="PremioLordo">{{this.formattaValuta(this.premioLordo)}} €</span>
      <span class="Occorrenza">{{this.occorrenzaPrezzoGaranzia}}</span>
    </div>
  </div>
</div>
