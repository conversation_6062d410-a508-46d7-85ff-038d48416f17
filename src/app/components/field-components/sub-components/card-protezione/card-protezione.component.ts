import { Component, Input } from '@angular/core';
import {
  IField,
} from '../../../../models/models.commons';
import { CustomData } from '../../px-hidden/px-hidden.component';
import RetrieverService from "../../../../services/retriever.service";
import { TextHandlerService } from "../../../../services/text-handler.service";

@Component({
  selector: 'tpd-card-protezione',
  templateUrl: './card-protezione.component.html',
  styleUrls: ['./card-protezione.component.scss'],
})
export class CardProtezioneComponent {
  public isSelected = false;

  public nomeGaranzia: string;
  public tooltipGaranzia: IField;
  public descrizioneGaranzia: string;
  public premioLordo: string;
  public occorrenzaPrezzoGaranzia: string;
  public checkGaranzia: IField;

  public attributiGaranzia: {label: string, values: IField[]}[] = [];

  constructor(
    private _retrieverService: RetrieverService,
    private _textHandlerService: TextHandlerService
  ) {}

  @Input() set data(input: CustomData | undefined) {
    this.nomeGaranzia = this._retrieverService.captionToValue(this._retrieverService.getFirstCaptionByControlFormat(input.groups, 'NomeGaranzia')).value;
    this.tooltipGaranzia = this._retrieverService.getFirstIconInGroupsByResource(input.groups, 'info');
    this.descrizioneGaranzia = this._retrieverService.captionToValue(this._retrieverService.getFirstCaptionByControlFormat(input.groups, 'DescrizioneGaranzia')).value;
    this.premioLordo = this._retrieverService.fieldToValue(this._retrieverService.getFirstFieldInGroupsByType(input.groups, 'pxCurrency'));
    this.occorrenzaPrezzoGaranzia = this._retrieverService.captionToValue(this._retrieverService.getFirstCaptionByControlFormat(input.groups, 'TestoPrezzoGaranzia')).value;
    this.checkGaranzia = this._retrieverService.getFirstFieldInGroupsByType(input.groups, 'pxCheckbox');

    const customComponents = this._retrieverService.getAllLayoutInGroupsByGroupFormat(input.groups, 'CustomComponent');
    for(const customComponent of customComponents){
      this.attributiGaranzia.push({
        label: this._retrieverService.captionToValue(this._retrieverService.getFirstCaptionByControlFormat(customComponent.groups, 'ChiaveAttributoGaranzia')).value,
        values: this._retrieverService.getEachVisibleFiledInGroups(customComponent.groups)
      })
    }

    if(this.checkGaranzia){
      this.isSelected = this.checkGaranzia?.value === 'true';
      this.checkGaranzia.label = '';
    }
  }

  public formattaValuta(valuta: string): string{
    return this._textHandlerService.formattaValuta(valuta);
  }

  public isCurrency(field: IField): boolean{
    return field?.control?.type === 'pxCurrecy';
  }
}
