import { Component, Input, OnDestroy } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Subscription } from 'rxjs';
import {
  ICaption,
  IField,
  IGroup,
  ILayout,
  IParagraph,
  IRow,
  IView,
} from '../../../../models/models.commons';
import { StatusService } from '../../../../services/status.service';
import { CustomData } from '../../px-hidden/px-hidden.component';

@Component({
  selector: 'tpd-card-garanzia',
  templateUrl: './card-garanzia.component.html',
  styleUrls: ['./card-garanzia.component.scss'],
})
export class CardGaranziaComponent implements OnDestroy {
  private _data: CustomData | undefined;
  public field: IField | undefined;
  public groups: IGroup[] = [];
  public cardGaranzia: IGroup[] = [];
  public groupAdvice: IGroup[] = [];
  public checkboxSubscription: Subscription | undefined;
  public customAttributes: any;
  public cardDisabilitata = false;
  public fg: FormGroup | undefined;
  public isSelected = false;
  public checkField: IField | undefined;
  public buttonField: IField | undefined;
  public garanziaCap: ICaption | undefined;
  public premioCap: ICaption | undefined;
  public tagsCap: IGroup[] = [];
  public chiaviAttributiGaranzia: IGroup[] = [];
  public valoriAttributiGaranzia: IGroup[] = [];
  public rowsListaAttributi: IRow[] = [];

  constructor(private statusService: StatusService) {
    /*Do Nothing*/
  }

  @Input() set data(input: CustomData | undefined) {
    if (input) {
      this.groups = input.groups ?? [];
      this.field = input.field;
      for (const group of this.groups) this.filterData(group);

      this.listenValue();
      if (this.field) this.customAttributes = this.field.customAttributes;
      this.cardDisabilitata =
        !(this.customAttributes && this.customAttributes.cardAbilitata === 'true');
    }
  }

  get data() {
    return this._data;
  }

  listenValue(): void {
    this.fg = this.statusService.getFormGroup();
    this.checkboxSubscription && this.checkboxSubscription.unsubscribe();
    this.checkboxSubscription = this.fg.valueChanges.subscribe((val) => {
      if (this.checkField && this.checkField.reference)
        this.isSelected = val[this.checkField.reference as keyof typeof val];
    });
  }

  filterData(group: IGroup) {
    if (group) {
      const field: IField | undefined = group.field;
      const caption: ICaption | undefined = group.caption;
      const paragraph: IParagraph | undefined = group.paragraph;
      const view: IView | undefined = group.view;
      const layout: ILayout | undefined = group.layout;
      const rows: IRow[] | undefined = layout?.rows;

      if (
        (field && field.visible) ||
        (caption && caption.visible) ||
        (paragraph && paragraph.visible)
      ) {
        this.cardGaranzia.push(group);

        if (field?.control?.type === 'pxCheckbox')
          this.checkField = group.field;

        if (field?.customAttributes?.ComponentPurpose === 'LinkGaranzia')
          this.buttonField = group.field;

        if (caption?.control?.format) {
          switch (caption.control.format) {
            case 'NomeGaranzia':
              this.garanziaCap = caption;
              break;
            case 'PrezzoGaranzia':
              this.premioCap = caption;
              break;
            case 'Tag alert':
            case 'Tag property':
              this.tagsCap.push(group);
              break;
            case 'ChiaveAttributoGaranzia':
              this.chiaviAttributiGaranzia.push(group);
              break;
            case 'ValoreAttributoGaranzia':
              this.valoriAttributiGaranzia.push(group);
              break;
          }
        }
      } else {
        if (view && view.visible && view.groups) {
          view.groups.forEach((subGroup) => this.filterData(subGroup));
        } else if (
          layout &&
          layout.visible &&
          layout.layoutFormat === 'SIMPLELAYOUT'
        ) {
          if (layout.groupFormat === 'AdviceBox')
            this.groupAdvice = layout.groups ?? [];

          if (layout.groups)
            layout.groups.forEach((subGroup) => this.filterData(subGroup));
        } else if (rows) {
          for (const row of rows) {
            if (row.groups)
              row.groups.forEach((subGroup) => this.filterData(subGroup));
          }
        }
      }
    }
  }

  ngOnDestroy(): void {
    this.checkboxSubscription && this.checkboxSubscription.unsubscribe();
  }
}
