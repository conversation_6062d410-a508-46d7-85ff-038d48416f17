<div
  class="card-garanzia"
  [ngClass]="{ 'dark-border': isSelected, 'card-disabled': cardDisabilitata }"
  *ngIf="cardGaranzia"
>
  <div class="row-1">
    <div class="d-flex">
      <tpd-px-checkbox
        *ngIf="this.checkField"
        [data]="{ field: checkField }"
        class="checkG a-self-center"
      ></tpd-px-checkbox>
      <caption-elem *ngIf="garanziaCap" [caption]="garanziaCap"></caption-elem>
    </div>
    <caption-elem *ngIf="premioCap" [caption]="premioCap"></caption-elem>
  </div>

  <div
    *ngIf="chiaviAttributiGaranzia && valoriAttributiGaranzia"
    class="mt-05em"
  >
    <div
      class="d-flex attributes"
      *ngFor="let chiave of chiaviAttributiGaranzia; index as i"
    >
      <div *ngIf="!cardDisabilitata">
        <caption-elem [caption]="chiave.caption"></caption-elem>
        <caption-elem
          [caption]="valoriAttributiGaranzia[i].caption"
        ></caption-elem>
      </div>
    </div>
  </div>

  <div *ngIf="groupAdvice" class="Advice-box">
    <groups [groups]="groupAdvice"></groups>
  </div>

  <div
    class="d-flex"
    [ngClass]="{
      'last-row': tagsCap.length > 0,
      'justify-end': tagsCap.length === 0
    }"
  >
    <div class="tags d-flex" *ngIf="tagsCap">
      <caption-elem
        *ngFor="let tag of tagsCap; index as i"
        [caption]="tag.caption"
      ></caption-elem>
    </div>

    <tpd-button
      [data]="{ field: buttonField }"
      *ngIf="buttonField"
      class="align-center mt-05em"
    ></tpd-button>
  </div>
</div>
