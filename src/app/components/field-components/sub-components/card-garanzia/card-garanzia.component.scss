@import "../../../../../styles.scss";

.card-garanzia {
  border: solid 2px #f0f0f0;
  background-color: white;
  padding: $padding-container;
  margin: 0 auto 1.2rem auto;

  @media #{$bkp_mobile} {
    // width: 90vw;
    .last-row {
      flex-direction: column;
      .align-center {
        align-self: center;
      }
    }
    .justify-end {
      flex-direction: row;
      justify-content: center;
    }
  }

  @media #{$bkp_tablet} {
    width: 90vw;
    .last-row {
      flex-direction: row;
      justify-content: space-between;
    }
    .justify-end {
      flex-direction: row;
      justify-content: flex-end;
    }
  }

  @media #{$bkp_desktop} {
    width: 75vw;
    .last-row {
      flex-direction: row;
      justify-content: space-between;
    }
    .justify-end {
      flex-direction: row;
      justify-content: flex-end;
    }
  }
  border-radius: 3rem;
}
.dark-border {
  border: solid $main_color !important;
}

.row-1 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: nowrap;

  @media #{$bkp_tablet} {
    align-items: center;
  }
}
.w-50 {
  width: 50%;
}
.checkG {
  // padding-top: 10px;
  width: 20px;
}
.checkG.a-self-center {
  @media #{$bkp_mobile} {
    margin-top: 4px;
    align-self: flex-start;
  }
  @media #{$bkp_tablet} {
    align-self: center;
    margin-top: 0;
  }
}
.a-self-center {
  align-self: center;
}
.a-self-start {
  align-self: flex-start;
}
.a-self-end {
  align-self: flex-end;
}
.tags,
.attributes {
  column-gap: 0.5em;
}

.card-disabled {
  background-color: $background-card-disabled;
  border: solid 2px $border-card-disabled;
}
