<div
  *ngIf="this.stepperVersion === 1"
  class="d-flex w-100 empty-bar-pu mrg-stepper-pu"
>
  <div
    class="full-bar-pu"
    [ngStyle]="{ 'width.%': progress }"
  ></div>
</div>

<div
  *ngIf="this.stepperVersion === 2"
  class="StepperContainer">
  <ng-container
    *ngFor="let step of this.steps; index as i">
    <span
      *ngIf="!this.isSpace(step)"
      class="StepperStep">
      <span
        class="StepperNumber"
        [ngClass]="{checked: this.isCheckLabel(i)}">
        {{ this.isCheckLabel(i) ? '' : (i / 2) + 1 }}
      </span>
      <custom-text-style
        class="StepName"
        [textCss]="this.getLabelFormat(i)"
        [content]="step"></custom-text-style>
    </span>
    <span
      class="StepperSpace"
      *ngIf="this.isSpace(step)">
    </span>
  </ng-container>
</div>
