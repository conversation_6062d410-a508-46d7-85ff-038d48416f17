@import "../../../../../styles.scss";

.empty-bar {
  background-color: #d8d8d8;
  height: 2px;
  margin-top: 16px;
  margin-bottom: 16px;
  &-pu {
    background-color: #cbdeea;
    height: 4px;
    @media #{$bkp_tablet} {
      height: 12px;
    }
  }
}

.full-bar {
  background-color: #0f3250;
  height: 7px;
  margin-top: -3px;
  &-pu {
    background-color: #5393bc;
    height: 4px;
    @media #{$bkp_tablet} {
      height: 12px;
    }
  }
}

.mrg-stepper {
  @media #{$bkp_mobile} {
    margin-bottom: 32px;
  }

  @media #{$bkp_tablet} {
    margin-bottom: 50px;
  }

  @media #{$bkp_desktop} {
    margin-bottom: 75px;
  }
}

.StepperContainer{

  display: flex;
  flex-direction: row;
  align-items: center;

  width: 100%;
  background-color: white;

  @media #{$bkp_mobile} {
    padding: 12px calc(100% * (2 / 24)) 31px;
  }

  @media #{$bkp_tablet} {
    padding: 24px calc(100% * (4 / 24)) 39px;
  }

  @media #{$bkp_desktop} {
    padding: 24px calc(100% * (6 / 24)) 43px;
  }

  border-bottom: 1px solid $ivory;
  margin-bottom: 20px;

  >.StepperStep{
    display: block;
    width: auto;
    height: auto;
    flex-shrink: 0;
    position: relative;

    >.StepperNumber{
      aspect-ratio: 1 / 1;

      display: flex;
      align-items: center;
      justify-content: center;

      width: 24px;
      border-radius: 50%;
      margin: 0 12px;

      background-color: $ivory;
      color: $grey;
      font-family: "Unipol Medium";
      font-weight: 500;
      font-size: 14px;

      &.checked{
        background-color: $secondary-lightest;
        &::after{
          content: "";
          aspect-ratio: 5 / 3;
          width: 30%;
          border-left: 1px solid $blue-primary;
          border-bottom: 1px solid $blue-primary;
          transform: rotateZ(-45deg);
        }
      }
    }

    >.StepName{
      position: absolute;
      top: calc(100% + 8px);
      left: 50%;

      transform: translateX(-50%);
    }
  }

  >.StepperSpace{
    display: block;
    flex-grow: 1;
    height: 1px;

    background-color: $border-card-disabled;
  }
}
