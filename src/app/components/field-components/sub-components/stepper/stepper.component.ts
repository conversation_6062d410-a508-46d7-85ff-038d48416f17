import { Component, Input} from '@angular/core';
import { IField } from '../../../../models/models.commons';
import { TextCss, TextHandlerService } from "../../../../services/text-handler.service";
import RetrieverService from "../../../../services/retriever.service";

@Component({
  selector: 'tpd-stepper',
  templateUrl: './stepper.component.html',
  styleUrls: ['./stepper.component.scss'],
})
export class StepperComponent {
  private _spaceId = "space";
  private _totalSteps = 0;
  private _labelFormatDeselected: TextCss;
  private _labelFormatPreviousStep: TextCss;
  private _labelFormatCurrentStep: TextCss;

  public stepperVersion: 1 | 2 = 2;

  public progress = '0';

  public currentStep: number;
  public steps: string[];

  constructor(
    private _retrieverService: RetrieverService,
    private _textHandlerService: TextHandlerService
  ) {}

  @Input() set field(input: IField | undefined) {
    this.progress = this._retrieverService.getCustomAttributeFromField(input, 'progress', '0');
    this.currentStep = this._retrieverService.getCustomAttributeFromFieldAsNumber(input, 'currentStep', 0);
    this._labelFormatDeselected = this._textHandlerService.getTextCss(this._retrieverService.getCustomAttributeFromField(input, 'labelFormatDeselected'));
    this._labelFormatPreviousStep = this._textHandlerService.getTextCss(this._retrieverService.getCustomAttributeFromField(input, 'labelFormatPreviousStep'));
    this._labelFormatCurrentStep = this._textHandlerService.getTextCss(this._retrieverService.getCustomAttributeFromField(input, 'labelFormatCurrentStep'));

    const trimmedSteps = this._retrieverService.getCustomAttributeFromField(input, 'labelsStepper').split('|').map(label => label.trim());
    this._totalSteps = trimmedSteps.length;
    if(trimmedSteps){
      this.steps = [];
      for(let i = 0; i < (trimmedSteps.length * 2) - 1; i++) {
        this.steps.push(i % 2 === 0 ? trimmedSteps[i * 0.5] : this._spaceId);
      }
    }
  }

  public getStatus(stepIndex): 0 | 1 | 2{
    let esito: 0 | 1 | 2 = 0;

    const checkIndex = (stepIndex / 2) + 1;
    if(checkIndex === this.currentStep)
      esito = 1
    if(checkIndex < this.currentStep)
      esito = 2;
    if(checkIndex > this.currentStep)
      esito = 0;

    return esito;
  }

  public getLabelFormat(stepIndex): TextCss{
    let esito = undefined;

    const status = this.getStatus(stepIndex);
    if(status === 1)
      esito = this._labelFormatCurrentStep;
    if(status === 2)
      esito = this._labelFormatPreviousStep;
    if(status === 0)
      esito = this._labelFormatDeselected;

    return esito;
  }

  public isCheckLabel(stepIndex): boolean{
    const checkIndex = (stepIndex / 2) + 1;
    return checkIndex <= this.currentStep;
  }

  public isSpace(step: string): boolean{
    return this._spaceId === step;
  }
}
