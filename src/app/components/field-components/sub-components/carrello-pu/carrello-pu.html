<div
  *ngIf="this.carrelloPURendering === this.carrelloPURenderingEnum.Carrello"
  class="PuCarrelloContainer">
  <span
    class="PuCarrelloHeader">
    <span class="UnicaIcon">
      <field *ngIf="this.headerUnicaIcon" [data]="{field: this.headerUnicaIcon}"></field>
    </span>
    <span
      *ngIf="this.headerRibbonValue && false"
      class="Ribbon">
      <caption-elem [caption]="headerRibbonValue"></caption-elem>
    </span>
  </span>
  <groups *ngIf="this.renderGroups" [groups]="this.renderGroups"></groups>
</div>

<div
  *ngIf="this.carrelloPURendering === this.carrelloPURenderingEnum.Footer"
  class="CarrelloPuFooterContainer">
  <span
    class="CarrelloPuFooterHeader"
    [ngClass]="{
      statoAperto: this.statoFooterAperto
    }"
    (click)="this.handleVediDiPiuClick()">
    <custom-text-style [textCss]="this.stileTestoVedi" [content]="this.testoVedi"></custom-text-style>
  </span>
  <div
    *ngIf="this.statoFooterAperto"
    class="CarrelloPuFooterBody">
    <layout *ngIf="this.contentFooter" [layout]="this.contentFooter"></layout>
  </div>
</div>
