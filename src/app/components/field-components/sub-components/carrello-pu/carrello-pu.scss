@import "../../../../../styles.scss";

.PuCarrelloContainer{
  display: flex;
  flex-direction: column;
  padding: 24px 16px;
  border: 1px solid $border-card-disabled;
  border-radius: 24px;

  max-width: 100%;
  width: 528px;
  margin: 0 auto;

  @media #{$bkp_mobile_only} {
    width: 100%;
  }

  >.PuCarrelloHeader{
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    margin: 0 -16px;

    >.UnicaIcon{
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 16px;

      background-color: $blue-primary;
      padding: 10px 0 10px 25px;

      position: relative;

      &::before{
        content: "";
        aspect-ratio: 1 / 1;
        display: block;
        height: 100%;
        background-color: inherit;
        position: absolute;
        top: 0;
        left: 100%;
        transform: translateX(-50%);

        border-radius: 50%;
        z-index: -10;
      }
    }

    >.Ribbon{
      display: flex;
      flex-direction: row;
      background-color: $ribbon-color;
      padding: 2px 8px;
      padding-left: 0;

      position: relative;

      &::before{
        content: "";
        aspect-ratio: 1 / 1;
        display: block;
        height: 100%;
        background-color: inherit;
        position: absolute;
        top: 0;
        left: 0;
        transform: translateX(-50%);

        border-radius: 50%;
        z-index: -10;
      }
    }
  }
}
