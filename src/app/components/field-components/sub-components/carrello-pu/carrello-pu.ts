import { Component, Input } from '@angular/core';
import { I<PERSON><PERSON><PERSON>, IField, IGroup, ILayout } from "../../../../models/models.commons";
import { CustomData } from '../../px-hidden/px-hidden.component';
import RetrieverService from "../../../../services/retriever.service";
import { TextCss, TextHandlerService } from "src/app/services/text-handler.service";

enum CarrelloPURendering{
  <PERSON><PERSON>,
  Footer
}

@Component({
  selector: 'carrello-pu',
  templateUrl: './carrello-pu.html',
  styleUrls: ['./carrello-pu.scss', './carrello-pu-footer.scss'],
})
export class CarelloPUComponent {
  public carrelloPURendering = CarrelloPURendering.Carrello;
  public carrelloPURenderingEnum = CarrelloPURendering;

  //carrellopu
  public headerUnicaIcon: IField;
  public headerRibbonValue: ICaption;
  public renderGroups: IGroup[];

  //footer
  public testoVediDiPiu: string;
  public testoVediDiMeno: string;
  public stileTestoVedi: TextCss;
  public contentFooter: ILayout;

  public statoFooterAperto = true;

  constructor(
    private _retrieverService: RetrieverService,
    private _textHandlerService: TextHandlerService
  ) {
  }

  @Input() set data(input: CustomData) {
    if (input) {
      switch (input?.field?.customAttributes?.customType){
        case 'CarrelloPUFooter':
          this._setupCarrelloPuFooter(input);
          this.carrelloPURendering = CarrelloPURendering.Footer;
          break;
        default:
          this._setupCarrelloPu(input);
          this.carrelloPURendering = CarrelloPURendering.Carrello;
          break;
      }
    }
  }

  private _setupCarrelloPu(input: CustomData){
    const headerLayout = this._retrieverService.getFirstLayoutInGroupsByGroupFormat(input.groups, 'CarrelloPUHeader');
    if(headerLayout){
      this.headerUnicaIcon = this._retrieverService.getFirstIconInGroupsByResource(headerLayout.groups, 'unicoLogo');
      this.headerUnicaIcon.customAttributes.webResponsiveSize = "40L 40L 40L";

      const ribbonLayout = this._retrieverService.getFirstLayoutInGroupsByGroupFormat(input.groups, 'CarrelloPURibbon');
      if(ribbonLayout){
        this.headerRibbonValue = this._retrieverService.getFirstCaptionInGroups(ribbonLayout.groups);
        if(this.headerRibbonValue)
          this.headerRibbonValue.value = `-${this.headerRibbonValue.value}%`;
      }
    }
    this._filtraRenderGroups(input.groups);
  }

  private _filtraRenderGroups(groups: IGroup[]){
    this.renderGroups = groups.filter((group) => {
      let esito = true;
      if(group?.field?.control?.type === 'pxHidden')
        esito = false;
      if(group?.view && this._retrieverService.getFirstLayoutInGroupsByGroupFormat(group.view.groups, 'CarrelloPUHeader'))
        esito = false;
      if(group?.layout?.groupFormat === 'CarrelloPUHeader')
        esito = false;

      return esito;
    });
  }

  private _setupCarrelloPuFooter(input: CustomData){
    this.testoVediDiMeno = this._retrieverService.getCustomAttributeFromField(input.field, 'textShowLess');
    this.testoVediDiPiu = this._retrieverService.getCustomAttributeFromField(input.field, 'textShowMore');
    this.stileTestoVedi = this._textHandlerService.getTextCss(this._retrieverService.getCustomAttributeFromField(input.field, 'textShowStyle'));
    this.contentFooter = input.groups[1].layout;
  }

  public handleVediDiPiuClick(){
    this.statoFooterAperto = !this.statoFooterAperto;
  }

  public get testoVedi(): string{
    return this.statoFooterAperto ? this.testoVediDiMeno : this.testoVediDiPiu;
  }
}
