@import "../../../../../styles.scss";

.CarrelloPuFooterContainer{
  display: flex;
  flex-direction: column;
  width: 100%;

  >.<PERSON>elloPuFooterHeader{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: calc(100% + 32px);
    gap: 16px;
    padding: 27px 0;
    margin: 0 -16px;
    z-index: 100;
    background-color: white;

    cursor: pointer;
    border-bottom: 1px solid $border-card-disabled;
    border-bottom-left-radius: 24px;
    border-bottom-right-radius: 24px;

    @media #{$bkp_mobile_only} {
      padding: 24px 0;;
    }

    &::after{
      content: ">";
      font-family: "Unipol Bold";
      color: $blue-primary;
      font-size: 20px;
      transform: rotateZ(90deg) scaleY(1.6) scaleX(0.6);
    }

    &.statoAperto{
      &::after{
        transform: rotateZ(270deg) scaleY(1.6) scaleX(0.6);
      }
    }
  }

  >.CarrelloPuFooterBody{
    background-color: $blue-primary;
    width: calc(100% + 32px);
    padding: 16px;
    position: relative;
    margin: 0 -16px;
    border-bottom-right-radius: 24px;
    border-bottom-left-radius: 24px;

    &::before{
      content: "";
      background-color: inherit;
      width: 100%;
      height: 50px;

      position: absolute;
      left: 0;
      top: 0;
      transform: translateY(-100%);
    }
  }
}
