import { Component, Input, OnDestroy } from "@angular/core";
import { IActionSet, ICaption, IField, IGroup, IParagraph } from "../../../../models/models.commons";
import RetrieverService from "../../../../services/retriever.service";
import ComponentReferenceStorageService from "../../../../services/component-reference-storage.service";
import { ButtonComponent } from "../../button/button.component";
import { MessagesService } from "../../../../services/messages.service";
import { EventsManaged } from "../../../../utils/events.enum";
import { FormControl } from "@angular/forms";
import { StatusService } from "src/app/services/status.service";

@Component({
  templateUrl: './accordion-checkbox.component.html',
  styleUrls: ['./accordion-checkbox.component.scss'],
  selector: 'accordion-checkbox'
})
export default class AccordionCheckboxComponent implements OnDestroy{
  private _actionSet: IActionSet[] = [];
  private _checkboxReference: string;
  private _checkboxFormControl = new FormControl();

  public accordionTitle: string;
  public accordionSubtitle: string;
  public accordionSubtitleStyle: string;
  public paragraphDescrizioneWeb: IParagraph;
  public captionDescrizioneAttivaWeb: ICaption;
  public fieldValoreAttivoWeb: IField;
  public iconGreenCheck: IField;
  public accordionBody: IGroup[] = [];

  public isDisabled = false;
  public isOpen = false;

  public constructor(
    private _componentReferenceStorageService: ComponentReferenceStorageService,
    private _retrieverService: RetrieverService,
    private _messagesService: MessagesService,
    private _statusService: StatusService
  ) {
  }

  @Input() set data(data: {field: IField, groups: IGroup[]}){
    this.accordionTitle = data?.field?.customAttributes?.title || "";
    this.accordionSubtitle = data?.field?.customAttributes?.subtitle || "";
    this.accordionSubtitleStyle = data?.field?.customAttributes?.subtitleStyle || "TEXT APP GDL16 WEB BDL16 BDL16 BDL16";

    const pegaCheckbox = this._retrieverService.getFirstFieldInGroupsByComponentPurpose(data.groups, 'WebStatusCheckbox');
    if(pegaCheckbox){
      this._actionSet = pegaCheckbox?.control?.actionSets || [];
      this._checkboxReference = pegaCheckbox.reference;
      this._statusService.addControlFormGroup(this._checkboxReference, this._checkboxFormControl);
      this._checkboxFormControl.setValue(pegaCheckbox.value === 'true');
    }

    if(this.accordionTitle && pegaCheckbox)
      this.isOpen = pegaCheckbox.value === 'true';
    this.paragraphDescrizioneWeb = this._retrieverService.getFirstParagraphByControlFormat(data.groups, 'DescrizioneWeb');
    this.captionDescrizioneAttivaWeb = this._retrieverService.getFirstCaptionByControlFormat(data.groups, 'DescrizioneAttivaWeb', true);
    if(this.captionDescrizioneAttivaWeb){
      this.isDisabled = true;
      this.isOpen = false;

      this.iconGreenCheck = this._retrieverService.getFirstIconInGroupsByResource(data.groups, 'checkGreen');
      this.fieldValoreAttivoWeb = this._retrieverService.getFirstFieldInGroupsByComponentPurpose(data.groups, 'ValoreAttivoWeb');
    }

    const accordionOpened = this._retrieverService.getFirstLayoutInGroupsByGroupFormat(data.groups, 'AccordionOpened');
    if(accordionOpened)
      this.accordionBody = accordionOpened.groups || [];

    if(this.accordionBody){
      const annullaButton = this._retrieverService.getLeastButtonInGroupsByLabelNotCloned(this.accordionBody, 'Annulla');
      if(annullaButton) {
        annullaButton.elementUniqueId = `${this.accordionTitle}-AccordionCheckboxAnnullaButton`;
        this._componentReferenceStorageService.addOnSetComponentListener(annullaButton.elementUniqueId, (component: ButtonComponent) => {
          component.customClickCallback = { callback: () => this.isOpen = false, ignoraFunzionamentoStandard: true };
        });
      }
    }
  }

  public cambiaStatoApertura() {
    if(!this.isDisabled){
      this.isOpen = this.isOpen = !this.isOpen && !this.isDisabled;
      this._checkboxFormControl.setValue(this.isOpen);
      this._messagesService.onEventExecuted(
        this._actionSet,
        EventsManaged.click,
        {},
        {}
      );
    }
  }

  public ngOnDestroy(): void {
    this._statusService.removeControlFormGroup(this._checkboxReference);
  }
}
