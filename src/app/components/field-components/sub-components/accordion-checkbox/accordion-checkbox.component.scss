@import "../../../../../variables.scss";

.AccordionContainer{
  display: flex;
  flex-direction: column;

  width: 100%;
  padding: 16px;

  border: 2px solid $ivory;

  gap: 24px;

  >.AccordionHeader{
    display: flex;
    flex-direction: row;
    gap: 16px;
    align-items: flex-start;

    >.AccordionHeaderCheckbox{
      aspect-ratio: 1 / 1;

      display: flex;
      justify-content: center;
      align-items: center;

      width: 24px;

      border: 2px solid $blue-primary;
      border-radius: 4px;

      cursor: pointer;
      flex-shrink: 0;

      &.attive{
        background-color: $check-green-color;
        border-color: $check-green-color;
      }

      &.disabled{
        background-color: $check-green-disabled-color !important;
        border-color: $check-green-disabled-color !important;
        cursor: auto !important;
      }

      &::after{
        content: '';
        aspect-ratio: 4 / 2;
        border: 2px solid white;
        border-top: 0;
        border-right: 0;
        width: 30%;
        transform: rotateZ(-45deg);
      }
    }

    >.AccordionHeaderTitle{
      display: flex;
      flex-direction: column;
      width: 100%;
      gap: 4px;
    }
  }

  >.AccordionDisabled{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;

    >.AccordionValoreAttivoContainer{
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-items: center;
      justify-content: flex-start;
      gap: 16px;
      row-gap: 4px;
    }
  }
}
