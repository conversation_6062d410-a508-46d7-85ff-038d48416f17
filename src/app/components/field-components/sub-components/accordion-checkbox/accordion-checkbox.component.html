<span
  class="AccordionContainer"
  [ngClass]="{AccordionOpenStatusDetector: this.isOpen}">
  <span class="AccordionHeader">
    <span
      class="AccordionHeaderCheckbox"
      [ngClass]="{
        attive: this.isOpen,
        disabled: this.isDisabled
      }"
      (click)="this.cambiaStatoApertura()">
    </span>
    <div class="AccordionHeaderTitle">
      <paragraph
        *ngIf="this.paragraphDescrizioneWeb"
        [valoriDiTesta]="['<b>' + this.accordionTitle + ':</b>']"
        [paragraph]="this.paragraphDescrizioneWeb">
      </paragraph>
      <custom-text-style
        *ngIf="this.accordionSubtitle"
        [content]="this.accordionSubtitle"
        [textCssString]="this.accordionSubtitleStyle">
      </custom-text-style>
    </div>
  </span>
  <div
    *ngIf="this.isOpen && !this.isDisabled && this.accordionBody"
    class="AccordionBody">
    <groups [groups]="this.accordionBody">
    </groups>
  </div>
  <div
    *ngIf="this.isDisabled"
    class="AccordionDisabled">
    <caption-elem
      *ngIf="this.captionDescrizioneAttivaWeb"
      [caption]="this.captionDescrizioneAttivaWeb">
    </caption-elem>
    <span class="AccordionValoreAttivoContainer">
      <tpd-input-text *ngIf="this.fieldValoreAttivoWeb" [data]="{field: this.fieldValoreAttivoWeb}"></tpd-input-text>
      <tpd-px-icon [data]="{field: this.iconGreenCheck}"></tpd-px-icon>
    </span>
  </div>
</span>
