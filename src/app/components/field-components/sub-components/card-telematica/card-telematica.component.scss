@import "../../../../../variables.scss";
@import "@tpd-web-angular-libs/angular-library/styles/mixin";
@import "@tpd-web-angular-libs/angular-library/styles/variables";

.CardTelematica {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 20px;
  gap: 16px;

  border: 2px solid $secondary-lightest;
  border-radius: 8px;

  >.CardTelematicaHeader {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    >.HeaderCheckbox{
      aspect-ratio: 1 / 1;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      width: 24px;

      cursor: pointer;
      border: 2px solid $blue-primary;
      border-radius: 50%;

      &.disabled{
        border-color: $check-disabled-color;
        cursor: default;
        background-color: white;
      }

      &.checked{
        border: 0;
        background-color: $check-green-color;

        &.disabled{
          background-color: $check-green-disabled-color;
        }

        &:after{
          content: "";
          aspect-ratio: 5 / 3;
          display: block;
          width: 40%;
          transform: rotateZ(-45deg);
          border-bottom: 2px solid white;
          border-left: 2px solid white;
        }
      }
    }
  }

  >.CardTelematicaFrazionamento {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  >.CardTelematicaDescrizione {
    align-self: center;
  }
}
