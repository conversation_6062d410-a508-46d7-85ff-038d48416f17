import { Component, Input } from "@angular/core";
import { I<PERSON>ap<PERSON>, IField, IGroup, IParagraph } from "../../../../models/models.commons";
import RetrieverService from "../../../../services/retriever.service";
import { MessagesService } from "../../../../services/messages.service";
import { EventsManaged } from "../../../../utils/events.enum";

@Component({
  selector: 'card-telematica',
  templateUrl: './card-telematica.component.html',
  styleUrls: ['./card-telematica.component.scss']
})
export default class CardTelematicaComponent{
  public titoloTelematica: ICaption;
  public checkboxTelematica: IField;
  public canoneTelematica: IField;
  public labelFrazionamentoCanone: ICaption;
  public descrizioneTelematica: IParagraph;

  public constructor(
    private _retriverService: RetrieverService,
    private _messagesService: MessagesService
  ) {

  }

  @Input() set data(data: {field: IField, groups: IGroup[]}){
    if(data){
      this.titoloTelematica = this._retriverService.getFirstCaptionByControlFormat(data.groups, 'TitoloTelematica');
      this.checkboxTelematica = this._retriverService.getFirstFieldInGroupsByType(data.groups, 'pxCheckbox');
      this.canoneTelematica = this._retriverService.getFirstFieldInGroupsByType(data.groups, 'pxCurrency');
      this.labelFrazionamentoCanone = this._retriverService.getFirstCaptionByControlFormat(data.groups, 'FrazionamentoTelematica');
      this.descrizioneTelematica = this._retriverService.getFirstParagraphInGroups(data.groups);
    }
  }

  public clickCheckboxHandler(){
    !this.idDisabled &&
    this._messagesService.onEventExecuted(
      this.checkboxTelematica?.control?.actionSets,
      EventsManaged.change,
      {},
      {
        [this.checkboxTelematica.reference]: !this.isChecked
      }
    )
  }

  public get idDisabled(): boolean{
    return this.checkboxTelematica.disabled;
  }

  public get isChecked(): boolean{
    return this.checkboxTelematica.value === 'true';
  }
}
