<div class="CardTelematica">
  <span class="CardTelematicaHeader">
    <caption-elem *ngIf="this.titoloTelematica" [caption]="this.titoloTelematica"></caption-elem>
    <span
      *ngIf="this.checkboxTelematica"
      class="HeaderCheckbox"
      [ngClass]="{checked: this.isChecked, disabled: this.idDisabled}"
      (click)="this.clickCheckboxHandler()"></span>
  </span>
  <div class="CardTelematicaFrazionamento">
    <field *ngIf="this.canoneTelematica" [data]="{field: this.canoneTelematica}"></field>
    <caption-elem *ngIf="labelFrazionamentoCanone" [caption]="this.labelFrazionamentoCanone"></caption-elem>
  </div>
  <paragraph
    *ngIf="this.descrizioneTelematica"
    class="CardTelematicaDescrizione"
    [paragraph]="this.descrizioneTelematica"></paragraph>
</div>
