import { Component, Input } from '@angular/core';
import { IField } from '../../../../models/models.commons';

interface CustomAttributes {
  TotalStep: string;
  CurrentStep: string;
}

@Component({
  selector: 'circular-stepper',
  templateUrl: './circular-stepper.component.html',
  styleUrls: ['./circular-stepper.component.scss'],
})
export class CircularStepperComponent {
  private _field: IField | undefined;
  private customAttributes: CustomAttributes | undefined;
  public totalStep = 0;
  public currentStep = 0;
  public counter = Array;

  constructor() {
    /* DONOTHING */
  }

  @Input() set field(input: IField | undefined) {
    if (input) {
      this._field = input;
      this.customAttributes = this._field.customAttributes;
      if (this.customAttributes) {
        this.totalStep = Number(this.customAttributes.TotalStep);
        this.currentStep = Number(this.customAttributes.CurrentStep);
      }
    }
  }

  get field() {
    return this._field;
  }
}
