import { Component, Input } from '@angular/core';
import { IDataInputField } from '../../../../components/field/field';
import { IField, IGroup, ILayout } from '../../../../models/models.commons';
import { UtilsService } from '../../../../services/utils.service';
import { CustomData } from '../../px-hidden/px-hidden.component';
import RetrieverService from 'src/app/services/retriever.service';

@Component({
  selector: 'box-indirizzo',
  templateUrl: './box-indirizzo.component.html',
  styleUrls: ['./box-indirizzo.component.scss'],
})
export class BoxIndirizzoComponent {
  private _data: CustomData | undefined;
  private _field: IField | undefined;
  private _groups: IGroup[] | undefined;

  private _dataInputField: IDataInputField | undefined;

  iconBox: IField | undefined;
  customAttributes!: { address: string; cardTitle: string };
  address = '';

  constructor(
    private utilsService: UtilsService,
    private retrieverService: RetrieverService
  ) {}

  @Input() set data(input: CustomData | undefined) {
    this._data = input;
    this._field = input?.field;
    this._groups = input?.groups;
    this.customAttributes = this._field?.customAttributes;
    this.address = this.utilsService.htmlDecode(this.customAttributes?.address);

    if (this._groups) {
      const firstIcon = this.retrieverService.getFirstFieldInGroupsByType(this._groups, "pxIcon", true);
      if (firstIcon) this.iconBox = firstIcon;

    }
  }

  get data() {
    return this._data;
  }
}
