<div class="card-box box-indirizzo-width" *ngIf="customAttributes">
  <div class="layout-box-indirizzo">
    <div class="layout-text-address">
      <div *ngIf="customAttributes.cardTitle" class="Text-responsive-medium">
        {{ customAttributes.cardTitle }}
      </div>
      <div *ngIf="address" class="address-layout">
        <div class="Text-responsive-medium">Indirizzo:</div>
        <div class="Text-responsive">{{ address }}</div>
      </div>
    </div>
    <tpd-px-icon *ngIf="iconBox" [data]="{field: iconBox}"></tpd-px-icon>
  </div>
</div>
