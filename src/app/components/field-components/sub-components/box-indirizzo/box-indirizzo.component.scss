@import "../../../../../styles.scss";

.address-layout {
  display: flex;
  align-items: flex-start;
  flex-direction: row;
  gap: 5px;
}

.layout-box-indirizzo {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@media #{$bkp_mobile} {
  .layout-text-address {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }
}
@media #{$bkp_tablet} {
  .layout-text-address {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }
}
@media #{$bkp_desktop} {
  .layout-text-address {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }
}
.card-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
