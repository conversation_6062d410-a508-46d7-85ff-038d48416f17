import { Component, Input } from "@angular/core";
import { CustomData } from "../../px-hidden/px-hidden.component";
import RetrieverService from "../../../../services/retriever.service";

@Component({
  templateUrl: './multi-stepper.component.html',
  styleUrls: ['./multi-stepper.component.scss'],
  selector: 'multi-stepper'
})
export default class MultiStepperComponent{
  public stepAttuale = 0;
  public numeroDiStep = 1;

  public loopArray: number[];

  public constructor(
    private _retrieverService: RetrieverService
  ) {
  }

  @Input() set data(data: CustomData) {
    if(data.groups){
      this.stepAttuale = this._retrieverService.getCustomAttributeFromFieldAsNumber(data.field, 'actualStep', 0);
      this.numeroDiStep = this._retrieverService.getCustomAttributeFromFieldAsNumber(data.field, 'numberOfSteps', 1);
      this.loopArray = new Array(this.numeroDiStep).fill(0);
    }
  }
}
