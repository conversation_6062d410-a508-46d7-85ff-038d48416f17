<div [class]="'CarouselCardContainer' + ' ' + this.cardBackgroundClass">
  <span class="CarouselCardHead">
    <div
      class="CarouselCardHeadIcon"
      [style]="'background-image: url(' + this.iconContent + ')'">
    </div>
    <custom-text-style
      *ngIf="this.cardTitle"
      [content]="this.cardTitle"
      [textCssString]="'TEXT APP BD0 WEB WHBU16 WHBU16 WHBU16'">
    </custom-text-style>
  </span>
  <div class="CarouselCardBody">
    <custom-text-style
      *ngIf="this.bodyTitle"
      [content]="this.bodyTitle"
      [textCssString]="'TEXT APP BD0 WEB WHB16 WHB16 WHB16'">
    </custom-text-style>
    <div
      class="CarouselCardBodyContent">
      <custom-text-style
        *ngIf="this.bodyContent"
        [content]="this.bodyContent"
        [textCssString]="'TEXT APP BD0 WEB WHL16 WHL16 WHL16'">
      </custom-text-style>
    </div>
  </div>
  <span
    class="CarouselCardButton">
    <tpd-button *ngIf="this.button" [data]="{field: this.button}"></tpd-button>
  </span>
</div>
