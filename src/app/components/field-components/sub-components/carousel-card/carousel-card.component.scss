@import "../../../../../styles.scss";

.CarouselCardContainer {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  width: 340px;
  height: 350px;
  max-height: 350px;
  gap: 16px;
  padding: 24px;
  margin-right: 8px;

  &.bg-Vei<PERSON>li{
    @extend .bg-Auto;
  }

  @media #{$bkp_mobile_only} {
    width: calc(95vw - 68px);
  }

  >.CarouselCardHead {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;

    >.CarouselCardHeadIcon {
      aspect-ratio: 1 / 1;
      display: block;
      width: 40px;
      background-size: cover;
      filter: brightness(0) invert(1);
    }
  }

  >.CarouselCardBody {
    display: flex;
    flex-direction: column;

    >.CarouselCardBodyContent {
      overflow-y: auto;
      max-height: 130px;
      flex-shrink: 1;
    }
    margin-bottom: 8px;
  }

  >.CarouselCardButton {
    margin-top: auto;
  }
}
