import { Component, Input } from "@angular/core";
import { CustomData } from "../../px-hidden/px-hidden.component";
import { UtilsService } from "../../../../services/utils.service";
import { GetBeneIcon } from "@tpd-web-angular-libs/angular-library";
import RetrieverService from "../../../../services/retriever.service";
import { IField } from "../../../../models/models.commons";

@Component({
  templateUrl: './carousel-card.component.html',
  styleUrls: ['./carousel-card.component.scss'],
  selector: 'carousel-card'
})
export default class CarouselCardComponent{
  public cardBackgroundClass: string;
  public cardTitle: string;
  public bodyTitle: string;
  public bodyContent: string;
  public iconContent: string;
  public button: IField;

  public constructor(
    private _utilsService: UtilsService,
    private _retrieverService: RetrieverService
  ) {
  }

  @Input() set data(input: CustomData) {
    if (input && input.field) {
      const cardShortDescription: string = input?.field?.customAttributes?.cardShortDescription || ' | | ';
      const splittedValues = cardShortDescription.split('|').map(val => this._utilsService.htmlDecode(val.trim()));
      if(splittedValues.length === 3){
        this.cardTitle = splittedValues[0];
        this.bodyTitle = splittedValues[1];
        this.bodyContent = splittedValues[2];
      }

      const cardName = input?.field?.customAttributes?.cardName || '';

      this.cardBackgroundClass = `bg-${cardName || ''}`;
      const beneCode = {
        'Casa': 'PUCASA',
        'Veicoli': 'PUAUTO',
        'Infortuni': 'PUINF',
        'Salute': 'PUSAL',
        'Viaggi': 'PUVIAGGIA',
        'Viaggio': 'PUVIAGGIA',
        'Famiglia': 'PUFAM',
        'Pet': 'PUPET',
        'Mobilità': 'PUMOBILITA',
        'Mobilita': 'PUMOBILITA',
      }[cardName || ''];
      this.iconContent = GetBeneIcon(beneCode);

      this.button = this._retrieverService.getFirstFieldInGroupsByType(input.groups, 'pxButton', true);
      if(this.button?.customAttributes?.linkType === 'GO_TO_NEW_QUOTATION'){
        delete this.button.customAttributes['linkType'];
        this.button.customAttributes['GO_TO_QUOTATION'] = cardName || '';
      }
    }
  }
}
