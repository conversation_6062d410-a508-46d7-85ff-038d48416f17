<div class="BoxPrivacyContainer">
  <field
    *ngIf="this.checkboxElement"
    [data]="{field: checkboxElement}"></field>
  <div class="BoxPrivacyContent">
    <caption-elem *ngIf="this.captionTitle" [caption]="this.captionTitle"></caption-elem>
    <div
      class="BoxPrivacyDescription"
      [ngClass]="{'open': this.isOpen}">
      <paragraph *ngIf="this.paragraphDescription" [paragraph]="this.paragraphDescription"></paragraph>
    </div>
  </div>
  <span
    class="PlusIcon"
    (click)="this.handleXClick()"
    [ngClass]="{'xVersion': this.isOpen}"></span>
</div>
