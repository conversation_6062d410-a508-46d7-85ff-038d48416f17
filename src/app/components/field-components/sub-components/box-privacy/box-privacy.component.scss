@import "../../../../../styles.scss";

.BoxPrivacyContainer {
  display: flex;
  flex-direction: row;
  width: 100%;

  border: 1px solid $border-card-disabled;
  padding: 16px;
  gap: 16px;

  > .PlusIcon {
    display: block;
    width: 24px;
    height: 24px;
    flex-shrink: 0;
    position: relative;
    cursor: pointer;

    @media #{$bkp_mobile} {
      display: block;
    }

    @media #{$bkp_tablet} {
      display: block;
    }

    @media #{$bkp_desktop} {
      display: none;
    }

    &.xVersion {
      transform: rotateZ(45deg);
    }

    &:before,
    &:after {
      content: "";
      background-color: $blue-primary;
      width: 2px;
      height: 100%;
      border-radius: 10px;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translateX(-50%) translateY(-50%);
    }

    &:after {
      transform: translateX(-50%) translateY(-50%) rotateZ(90deg);
    }
  }

  > .BoxPrivacyContent {
    display: flex;
    flex-direction: column;
    width: 100%;

    > .BoxPrivacyDescription {
      @extend .SmallScrollBar;

      $desktopMaxHeight: 144px;
      $tabletMaxHeight: 50px;
      padding-right: 10px;

      @media #{$bkp_mobile} {
        overflow-y: hidden;
        max-height: $tabletMaxHeight;

        &.open {
          max-height: unset;
        }
      }

      @media #{$bkp_tablet} {
        overflow-y: hidden;
        max-height: $tabletMaxHeight;

        &.open {
          max-height: unset;
        }
      }

      @media #{$bkp_desktop} {
        overflow-y: auto;
        max-height: $desktopMaxHeight;
      }
    }
  }
}
