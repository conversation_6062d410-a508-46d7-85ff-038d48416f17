import { Component, Input } from "@angular/core";
import { <PERSON><PERSON><PERSON><PERSON>, IField, IGroup, IParagraph } from "../../../../models/models.commons";
import RetrieverService from "../../../../services/retriever.service";

@Component({
  selector: 'box-privacy',
  templateUrl: './box-privacy.component.html',
  styleUrls: ['./box-privacy.component.scss']
})
export default class BoxPrivacyComponent{
  public captionTitle: ICaption;
  public paragraphDescription: IParagraph;
  public checkboxElement: IField;

  public isOpen = false;
  constructor(private _retrieverService: RetrieverService) {
  }

  @Input() set data(data: {groups: IGroup[], field: IField}){
    if(data){
      this.captionTitle = this._retrieverService.getFirstCaptionInGroups(data.groups);
      this.paragraphDescription = this._retrieverService.getFirstParagraphInGroups(data.groups);
      this.checkboxElement = this._retrieverService.getFirstFieldInGroupsByType(data.groups,'pxCheckbox');

      if(this.checkboxElement)
        this.checkboxElement.label = '';
    }
  }

  public handleXClick(){
    this.isOpen = !this.isOpen;
  }
}
