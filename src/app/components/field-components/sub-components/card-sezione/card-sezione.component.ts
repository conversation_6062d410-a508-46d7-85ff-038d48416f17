import { Component, Input } from '@angular/core';
import { CustomData } from '../../px-hidden/px-hidden.component';
import { IGroup, ILayout } from "../../../../models/models.commons";
import RetrieverService from "../../../../services/retriever.service";
import { TextCss, TextHandlerService } from "../../../../services/text-handler.service";

@Component({
  selector: 'card-sezione',
  templateUrl: './card-sezione.component.html',
  styleUrls: ['./card-sezione.component.scss'],
})
export default class CardSezioneComponent {
  public deselectedTextStyle: TextCss;
  public deselectedText = '';
  public selected = false;
  public renderGroups: IGroup[];

  constructor(
    private _retrieverService: RetrieverService,
    private _textHandlerService: TextHandlerService
  ) {
    this.deselectedTextStyle = this._textHandlerService.getTextCss('TEXT APP BDB16 WEB BLB18C BLB18C BLB18C');
  }

  @Input() public set data(data: CustomData) {
    if (data) {
      this.deselectedText = data?.field?.customAttributes?.deselectedText || '';
      this.selected = data?.field?.customAttributes?.selected === 'true';
      if(data.groups){
        this.renderGroups = data.groups.filter(group => group?.field?.control?.type !== 'pxHidden');
      }
    }
  }
}
