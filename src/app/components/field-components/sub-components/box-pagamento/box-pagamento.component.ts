import { Component, Input } from "@angular/core";
import { CustomData } from "../../px-hidden/px-hidden.component";
import RetrieverService from "../../../../services/retriever.service";
import { IView } from "src/app/models/models.commons";

@Component({
  templateUrl: './box-pagamento.component.html',
  selector: 'box-pagamento'
})
export default class BoxPagamentoComponent {
  public mainView: IView;
  private _padding: string;
  private _presenzaInstallatore: string;
  private _productType: string;

  constructor(
    private _retrieverService: RetrieverService
  ) {
  }

  public mappaBackground(): string{
    let esito = "";

    if(this._productType){
      const capitalize = this._productType.substring(0, 1).toUpperCase() + this._productType.toLowerCase().substring(1).replace('à', 'a');
      esito = `bg-${capitalize}`;
    }

    return esito;
  }

  public mappaPadding(): string{
    let esito = "0";

    if(this._padding){
      esito = this._padding.split(' ').map(paddingNumber => `${paddingNumber}px`).reverse().join(' ');
    }

    return esito;
  }

  @Input() set data(data: CustomData){
    this.mainView = this._retrieverService.getFirstViewInGroupsByViewId(data.groups, 'MostraAmbitoPagamento');
    this._padding = this._retrieverService.getCustomAttributeFromField(data.field, 'padding');
    this._presenzaInstallatore = this._retrieverService.getCustomAttributeFromField(data.field, 'presenzaInstallatore');
    this._productType = this._retrieverService.getCustomAttributeFromField(data.field, 'productType');
  }
}
