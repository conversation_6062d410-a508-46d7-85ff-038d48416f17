import { Component, Input } from '@angular/core';
import { IField } from '../../../../models/models.commons';
import { Helpers } from '@tpd-web-common-libs/nodejs-library';
import ColorsService from "../../../../services/colors.service";

interface SeparatorLineComponentCustomAttributes {
  size: string;                   //Indica la dimensione in altezza della linea separatrice
  RenderPlatform: string;         //Indica la piattaforma di rendering del componente
  maxWidth: string;               //Indica la larghezza massima del componente
  maxWidthDesktop: string;        //Indica la larghezza massima desktop
  separatorColor: string;         //Colore del separatore
}

@Component({
  selector: 'tpd-line',
  templateUrl: './line.component.html'
})
export class SeparatorLineComponent {
  private _field: IField | undefined;
  public size = '1px';
  public renderPlatform = 'Web';
  public lineRenderVisible = true;
  public maxWidth = '100%';

  public renderColor = '#ccc';

  constructor(private _colorsService: ColorsService) {
    //DO-NOTHING
  }

  @Input() set field(input: IField | undefined) {
    this._field = input;
    if (this.field) {
      const customAttributes: SeparatorLineComponentCustomAttributes =
        this.field.customAttributes;
      if (customAttributes) {
        this.size = customAttributes.size ? `${customAttributes.size}px` : '1px';
        this.renderPlatform = customAttributes.RenderPlatform ?? 'Web';
        this.lineRenderVisible = this.renderPlatform.toLowerCase().includes('web');
        if (Helpers.EnvironmentHelper.isClientSide() && window.innerWidth > 510)
          this.maxWidth = customAttributes.maxWidthDesktop ? `${customAttributes.maxWidthDesktop}px` : '100%';
        else this.maxWidth = customAttributes.maxWidth ? `${customAttributes.maxWidth}px` : '100%';

        if(customAttributes.separatorColor)
          this.renderColor = this._colorsService.getColor(customAttributes.separatorColor);
      }
    }
  }

  get field() {
    return this._field;
  }
}
