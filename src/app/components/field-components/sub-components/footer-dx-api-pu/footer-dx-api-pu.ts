import { Component, Input, OnDestroy } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Helpers } from '@tpd-web-common-libs/nodejs-library';
import { Subscription } from 'rxjs';
import { IField, IGroup, ILayout } from '../../../../models/models.commons';
import { StatusService } from '../../../../services/status.service';
import { CustomData } from '../../px-hidden/px-hidden.component';

@Component({
  selector: 'footer-dx-api-pu',
  templateUrl: './footer-dx-api-pu.html',
  styleUrls: ['./footer-dx-api-pu.scss'],
})
export class FooterDxAPIPUComponent implements OnDestroy {
  private field: IField | undefined;
  private _data: CustomData | undefined;
  public groups: IGroup[] = [];
  public footerLayout: ILayout | undefined;
  public footerPositiveButton: IField | undefined;
  public footerSecondaryButton: IField | undefined;
  public secondaryButtonVisible = false;
  public footerIcon: IGroup[] = [];
  public footerOpened: IGroup[] = [];
  public footerButton: IGroup[] = [];
  public groupsFooter: IGroup[] = []; // gruppi esterni a FooterIcon / FooterButton/ FooterOpened
  public fg: FormGroup | undefined;
  public subscription: Subscription | undefined;
  public accordionIsOpen = false;

  //Aggiornamento del placeholder per il footer
  public showPlaceolder = false;
  private _footerPlaceholderUpdateLoop = false;
  //-------------------------------------------

  constructor(private statusService: StatusService) {}

  @Input() set data(input: CustomData | undefined) {
    if (input) {
      if(Helpers.EnvironmentHelper.isClientSide()){
        if(window.location.href.includes('localhost:7008'))
          this.showPlaceolder = true;
      }

      this._data = input;
      this.field = input.field;
      this.groups = input.groups ?? [];

      for (const group of this.groups) this.filterGroups(group);

      if(this.showPlaceolder) {
        this._updateFooterPlaceHolder();
      }
    }
  }

  get data() {
    return this._data;
  }

  /**
   * Avvia il loop di aggiornamento del placeholder del footer
   * @private
   */
  private _updateFooterPlaceHolder(): void {
    const upldateFunction = () => {
      if (typeof document === 'object') {
        const placeHolderList = document.getElementsByClassName(
          'footer-pu-placehoder'
        );

        const containerList = document.getElementsByClassName(
          'footer-pu-container'
        );

        if (placeHolderList.length > 0 && containerList.length > 0) {
          const placeHolder = placeHolderList[0] as HTMLDivElement;
          const container = containerList[0] as HTMLDivElement;

          const height = container.getBoundingClientRect().height;
          placeHolder.style.minHeight = `${height}px`;
          placeHolder.style.maxHeight = `${height}px`;
        }

        if (this._footerPlaceholderUpdateLoop)
          requestAnimationFrame(upldateFunction);
      }
    };
    if (!this._footerPlaceholderUpdateLoop) {
      this._footerPlaceholderUpdateLoop = true;
      upldateFunction();
    }
  }

  filterGroups(group: IGroup) {
    const field = group.field;
    const layout = group.layout;

    if (layout && layout.visible && layout?.groupFormat === 'PriceContainer') {
      this.footerLayout = layout;
    } else if (field && field.visible) {
      if (field.customAttributes.componentID == 'footerPrimaryCTA')
        this.footerPositiveButton = field;
      else if (field.customAttributes.componentID == 'footerSecondaryCTA') {
        this.secondaryButtonVisible = true;
        this.footerSecondaryButton = field;
      }
    }
  }

  ngOnDestroy() {
    this._footerPlaceholderUpdateLoop = false;
    this.subscription && this.subscription.unsubscribe();
  }
}
