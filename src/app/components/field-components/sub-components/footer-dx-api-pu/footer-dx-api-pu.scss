@import "../../../../../styles.scss";

.footer-pu-container {
  position: fixed;
  bottom: 0;

  width: 100%;
  z-index: 998;
  border-bottom: 2px solid white;
  background-color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-top: 2px solid $border-card-disabled;
  padding: 30px 0;
}

.separator {
  border-left: 1px solid $blue-primary;
  opacity: 0.5;
  height: 50px;
}
.d-flex {
  column-gap: 16px;
  @media #{$bkp_tablet} {
    column-gap: 40px;
  }
}

.visible-desktop {
  display: none;
  @media #{$bkp_desktop} {
    display: block;
  }
}
.visible-tablet {
  display: none;
  @media #{$bkp_tablet} {
    display: block;
  }
}
.visible-mobile {
  @media #{$bkp_tablet} {
    display: none;
  }
}
