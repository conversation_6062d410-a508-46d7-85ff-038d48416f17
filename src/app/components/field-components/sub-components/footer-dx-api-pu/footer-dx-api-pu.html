<div class="footer-pu-container">
  <div class="d-flex align-center">
    <groups
      *ngIf="this.footerLayout && this.footerLayout.groups"
      [groups]="footerLayout.groups"
    ></groups>
    <div
      *ngIf="this.footerLayout && this.footerLayout.groups"
      class="separator visible-tablet"
    ></div>
    <field
      *ngIf="secondaryButtonVisible"
      [data]="{field: footerSecondaryButton, groups: groups}"
      class="visible-tablet"
    ></field>
    <field [data]="{field: footerPositiveButton, groups: groups}"></field>
  </div>
  <field
    [data]="{field: footerSecondaryButton, groups: groups}"
    class="visible-mobile"
  ></field>
</div>
<div *ngIf="this.showPlaceolder" class="footer-pu-placehoder"></div>
