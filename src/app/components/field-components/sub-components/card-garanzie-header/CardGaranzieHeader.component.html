<div class="CardGaranzieHeaderContainer">
  <input #controlloreMobile class="visibleMobile" type="hidden"/>
  <field *ngIf="this.isObbligatoria && !this.isMobile" [data]="{field: this.lockIcon}"></field>
  <field *ngIf="!this.isObbligatoria && !this.isMobile" [data]="{field: this.checkbox}"></field>
  <span class="NomeGaranziaLabel">{{this.nomeGaranzia}}</span>
  <field [data]="{field: this.infoIcon}"></field>
  <div class="RightSideContainer">
    <field *ngIf="!this.isObbligatoria && this.isMobile" [data]="{field: this.checkbox}"></field>
    <field *ngIf="this.isObbligatoria && this.isMobile" [data]="{field: this.lockIcon}"></field>
    <span *ngIf="this.isObbligatoria" class="ObbligatoriaLabel">{{this.labelObbligatoria}}</span>
  </div>
</div>
