@import "../../../../../styles.scss";

.CardGaranzieHeaderContainer {
  display: flex;
  flex-direction: row;
  gap: 8px;

  >.NomeGaranziaLabel{
    color: $blue-primary;
    font-family: 'Unipol Medium';
    font-size: 16px;
  }

  >.RightSideContainer{
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    margin-left: auto;
    gap: 8px;

    >.ObbligatoriaLabel{
      color: $grey;
      font-family: 'Unipol Medium';
      font-size: 16px;
    }
  }

  .visibleMobile {
    --statusMobile: 'false';
    @media #{$bkp_mobile} {
      --statusMobile: 'true';
    }

    @media #{$bkp_tablet} {
      --statusMobile: 'false';
    }

    @media #{$bkp_desktop} {
      --statusMobile: 'false';
    }
  }
}
