import { Component, ElementRef, Input, ViewChild } from '@angular/core';
import { CustomData } from '../../px-hidden/px-hidden.component';
import { IField } from '../../../../models/models.commons';
import RetrieverService from "../../../../services/retriever.service";
import { Helpers } from '@tpd-web-common-libs/nodejs-library';

@Component({
  templateUrl: './CardGaranzieHeader.component.html',
  styleUrls: ['./CardGaranzieHeader.component.scss'],
  selector: 'card-garanzie-header',
})
export default class CardGaranzieHeaderComponent {
  private _data: CustomData | undefined;

  public checkbox: IField;
  public nomeGaranzia: string;
  public infoIcon: IField;
  public labelObbligatoria: string;
  public lockIcon: IField;

  @ViewChild('controlloreMobile') controlloreMobile: ElementRef;

  constructor(
    private _retrieverService: RetrieverService
  ) {
  }

  @Input() public set data(data: CustomData | undefined) {
    this._data = data;

    this.checkbox = this._retrieverService.getFirstFieldInGroupsByType(data.groups, 'pxCheckbox');
    this.nomeGaranzia = this._retrieverService.captionToValue(this._retrieverService.getFirstCaptionByControlFormat(data.groups, 'NomeGaranzia')).value;
    this.infoIcon = this._retrieverService.getFirstIconInGroupsByResource(data.groups, 'info');
    this.labelObbligatoria = this._retrieverService.captionToValue(this._retrieverService.getFirstCaptionByControlFormat(data.groups, 'TagObbligatoria')).value;
    this.lockIcon = this._retrieverService.getFirstIconInGroupsByResource(data.groups, 'lock');

    if(this.infoIcon) {
      this.infoIcon.customAttributes.resource = 'lightInfo';
      this.infoIcon.customAttributes.webResponsiveSize = '24L 24L 24L';
    }

    if(this.checkbox)
      this.checkbox.label = "";
  }

  public get isObbligatoria(): boolean{
    return this.lockIcon?.visible;
  }

  public get isMobile(): boolean{
    let esito = false;
    if(Helpers.EnvironmentHelper.isClientSide() && this.controlloreMobile?.nativeElement){
      const statusMobile = window.getComputedStyle(this.controlloreMobile.nativeElement).getPropertyValue('--statusMobile');
      esito = statusMobile === '"true"';
    }

    return esito;
  }
}
