<div *ngIf="field" class="select layout-input">
  <ng-container *ngIf="this.field.labelReserveSpace || this.label">
    <div
      class="{{ labelFormat }}  mrg-label-input"
      *ngIf="!this.labelCss">
      {{ label }}
    </div>
    <custom-text-style
      *ngIf="this.labelCss"
      [textCss]="this.labelCss"
      [content]="this.label">
    </custom-text-style>
  </ng-container>
  <div class="tpd_selectAngular" [ngClass]="{ isDisabled: disabled }">
    <input
      type="text"
      class="select-styled"
      (keydown)="this.resetValueOnDisabledReadonly()"
      (keyup)="this.filterElements($event)"
      (click)="this.changeVisibility($event)"
      [ngClass]="{
        active: opened,
        'disabled-input-box isDisabled': this.disabled,
        'read-only-mode': this.readonly,
        'invalid-input': this.touched && this.keyControl.invalid
      }"
      [placeholder]="placeholder"
      [(ngModel)]="this.visualizedValue"
    />
    <ul class="select-options" [hidden]="!opened">
      <li
        *ngFor="let hOpt of highlightOptions; index as i"
        [ngClass]="{'select-highlight-options': i === highlightOptions.length - 1}"
        [hidden]="highlightOptions.length === 0"
        (click)="onClick(hOpt)">
        {{ hOpt.value | capitalize }}
        <span
          *ngIf="hOpt.tooltip"
          class="image-zone"
          [style.background-image]="'url(' + hOpt.tooltip + ')'">
        </span>
      </li>
      <li *ngFor="let opt of options" (click)="onClick(opt)">
        {{ opt.value | capitalize }}
        <span
          *ngIf="opt.tooltip"
          class="image-zone"
          [style.background-image]="'url(NextAssets/interprete-pu/immagini-pet/' + opt.tooltip + '.png)'">
        </span>
      </li>
    </ul>
  </div>

  <div
    [hidden]="!showError"
    *ngIf="this.touched && this.keyControl.invalid && !this.disabled && !this.readonly && this.errorMessage()">
    <i class="icon-Attenzione-pieno bd-icona"></i>
    <span class="testo-non-valido">{{ errorMessage() }}</span>
  </div>
</div>
