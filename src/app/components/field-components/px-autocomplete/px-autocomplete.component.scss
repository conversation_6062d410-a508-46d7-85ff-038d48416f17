@import "../../../../styles.scss";
.label-input-form {
  font-family: "Unipol Medium";
  font-size: 16px;
  font-weight: 500;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.25;
  letter-spacing: normal;
  padding-bottom: 19px;
  color: #0f3250;
}

.read-only-mode {
  pointer-events: none;
  caret-color: transparent;
  cursor: default;
  outline: none;
}

.wrapper-select {
  height: 50px;
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;

  .child {
    border-bottom: none;
    border-left: none;
    border-right: none;
    padding: 6px 12px;
    display: flex;
    align-items: center;
  }
}

.container-input-form-dati-utente {
  margin: 0 !important;
}

.selected-element {
  border-top: none;
}

.first-child {
  border-top: 2px solid;
}

.select {
  @include selectBoxUnipol(".tpd_selectAngular");
  margin-bottom: 0 !important;

  .select-styled {
    color: $middle-grey-pu !important;
    position: absolute;
    background-color: #fff;
    padding: 6px 12px !important;
    border: 1px solid #0f3250 !important;
    text-align: left;
    word-break: break-word;
    overflow: hidden;
    align-items: center;
    height: 48px;
    display: flex;

    &.isDisabled {
      pointer-events: none;
      color: $grey !important;
      //border-color: $grey !important;
      border: 0 !important;
    }

    &.active {
      &:after {
        transform: rotate(-45deg) !important;
        top: 19px !important;
      }
    }

    &:after {
      border: none !important;
      transform: rotate(135deg) !important;
      width: 11px !important;
      cursor: pointer;
      display: inline-block;
      height: 11px !important;
      border-style: solid !important;
      border-width: 2px 2px 0 0 !important;
      position: absolute;
      top: 17px !important;
      right: 17px !important;
    }
  }

  .tpd_selectAngular {
    display: flex;
    font-family: "Unipol";
    font-size: 16px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.25;
    letter-spacing: normal;
    color: #fff;
    width: 100%;
    height: 48px;

    &.isDisabled {
      cursor: default !important;
    }

    .select-options {
      border: 1px solid #0f3250 !important;
      border-top: none !important;
      top: 96% !important;
      li {
        //color: #0f3250 !important;
        color: $middle-grey-pu !important;
        margin: 0;
        padding: 12px 16px !important;
        //border-top: 1px solid #0f3250 !important;
        border-bottom: none !important;
        border-left: none !important;
        border-right: none !important;
        height: auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        z-index: 50;
        gap: 10px;

        .image-zone {
          display: block;
          width: 100px;
          height: 60px;

          background-repeat: no-repeat;
          background-size: cover;
          background-position: center;
          background-color: lightgray;

          flex-shrink: 0;
        }
      }
      li.select-highlight-options {
        border-bottom: 1px solid #0f3250 !important;
      }
    }

    @media #{$bkp_mobile} {
      max-width: none;
    }

    @media #{$bkp_tablet} {
      max-width: 500px;
    }

    @media #{$bkp_desktop} {
      max-width: 550px;
    }
    .invalid-input {
      border: solid 1px $alert-color !important;
    }
  }
}
