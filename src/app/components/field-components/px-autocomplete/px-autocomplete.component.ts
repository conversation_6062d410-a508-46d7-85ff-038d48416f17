import { Component, Input, OnDestroy } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import {
  TextCss,
  TextHandlerService,
} from '../../../services/text-handler.service';
import { errors } from '../../../utils/errorMessage';
import {
  IActionSet,
  IControl,
  IField,
  IOption,
} from '../../../models/models.commons';
import { AnalyticsInterprete } from '../../../services/analytics.interprete.service';
import { MessagesService } from '../../../services/messages.service';
import { ListenerErroreFormInterface, StatusService } from "../../../services/status.service";
import { UtilsService } from '../../../services/utils.service';
import { EventsManaged } from '../../../utils/events.enum';
import { EventsMethod } from '../../../utils/eventsMethod';

interface PxAutocompleteCustomAttributes {
  highlights?: string;
}

@Component({
  selector: 'tpd-px-autocomplete',
  templateUrl: './px-autocomplete.component.html',
  styleUrls: ['./px-autocomplete.component.scss'],
})
export class PxAutocompleteComponent extends EventsMethod implements OnDestroy {
  constructor(
    private utilsService: UtilsService,
    public override messageService: MessagesService,
    public analyticsService: AnalyticsInterprete,
    private statusService: StatusService,
    private textHandlerService: TextHandlerService
  ) {
    super(messageService, analyticsService);
    this._standAloneFormIndex = this.statusService.currentStandAloneFormIndex;
  }

  private _standAloneFormIndex = -1;
  private _errorListener: ListenerErroreFormInterface;

  public label = '';
  public opened = false;
  public touched = false;
  public disabled = false;
  public readonly = false;

  public placeholder = '';
  public labelFormat = '';
  public labelCss: TextCss;

  public maxLength: number;
  public options: IOption[] = [];
  public highlightOptions: IOption[] = [];

  public showError = false;

  public visualizedValue: string;
  public keyControl = new FormControl('', null);


  public control: IControl;
  public override actionsSet: IActionSet[] = [];
  public customAttributes: PxAutocompleteCustomAttributes;

  public override eventsManaged = EventsManaged;

  public ngOnInit(): void {
    this._errorListener = this.statusService.registraListenerDiErroreInputInvalido(this._standAloneFormIndex, statoErrore => this.showError = statoErrore);
  }

  @Input() set data(input: { field: IField }) {
    this.field = input.field;
    this.control = this.field.control;
    this.customAttributes = this.field?.customAttributes;
    this.disabled = this.field.disabled;
    this.maxLength = this.field.maxLength;
    this.keyControl = new FormControl();



    this.label = this.utilsService.htmlDecode(this.field.label ?? '');
    if (this.field.labelFormat) {
      this.labelFormat = this.utilsService.getClassFromFormat(this.field.labelFormat);
      this.labelCss = this.textHandlerService.getTextCss(this.field.labelFormat);
    }

    this._detectOptionsInversion();
    this._setDefaultValue();

    if (this.control?.modes) {
      this.placeholder = this.utilsService.htmlDecode(this.control.modes[0].placeholder ?? '');
      this.options = this.field.control.modes[0].options;
    }

    if (this.customAttributes?.highlights && this.options) {
      const keys: string[] = this.customAttributes.highlights.split('|');
      if (this.field.control.modes[0].options) {
        this.options = this.field.control.modes[0].options.filter((option) => !keys.includes(option.key));
        this.highlightOptions = this.field.control.modes[0].options.filter((option) => keys.includes(option.key));
      }
    }

    if (this.field.required) {
      // eslint-disable-next-line @typescript-eslint/unbound-method
      this.keyControl.setValidators([Validators.required]);
      this.showError = false;
    }

    this.readonly = this.field.readOnly || false;
    this.actionsSet = this.control?.actionSets || [];
    this.field.reference && this.statusService.addControlFormGroup(this.field.reference, this.keyControl);

    this.setValueProcessManagement();
    this.settaValoriGestioneProcesso();
  }

  private _detectOptionsInversion() {
    if (this.control?.modes[0]?.options) {
      let areInverted = false;
      const firstOption = this.field.control.modes[0].options[0];
      if(firstOption){
        for(let i = 0; i < firstOption.key.length && !areInverted; i++){
          if(isNaN(parseFloat(firstOption.key.charAt(i)))){
            areInverted = true;
            break;
          }
        }
      }

      if (areInverted) {
        this.field.control.modes[0].options =
          this.field.control.modes[0].options.map((option) => ({
            key: option.value,
            value: option.key,
            tooltip: option.tooltip,
          }));
      }
    }
  }

  private _setDefaultValue() {
    if (this._isInKey(this.field.value ?? '')) {
      this.visualizedValue = this.utilsService.htmlDecode(this.getValueFromKey(this.field.value));
      this.keyControl.setValue(this.field.value);
    } else if (this._isInValue(this.field.value ?? '')) {
      this.visualizedValue = this.utilsService.htmlDecode(this.field.value ?? '');
      this.keyControl.setValue(this.getKeyFromValue(this.visualizedValue));
    } else {
      this.visualizedValue = '';
      this.keyControl.setValue('');
    }
  }

  private _getOptionFromKey(key: string): IOption {
    let esito: IOption = { key: '', value: '', tooltip: '' };

    if (this.control.modes && this.control.modes[0].options) {
      for (const option of this.control.modes[0].options) {
        if (option.key === key) {
          esito = option;
          break;
        }
      }
    }

    return esito;
  }

  private _isInKey(value: string): boolean {
    return this._getOptionFromKey(value).key === value;
  }

  private _isInValue(value: string): boolean {
    let esito;

    if (this.control.modes && this.control.modes[0].options) {
      for (const option of this.control.modes[0].options) {
        if (option.value === value) {
          esito = true;
          break;
        }
      }
    }

    return esito;
  }

  public changeVisibility(event: MouseEvent) {
    event.preventDefault();
    this.touched = true;
    this.opened = !this.opened;
    if (this.options.length > 0 && this._isInKey(this.keyControl.value))
      this.visualizedValue = this.getValueFromKey(this.keyControl.value);
  }

  public getKeyFromValue(value: string): string {
    let esito = '';

    if (this.control.modes && this.control.modes[0].options) {
      for (const option of this.control.modes[0].options) {
        if (option.value === value) {
          esito = option.key;
          break;
        }
      }
    }

    return esito;
  }

  public getValueFromKey(key: string): string {
    return this.utilsService.htmlDecode(
      this._getOptionFromKey(key).value ?? ''
    );
  }

  override onClick(opt: IOption) {
    if (opt.value !== this.visualizedValue) {
      this.visualizedValue = opt.value;
      this.keyControl.setValue(opt.key);
      this.onChange();
    }

    this.opened = false;
    this.messageService.onEventExecuted(
      this.actionsSet,
      EventsManaged.click,
      this.processManagement,
      this.gestioneProcesso
    );
  }

  public resetValueOnDisabledReadonly() {
    if (this.readonly || this.disabled) this._setDefaultValue();
  }

  public filterElements(ev?: KeyboardEvent) {
    if (!(this.readonly || this.disabled)) {
      if (ev) {
        this.opened = true;
        this.touched = true;
      }
      let input = this.visualizedValue;
      if (ev) {
        ev.preventDefault();
        input = (ev.target as HTMLInputElement).value;
      }
      if (this.control?.modes) {
        if (input) {
          this.options = this.control.modes[0].options.filter((option) =>
            option.value.toLowerCase().match(input.toLowerCase())
          );
          if (this.options.length === 0) this.keyControl.setValue(input);
        } else if (this.control?.modes)
          this.options = this.control.modes[0].options;
      }
    } else this._setDefaultValue();
  }

  public errorMessage(): string {
    let message = '';
    if (this.keyControl.hasError('required'))
      message = errors['required'];
    return message;
  }

  public ngOnDestroy(): void {
    this._errorListener && this._errorListener.rimuoviListenerDiErroreInputInvalido();
    this.statusService.removeControlFormGroup(this.field?.reference ?? '');
  }
}
