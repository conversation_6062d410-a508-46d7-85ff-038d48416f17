@import "../../../../styles.scss";

.ReadOnlyContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
}

.PxIntegerContainer{
  display: flex;
  flex-direction: column;
  gap: 4px;

  >.InputFieldContainer{
    position: relative;

    >.InputField{
      width: 100%;
      height: 48px;
      padding: 12px 16px;
      border: 1px solid $color-darker;
      color: $middle-grey-pu;

      -moz-appearance: textfield;
      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      &.errore{
        border-color: $alert-color;
      }
    }

    >.Percentage{
      font-family: unset;
      font-size: unset;
      color: $middle-grey-pu;

      position: absolute;
      bottom: calc(13px);
    }
  }
}
