<ng-container *ngIf="this.visible">
  <div
    *ngIf="!this.readOnly; else readOnlyView"
    class="PxIntegerContainer">
    <custom-text-style
      [textCss]="this.customFormat"
      [content]="this.label">
    </custom-text-style>
    <div class="InputFieldContainer">
      <input
        class="InputField"
        type="number"
        [ngClass]="{
          errore: this.showError && this.campoInErrore
        }"
        [min]="this.minValue"
        [max]="this.maxValue"
        [formControl]="this.fieldControl"
        [value]="this.value"
        (blur)="this.onBlur($event)"
        (change)="this.onChange($event)"
        (focus)="this.onFocus($event)"
        (click)="this.onClick($event)"
        (keyup)="this.calcPercentageOffset()"/>
      <span
        *ngIf="this.isPercentuale"
        class="Percentage"
        [ngStyle]="{
          'left': this.percentageOffset,
          'errore': this.showError
          }">
        %
      </span>
    </div>
    <div
      *ngIf="this.showError && this.campoInErrore">
      <i class="icon-Attenzione-pieno bd-icona"></i>
      <span class="testo-non-valido">{{ this.errorMessage }}</span>
    </div>
  </div>
  <ng-template #readOnlyView>
    <span
      class="{{ this.labelFormat }}"
      *ngIf="!this.customFormat; else customTextPega">
      {{ this.value }}
      <span class="{{ this.labelFormat }}" *ngIf="this.isPercentuale">%</span>
    </span>
    <ng-template #customTextPega>
      <span class="ReadOnlyContainer">
        <custom-text-style
          [textCss]="this.customFormat"
          [content]="this.value">
        </custom-text-style>
        <custom-text-style
          *ngIf="this.isPercentuale"
          [textCss]="this.customFormat"
          [content]="'%'">
        </custom-text-style>
      </span>
    </ng-template>
  </ng-template>
</ng-container>
