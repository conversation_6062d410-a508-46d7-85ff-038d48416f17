import { Component, Input } from '@angular/core';
import { AbstractControl, FormControl, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { IField } from '../../../models/models.commons';
import { ListenerErroreFormInterface, StatusService } from "../../../services/status.service";
import {
  TextCss,
  TextHandlerService,
} from '../../../services/text-handler.service';
import { UtilsService } from '../../../services/utils.service';
import { EventsMethod } from '../../../utils/eventsMethod';
import { MessagesService } from '../../../services/messages.service';
import { AnalyticsInterprete } from '../../../services/analytics.interprete.service';

@Component({
  templateUrl: './px-integer.component.html',
  styleUrls: ['./px-integer.component.scss'],
  selector: 'px-integer',
})
export default class PxIntegerComponent extends EventsMethod {
  private _standAloneFormIndex = -1;
  private _errorListener: ListenerErroreFormInterface;

  public disabled = false;
  public readOnly = true;
  public visible = true;
  public label = '';
  public value = '';
  public isPercentuale = false;

  public minValue = -1;
  public maxValue = -1

  public labelFormat = '';
  public customFormat: TextCss | null;

  public fieldControl: FormControl;
  public showError = false;

  public percentageOffset = '0px';

  public constructor(
    private _utilsService: UtilsService,
    private _statusService: StatusService,
    private _textHandlerService: TextHandlerService,
    private _messageService: MessagesService,
    private _analyticsService: AnalyticsInterprete
  ) {
    super(_messageService, _analyticsService);
    this._standAloneFormIndex = this._statusService.currentStandAloneFormIndex;
    this._errorListener = this._statusService
      .registraListenerDiErroreInputInvalido(this._standAloneFormIndex, statoErrore => this.showError = statoErrore);
  }

  @Input() public set data(input: { field: IField }) {
    if (input.field) {
      this.field = input.field;
      this.fieldControl = new FormControl();

      this.disabled = this.field?.disabled || false;
      this.visible = this.field?.visible || false;
      this.readOnly = this.field?.readOnly || false;
      this.value = this.field?.value || '';
      this.label = this.field?.label || '';
      this.isPercentuale = this.field?.fieldID?.toLowerCase()?.includes('percentuale') || false;
      if(!this.isPercentuale)
        this.isPercentuale = this.field?.customAttributes?.validation === 'percentage';

      if(this.isPercentuale){
        this.minValue = 0;
        this.maxValue = 100;
      }

      //Recuperiamo i validatori
      const validators: ValidatorFn[] = [];
      this.field.required && validators.push(Validators.required);
      if(this.isPercentuale)
        validators.push(this._validaPercentuale());
      this.fieldControl.setValidators(validators);

      //Gestiamo la label, in questo caso lo stile anche del value
      if (this.field.labelFormat) {
        this.customFormat = this._textHandlerService.getTextCss(this.field.labelFormat);
        if (!this.customFormat)
          this.labelFormat = this._utilsService.getClassFromFormat(this.field.labelFormat);
      }

      //Registriamo il control del field
      this.field.reference &&
        this._statusService.addControlFormGroup(this.field.reference, this.fieldControl);

      //Prevalorizziamo il field
      this.value && this.fieldControl.setValue(this.value);

      this.calcPercentageOffset();
      this.setValueProcessManagement();
      this.settaValoriGestioneProcesso();
    }
  }

  //region customValidators

  private _validaPercentuale(): ValidatorFn{
    return (control: AbstractControl): ValidationErrors | null => {
      let hasErrore;

      const value = control.value as string;
      const intValue = parseInt(value);
      if(!isNaN(intValue)){
        hasErrore = intValue < 0 || intValue > 100;
      }else hasErrore = true;


      let errore = null;
      if(hasErrore){
        errore = {errorePercentuale: true}
      }
      return errore;
    }
  }

  //endregion

  //region override default events

  public calcPercentageOffset() {
    if(this.isPercentuale) {
      const value = `${this.fieldControl?.value || '0'}`;
      this.percentageOffset = `${value.length * 10 + 12}px`;
    }
  }

  //endregion

  //region Beam


  public get campoInErrore(): boolean{
    return this.fieldControl.status === 'INVALID';
  }

  public get errorMessage(): string{
    let errore = "";
    if(this.fieldControl?.errors?.required)
      errore = "Questo campo è obbligatorio";
    else if(this.fieldControl?.errors?.errorePercentuale)
      errore = "Il valore inserito deve essere compreso tra 0 e 100";
    return errore;
  }

  //endregion
}
