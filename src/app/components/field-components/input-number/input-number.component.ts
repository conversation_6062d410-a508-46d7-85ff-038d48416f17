import {
  ChangeDetectorRef,
  Component,
  Input
} from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import {
  TextCss,
  TextHandlerService,
} from '../../../services/text-handler.service';
import { errors } from '../../../utils/errorMessage';
import { IActionSet, IControl, IField } from '../../../models/models.commons';
import { AnalyticsInterprete } from '../../../services/analytics.interprete.service';
import { MessagesService } from '../../../services/messages.service';
import { ListenerErroreFormInterface, StatusService } from "../../../services/status.service";
import { UtilsService } from '../../../services/utils.service';
import { EventsMethod } from '../../../utils/eventsMethod';

interface InputNumberComponentCustomAttributes {
  intWithMaxValue?: string;
  validationMessage?: string;
}

@Component({
  selector: 'tpd-input-number',
  templateUrl: './input-number.component.html',
  styleUrls: ['./input-number.component.scss'],
})
export class InputNumberComponent extends EventsMethod {
  @Input() public currencyClass = false;

  private _standAloneFormIndex = -1;

  public readOnly = false;
  public disabled = false;
  public maxLength: number | false = false;

  public label = '';
  public labelFormat = '';
  public labelCss: TextCss;
  public placeholder = '';

  public fieldControl = new FormControl('', null);
  public valoreVisualizzato = "0";

  public control: IControl = {};
  public actionsSet: IActionSet[] = [];

  public formatType = '';
  public validationMessage = '';

  public tooltip = '';
  public customAttributes: InputNumberComponentCustomAttributes = {};
  public min: number;
  public max: number;

  public showError = false;
  private errorListener: ListenerErroreFormInterface;

  constructor(
    private utilsService: UtilsService,
    public override messageService: MessagesService,
    public analyticsService: AnalyticsInterprete,
    private statusService: StatusService,
    private textHandlerService: TextHandlerService,
    private _cdr: ChangeDetectorRef
  ) {
    super(messageService, analyticsService);
    this._standAloneFormIndex = this.statusService.currentStandAloneFormIndex;
    this.errorListener = this.statusService.registraListenerDiErroreInputInvalido(this._standAloneFormIndex, statoErrore => this.showError = statoErrore);
  }

  @Input() set data(input: { field: IField }) {
    if (input && input.field) {
      this.field = input.field;

      if (this.field) {
        this.disabled = this.field.disabled ?? false;
        this.readOnly = this.field.readOnly ?? false;

        this.valoreVisualizzato = this.textHandlerService.formattaValuta(this.field.value);

        this.label = this.utilsService.htmlDecode(this.field.label);
        this.labelCss = this.textHandlerService.getTextCss(this.field.labelFormat);
        if (!this.labelCss)
          this.labelFormat = this.utilsService.getClassFromFormat(this.field.labelFormat ?? '');

        if(!this.disabled && !this.readOnly){
          this.fieldControl = new FormControl(this.field.value);
          this.control = this.field.control;
          this.customAttributes = this.field.customAttributes;
          this.maxLength = this.field.maxLength ?? false;

          if (this.control) {
            this.actionsSet = this.control.actionSets ?? [];
            if (this.control.modes && this.control.modes[0]) {
              this.placeholder = this.utilsService.htmlDecode(this.control.modes[0].placeholder);
              this.tooltip = this.utilsService.htmlDecode(this.control.modes[0].tooltip);
            }
          }

          if (this.customAttributes) {
            this.max = this.customAttributes.intWithMaxValue ? parseInt(this.customAttributes.intWithMaxValue) : undefined;
            this.min = this.max !== -1 ? 1 : undefined;
            this.validationMessage = this.customAttributes.validationMessage ?? '';
          }

          const validators = []; //Validatori di campo da inserire all'interno del field
          if (this.field.required) {
            // eslint-disable-next-line @typescript-eslint/unbound-method
            validators.push(Validators.required);
          }

          if (this.max && this.validationMessage) {
            validators.push(Validators.max(this.max));
            validators.push(Validators.min(this.min as number));
          }

          validators.length > 0 && this.fieldControl.setValidators(validators);

          this.statusService.addControlFormGroup(
            this.field.reference ?? '',
            this.fieldControl
          );

          this.statusService.addControlFormGroup(
            this.field.reference ?? '',
            this.fieldControl
          );

          this.setValueProcessManagement();
          this.settaValoriGestioneProcesso();
        }
      }
    }
  }

  public handleInput(event: KeyboardEvent){
    event.preventDefault();
    const code = event.code.toLowerCase();
    if(code.startsWith('digit') || code.startsWith('numpad') || code === 'comma' || code === 'backspace'){
      if(code === 'backspace' && this.valoreVisualizzato.length > 0)
        this.valoreVisualizzato = this.valoreVisualizzato.substring(0, this.valoreVisualizzato.length - 1);

      if(code === 'comma' && !this.valoreVisualizzato.includes(','))
        this.valoreVisualizzato = `${this.valoreVisualizzato}${event.key}`;

      if(code !== 'backspace' && code !== 'comma'){
        if(this.valoreVisualizzato.includes(',')){
          if(this.valoreVisualizzato.split(',')[1].length < 2)
            this.valoreVisualizzato = `${this.valoreVisualizzato}${event.key}`;
        }else this.valoreVisualizzato = `${this.valoreVisualizzato}${event.key}`;
      }

      if(!this.valoreVisualizzato.includes(',')){
        const targetValue = (this.valoreVisualizzato as any).replaceAll('.', '').split('').reverse();
        this.valoreVisualizzato = '';
        for(let i = 1; i <= targetValue.length; i++){
          this.valoreVisualizzato = `${this.valoreVisualizzato}${targetValue[i - 1]}`;
          if(i % 3 === 0)
            this.valoreVisualizzato = `${this.valoreVisualizzato}.`;
        }
        this.valoreVisualizzato = this.valoreVisualizzato.split('').reverse().join('');
        if(this.valoreVisualizzato.startsWith('.') || this.valoreVisualizzato.startsWith('0'))
          this.valoreVisualizzato = this.valoreVisualizzato.substring(1);
      }else if(this.valoreVisualizzato.startsWith(','))
        this.valoreVisualizzato = this.valoreVisualizzato.substring(1);
    }

    const value = (this.valoreVisualizzato as any).replaceAll('.', '').replaceAll(',', '.');
    this.fieldControl.setValue(value);
    (event.target as HTMLInputElement).value = this.valoreVisualizzato + ' €';
  }

  public errorMessage(): string {
    let message = '';

    if (this.fieldControl.hasError('max'))
      message = this.utilsService.htmlDecode(this.validationMessage);

    if (this.fieldControl.hasError('required')) message = errors['required'];

    return message;
  }

  public ngOnDestroy(): void {
    this.errorListener && this.errorListener.rimuoviListenerDiErroreInputInvalido();
    if (!this.readOnly)
      this.statusService.removeControlFormGroup(this.field.reference ?? '');
  }
}
