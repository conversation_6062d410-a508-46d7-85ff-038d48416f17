@import "../../../../styles.scss";

/* Tolgo frecce su input per Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Tolgo frecce su input per Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

.layout-input {
  position: relative;
  @media #{$bkp_mobile} {
    width: 90vw;
  }

  @media #{$bkp_tablet} {
    width: 500px;
  }

  @media #{$bkp_desktop} {
    width: 550px;
  }
}

.input-dati-utente {
  width: 100%;
  height: 48px;
  border-radius: 0;
  padding: 6px 12px;
  background-color: white;
  border: solid 1px $main_color;
  color: $middle-grey-pu;
  margin-top: 4px;

  @media #{$bkp_mobile} {
    max-width: none;
  }

  @media #{$bkp_tablet} {
    max-width: 500px;
  }

  @media #{$bkp_desktop} {
    max-width: 550px;
  }
}

.currency-background {
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' version='1.1' height='16px' width='85px'><text x='2' y='13' fill='gray' font-size='12' font-family='arial'>€</text></svg>");
  background-repeat: no-repeat;
  background-position: 150%;
  background-size: 40%;
}

.testo-non-valido {
  height: 14px;
  font-family: "Unipol Medium";
  font-size: 14px;
  font-stretch: normal;
  font-style: normal;
  line-height: 0.88;
  letter-spacing: normal;
  color: #ff001f;
}
.bd-icona {
  font-size: 22px;
  color: #ff001f;
  top: 5px;
  position: relative;
  margin-right: 5px;
}


.readonly-text-container {
  .defaultCurrencyLabel {
    font-family: $font-family-default;

    font-size: 22px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.25;
    letter-spacing: normal;
    text-align: left;

    color: $blue-primary;
  }

  .customPegaFontStyle {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .currencySymbol {
      padding-left: 8px;
    }
  }
}
