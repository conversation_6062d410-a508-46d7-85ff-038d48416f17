<ng-container *ngIf="field">
  <div
    *ngIf="!this.readOnly; else readOnlyTemplate"
    class="pdn-label-input layout-input">
    <ng-container
      *ngIf="this.field.labelReserveSpace || this.label">
      <span
        *ngIf="!this.labelCss"
        class="mrg-label-input {{ labelFormat }}">
        {{ label }}
      </span>
      <custom-text-style
        *ngIf="this.labelCss"
        class="mrg-label-input"
        [textCss]="this.labelCss"
        [content]="this.label">
      </custom-text-style>
    </ng-container>
    <input
      class="input-dati-utente"
      type="text"
      [placeholder]="this.placeholder"
      [value]="this.valoreVisualizzato + ' €'"
      [min]="this.min"
      [max]="this.max"
      [required]="this.field?.required || false"
      [ngClass]="{ 'disabled-input-box': disabled, 'border-red': false }"
      (blur)="onBlur($event)"
      (change)="onChange($event)"
      (focus)="onFocus($event)"
      (click)="onClick($event)"
      (keydown)="this.handleInput($event)"
      (keyup)="$event.preventDefault()"/>
  </div>
  <div
    *ngIf="
      this.showError &&
      this.fieldControl.touched &&
      this.fieldControl.invalid &&
      errorMessage() &&
      !this.disabled &&
      !this.readOnly
    ">
    <i class="icon-Attenzione-pieno bd-icona"></i>
    <span class="testo-non-valido">{{ this.errorMessage() }}</span>
  </div>
  <ng-template #readOnlyTemplate>
    <div class="readonly-text-container">
      <span *ngIf="!this.labelCss" class="{{ this.labelFormat }}">
        {{this.valoreVisualizzato + ' €'}}
      </span>
      <div *ngIf="!!this.labelCss" class="customPegaFontStyle">
        <custom-text-style
          [textCss]="this.labelCss"
          [content]="this.valoreVisualizzato + ' €'">
        </custom-text-style>
      </div>
    </div>
  </ng-template>
</ng-container>
