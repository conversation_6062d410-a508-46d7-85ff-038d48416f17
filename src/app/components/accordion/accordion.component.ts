import { AfterViewInit, Component, ElementRef, Input } from '@angular/core';
import { IField, IGroup } from "../../models/models.commons";
import { StatusService } from '../../services/status.service';
import RetrieverService from "../../services/retriever.service";
import PaddingService from "../../services/padding.service";
import { Helpers } from '@tpd-web-common-libs/nodejs-library';

@Component({
  selector: 'tpd-accordion',
  templateUrl: './accordion.component.html',
  styleUrls: ['./accordion.component.scss'],
})
export class AccordionComponent implements AfterViewInit{
  public static accordionOpenStatus: Map<string, boolean> = new Map();
  public static clearOpenAccordionStatus = () => AccordionComponent.accordionOpenStatus.clear();

  constructor(
    private statusService: StatusService,
    private _retriverService: RetrieverService,
    private _paddingService: PaddingService,
    private _selfReferece: ElementRef
  ) {}

  public groups: IGroup[];
  public headerGroups: IGroup[] = [];
  public ribbonGroups: IGroup[] = [];
  public contentGroups: IGroup[] = [];
  public footerGroups: IGroup[] = [];

  private titleId = '';
  private accordionId: string;
  public accordionType: string;
  public showAccordionArrow = true;
  public showContent = false;
  public styleAccordion: string;

  //Telematica
  public thrashIconTelematica: IField;

  //Ca
  public paddingDesktop: string;
  public paddingTablet: string;
  public paddingMobile: string;

  @Input() set dataAccordion(input: { accordionType: string; groups: IGroup[]; title?: string; }) {
    this.groups = input?.groups;
    this.accordionType = input?.accordionType;
    this.titleId = input?.title?? '';

    if (this.accordionType === '1') {
      const currentState = this.statusService.getStatusAccordionGaranzieByID(this.titleId);
      this.showContent = currentState === undefined ? true : currentState;
      this.statusService.setStatusAccordionGaranzie(this.titleId, this.showContent);
    }
    if (this.accordionType === '6' || this.accordionType === '4') {
      const currentState = this.statusService.getStatoAccordionPostalizzazioneByID(this.titleId);
      this.showContent = currentState === undefined ? false : currentState;
      this.statusService.setStatoAccordionPostalizzazione(this.titleId, this.showContent);
    }
    if (this.groups) {
      this._splitAccordionGroups(this.groups);
    }
  }

  public ngAfterViewInit(): void {
    if(Helpers.EnvironmentHelper.isClientSide()){
      if(this._selfReferece.nativeElement){
        const self = this._selfReferece.nativeElement as HTMLElement;
        const openStatusDetector = self.getElementsByClassName('AccordionOpenStatusDetector');
        if(openStatusDetector.length > 0)
          this.showContent = true;
      }
    }
  }

  private _splitAccordionGroups(groups: IGroup[]) {
    const hiddenData = this._retriverService.getFirstFieldInGroupsByType(groups, 'pxHidden');
    hiddenData && this._gestisciCustomAttributes(hiddenData.customAttributes);

    if(this.accordionType === 'Telematica'){
      const iconCestinoTelematica = this._retriverService.getFirstIconInGroupsByResource(groups, 'trash');
      if(iconCestinoTelematica)
        this.thrashIconTelematica = iconCestinoTelematica;
    }

    const accordionHeader = this._retriverService.getFirstLayoutInGroupsByGroupFormat(groups, 'AccordionHeader');
    if(accordionHeader){
      this.headerGroups = accordionHeader.groups;
      let ribbon = this._retriverService.getFirstLayoutInGroupsByGroupFormatContain(this.headerGroups, 'ribbon');
      if(ribbon){
        this.ribbonGroups = ribbon.groups;
        this.headerGroups = this.headerGroups.filter(groups => !groups?.layout?.groupFormat?.toLowerCase()?.includes('ribbon'));
      }else{
        ribbon = this._retriverService.getFirstLayoutInGroupsByGroupFormatContain(groups, 'ribbon');
        if(ribbon)
          this.ribbonGroups = ribbon.groups;
      }
      const icon = this._retriverService.getFirstFieldInGroupsByType(this.headerGroups, 'pxIcon');
      this.showAccordionArrow = icon?.customAttributes?.ShowAccordionArrow !== '1';
    }
    const accordionOpened = this._retriverService.getAllLayoutInGroupsByGroupFormat(groups, 'AccordionOpened');
    if(accordionOpened) {
      this.contentGroups = [];
      for(const accordionData of accordionOpened)
        this.contentGroups.push(...accordionData.groups);
    }
    const accordionClosed = this._retriverService.getFirstLayoutInGroupsByGroupFormat(groups, 'AccordionClosed');
    if(accordionClosed)
      this.footerGroups = accordionClosed.groups;
  }

  private _gestisciCustomAttributes(customAttributes: {[key: string]: string}){
    this.accordionId = customAttributes.accordionId;
    if(this.accordionId !== undefined && AccordionComponent.accordionOpenStatus.has(this.accordionId))
      this.showContent = AccordionComponent.accordionOpenStatus.get(this.accordionId);

    this.showContent = this.showContent === false ? customAttributes?.openAccordion === "true" : this.showContent;
    this.styleAccordion = customAttributes?.style;

    if(customAttributes.paddingDesktop)
      this.paddingDesktop = this._paddingService.trasformaPaggingPegaToPaddingCss(customAttributes.paddingDesktop);
    if(customAttributes.paddingTablet)
      this.paddingTablet = this._paddingService.trasformaPaggingPegaToPaddingCss(customAttributes.paddingTablet);
    if(customAttributes.paddingMobile)
      this.paddingMobile = this._paddingService.trasformaPaggingPegaToPaddingCss(customAttributes.paddingMobile);
  }

  public changeVisibility() {
    this.showContent = !this.showContent;
    if(this.accordionId !== undefined)
      AccordionComponent.accordionOpenStatus.set(this.accordionId, this.showContent);
    this.accordionType === '1' && this.statusService.setStatusAccordionGaranzie(this.titleId, this.showContent);
    (this.accordionType === '6' || this.accordionType === '4') &&
      this.statusService.setStatoAccordionPostalizzazione(this.titleId, this.showContent);
  }

  public get paddingStyle(): string{
    let esito = "";

    if(this.paddingDesktop || this.paddingTablet || this.paddingMobile){
      if(this.paddingDesktop)
        esito = `--paddingDesktop: ${this.paddingDesktop};`;
      if(this.paddingTablet)
        esito += `--paddingTablet: ${this.paddingTablet};`;
      if(this.paddingMobile)
        esito += `--paddingMobile: ${this.paddingMobile};`;
    }

    return esito;
  }

  public get paddingClass(): string{
    let esito = "";

    if(this.paddingDesktop || this.paddingTablet || this.paddingMobile){
      esito = "paddingContainer";
      if(this.paddingDesktop){
        esito += " desktop";
      }

      if(this.paddingTablet){
        esito += " tablet";
      }

      if(this.paddingMobile){
        esito += " mobile";
      }
    }

    return esito;
  }

  public get accordionTypeString(): string{
    return `accordion-${this.accordionType}`
  }

  public get isLocalExecuton(): boolean{
    let esito = false;

    if(Helpers.EnvironmentHelper.isClientSide()){
      esito = window.location.href.includes('localhost') && !window.location.href.includes(':3000');
    }

    return esito;
  }
}
