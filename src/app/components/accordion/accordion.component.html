<div *ngIf="groups" class="space-accordion">
  <ng-container *ngIf="accordionType" [ngSwitch]="accordionType">
    <div *ngSwitchCase="'3'" id="accordion3">
      <ng-container *ngTemplateOutlet="accordionInformative"></ng-container>
    </div>

    <div *ngSwitchCase="'4'" id="accordion4">
      <div
        *ngIf="headerGroups"
        class="accordion d-flex space-between align-center accordion-4">
        <groups
          [groups]="headerGroups"
          [groupClass]="'mimicASentence'">
        </groups>
        <i
          class="icon-Freccia-down size-l cursor-pointer"
          (click)="changeVisibility()"
          [ngClass]="{ 'freccia-su': showContent }">
        </i>
      </div>

      <div *ngIf="contentGroups" class="accordion">
        <div [hidden]="!showContent" class="accordion-4">
          <div class="content-4">
            <groups [groups]="contentGroups"></groups>
          </div>
        </div>
      </div>
    </div>

    <div *ngSwitchCase="'5'" id="accordion5">
      <ng-container *ngTemplateOutlet="accordionInformative"></ng-container>
    </div>

    <div *ngSwitchCase="'7'" id="accordion7">
      <div
        *ngIf="headerGroups"
        class="d-flex fd-column jsf-center accordion-7">
        <groups [groups]="headerGroups"></groups>
        <groups
          *ngIf="this.ribbonGroups.length !== 0"
          [groups]="ribbonGroups"
          [groupClass]="'ribbon7-layout'">
        </groups>
      </div>

      <span *ngIf="this.showContent" class="accordion-7-separator"></span>

      <div *ngIf="contentGroups" class="accordion">
        <div [hidden]="!showContent" class="accordion-7-content">
          <div class="content-7">
            <groups [groups]="contentGroups"></groups>
          </div>
        </div>
      </div>

      <span class="accordion-7-separator"></span>

      <div
        class="d-flex jsf-center accordion-7-footer"
        (click)="changeVisibility()">
        <button [hidden]="showContent" class="simple-white">
          <span class="button-text">Mostra dettagli</span>
        </button>
        <button [hidden]="!showContent" class="simple-white">
          <span class="button-text">Nascondi dettagli</span>
        </button>
        <i
          class="icon-Freccia-down size-l cursor-pointer"
          [ngClass]="{ 'freccia-su': showContent }">
        </i>
      </div>
    </div>

    <div *ngSwitchCase="'8'" [ngClass]="'Accordion-' + this.accordionType">
      <div *ngIf="this.headerGroups">
        <groups [groups]="this.headerGroups"></groups>
      </div>
      <div *ngIf="contentGroups" class="accordion">
        <div class="accordion-content" [hidden]="!showContent">
          <groups [groups]="contentGroups"></groups>
        </div>
      </div>
      <div *ngIf="footerGroups" class="accordion">
        <groups [groups]="footerGroups" [hidden]="showContent"></groups>
      </div>
      <span
        (click)="changeVisibility()"
        [ngClass]=" showContent ? 'accordionLabel accordionOpenLabel' : 'accordionLabel accordionCloseLabel'">
        {{ this.showContent ? "Vedi di meno" : "Vedi di più" }}
      </span>
    </div>

    <div *ngSwitchCase="'9'" id="accordion9">
      <ng-container *ngTemplateOutlet="accordionInformative"></ng-container>
    </div>

    <div
      *ngSwitchCase="'12'"
      class="Accordion12">
      <div class="accordionHead" *ngIf="this.headerGroups">
        <groups [groups]="headerGroups"> </groups>
      </div>
      <div *ngIf="this.contentGroups && this.showContent">
        <groups [groups]="contentGroups"></groups>
      </div>
      <div
        class="accordionFooter"
        *ngIf="this.footerGroups && !this.showContent"
      >
        <groups [groups]="footerGroups"></groups>
      </div>
      <span class="arrowButton" (click)="changeVisibility()">
          <span style="text-decoration: underline">Vedi e personalizza</span>
          <i
            class="icon-Freccia-down size-l cursor-pointer"
            [ngClass]="{ 'freccia-su': showContent }">
          </i>
        </span>
    </div>

    <div
      *ngSwitchCase="'Telematica'"
      class="AccordionTelematica">
      <div *ngIf="this.headerGroups" class="AccordionHead">
        <groups [groups]="headerGroups"></groups>
        <span class="IconContainer">
          <span class="PenIcon" (click)="this.changeVisibility()"></span>
          <tpd-px-icon *ngIf="this.thrashIconTelematica" [data]="{field: this.thrashIconTelematica}"></tpd-px-icon>
        </span>
      </div>
      <div
        *ngIf="this.contentGroups"
        [hidden]="!this.showContent"
        class="AccordionContent">
        <groups [groups]="contentGroups"></groups>
      </div>
      <div
        *ngIf="this.footerGroups"
        [hidden]="this.showContent">
        <groups [groups]="footerGroups"></groups>
      </div>
    </div>

    <div
      *ngSwitchDefault id="accordionDefault"
      class="{{this.accordionTypeString + ' ' + this.paddingClass}}"
      style="{{this.paddingStyle}}">
      <div
        *ngIf="headerGroups"
        class="accordion d-flex space-between align-center accordion-header">
        <groups
          [groups]="headerGroups"
          [groupClass]="'mimicASentence-responsiveAlign'">
        </groups>
        <i
          class="icon-Freccia-down size-l cursor-pointer"
          (click)="changeVisibility()"
          *ngIf="showAccordionArrow"
          [ngClass]="{ 'freccia-su': showContent }">
          <ng-container *ngIf="this.isLocalExecuton">v</ng-container>
        </i>
      </div>

      <div *ngIf="contentGroups" class="accordion">
        <div class="accordion-content" [hidden]="!showContent">
          <groups [groups]="contentGroups"></groups>
        </div>
      </div>

      <div *ngIf="footerGroups" class="accordion" id="footerAccordion">
        <groups [groups]="footerGroups" [hidden]="showContent"></groups>
      </div>
    </div>
  </ng-container>

  <ng-template #accordionInformative>
    <div
      class="accordion-informative-padding"
      [ngClass]="'accordion-' + accordionType">
      <div
        *ngIf="headerGroups"
        class="accordion d-flex space-between align-center">
        <groups
          [groups]="headerGroups"
          [groupClass]="'mimicASentence'">
        </groups>
        <i
          *ngIf="this.styleAccordion === 'Card' && this.accordionType === '9'; else iconaPlusCase"
          style="font-size: 30px"
          class="icon-Freccia-down size-l cursor-pointer"
          (click)="this.changeVisibility()"
          [ngStyle]="{ transform: showContent ? 'rotate(180deg)' : 'rotate(0)' }">
        </i>
        <ng-template #iconaPlusCase>
          <i
            style="font-size: 30px"
            class="icon-Chiudi cursor-pointer"
            (click)="changeVisibility()"
            [ngStyle]="{ transform: showContent ? 'rotate(0)' : 'rotate(45deg)' }">
          </i>
        </ng-template>
      </div>

      <div *ngIf="contentGroups" class="accordion">
        <div [hidden]="!showContent" class="mrg-t-24">
          <groups [groups]="contentGroups"></groups>
        </div>
      </div>

      <div *ngIf="footerGroups" class="accordion">
        <div [hidden]="showContent" class="mrg-t-24">
          <groups [groups]="footerGroups"></groups>
        </div>
      </div>
    </div>
  </ng-template>
</div>
