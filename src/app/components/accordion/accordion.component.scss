@import "../../../styles.scss";

$accordion-mrg-mobile: 16px 0;
$accordion-mrg-tablet: 24px 0;
$accordion-mrg-desktop: 24px 0;

.icon-Chiudi {
  color: $main-color;
}

.size-l {
  font-size: 40px;
}

.freccia-su {
  transform: rotate(180deg);
}

.accordion-informative-padding {
  padding: 19px 24px;
}

*[class^="accordion-"]{
  &.paddingContainer{
    &.desktop{
      --paddingDesktop: 0;
      @media #{$bkp_desktop} {
        border: 2px solid $ivory;
        padding: var(--paddingDesktop);
      }
    }

    &.tablet{
      --paddingTablet: 0;
      @media #{$bkp_tablet_only} {
        border: 2px solid $ivory;
        padding: var(--paddingTablet);
      }
    }

    &.mobile{
      --paddingMobile: 0;
      @media #{$bkp_mobile_only} {
        border: 2px solid $ivory;
        padding: var(--paddingMobile);
      }
    }
  }
}

.accordion-2 {
  .accordion-header {
    height: 50px;
    border: solid 1px $secondary-darkest;
    background-color: white;
    padding: 15px 20px;
  }

  .accordion-content {
    padding: 15px 20px;
    background-color: white;
    border: solid 1px $secondary-darkest;
    border-top: none;
  }
}

.accordion-3 {
  border: solid 2px $soft-grey;
}

.accordion-4 {
  background: $ivory;
  @media #{$bkp_mobile} {
    padding: 20px;
  }
  @media #{$bkp_tablet} {
    padding: 20px 32px;
  }
  @media #{$bkp_desktop} {
    padding: 24px 36px;
  }

  .icon-Freccia-down {
    color: $main-color;
  }
}

.accordion-5 {
  background-color: $light_blue;
}

.accordion-6::ng-deep {
  background-color: white;
  @media #{$bkp_mobile} {
    padding: 20px;
  }
  @media #{$bkp_tablet} {
    padding: 20px 32px;
  }
  @media #{$bkp_desktop} {
    padding: 24px 36px;
  }
  .responsive2colLarge {
    justify-content: flex-start;
  }
  .layout-address-autocomplete {
    align-items: center;
    width: 100%;

    @media #{$bkp_tablet} {
      max-width: 550px;
    }
    @media #{$bkp_desktop} {
      max-width: 550px;
      .w-100 {
        width: 100% !important;
      }
    }
    .input-dati-utente {
      @media #{$bkp_mobile} {
        max-width: 100%;
      }
    }
  }
}

.accordion-9 {
  border: solid 2px #f0f0f0;
}

.space-accordion {
  // @media #{$bkp_mobile} {
  //   width: 100%;
  //   margin-left: auto;
  //   margin-right: auto;
  // }
  // @media #{$bkp_tablet} {
  //   width: 720px;
  // }
  // @media #{$bkp_desktop} {
  //   width: 1062px;
  // }
}

.accordion-7::ng-deep {
  background: $blue-primary;
  margin: 0 auto;
  padding: 24px 16px;
  border-radius: 24px 24px 0 0;
  @media #{$bkp_mobile} {
    // width: 288px;
    // border-radius: 10px 10px 0 0;
  }
  @media #{$bkp_tablet} {
    // width: 528px;
    // border-radius: 20px 20px 0 0;
  }
  @media #{$bkp_desktop} {
    // width: 528px;
    // border-radius: 30px 30px 0 0;
  }
}

.accordion-7-content {
  background: $blue-primary;
  margin: 0 auto;
  padding: 24px 16px;
}

.accordion-7-separator{
  display: block;
  width: 100%;
  height: 1px;
  background-color: $blue-primary;
  padding: 0 16px;

  &::after{
    content: '';
    display: block;
    width: 100%;
    height: 100%;
    background-color: $assurance-package-container-color;
  }
}

.accordion-7-footer {
  background-color: $blue-primary;
  margin: 0 auto;
  column-gap: 10px;
  border-radius: 0 0 24px 24px;
  @media #{$bkp_mobile} {
    height: 56px;
    align-items: center;
  }
  @media #{$bkp_tablet} {
    height: 56px;
    align-items: center;
  }
  @media #{$bkp_tablet} {
    height: 64px;
    align-items: center;
  }

  .icon-Freccia-down {
    color: white;
  }

  .simple-white {
    background-color: transparent;
    border: none;
    margin: 0;
    padding: 0;
    .button-text {
      font-family: $font-family-medium;
      font-size: $font-text-size;
      color: white;
      font-weight: 500;
      line-height: 1.25;
      cursor: pointer;
      text-decoration: underline;
    }
  }
}

.Accordion-8 {
  max-width: 1062px;
  width: 100%;
  margin: 0 auto;
  background-color: $secondary-lightest;
  border: solid 1px $secondary-opaque;
  border-radius: 24px;
  padding: 24px;

  .accordionLabel {
    display: flex;
    justify-content: center;
    max-width: fit-content;
    width: 100%;
    margin: 31px auto 0;
    font-size: 18px;
    font-weight: bold;
    color: $blue-primary;
    gap: 16px;
    cursor: pointer;

    &::after {
      content: ">";
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: inherit;
      font-weight: inherit;
    }
  }
  .accordionOpenLabel {
    &::after {
      transform: rotateZ(-90deg) scaleY(1.5);
    }
  }

  .accordionCloseLabel {
    &::after {
      transform: rotateZ(-270deg) scaleY(1.5);
    }
  }
}

.Accordion12 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;

  background-color: white;
  border-radius: 24px;
  padding: 16px;
  gap: 16px;

  .arrowButton {
    display: flex;
    justify-content: center;
    align-items: center;

    width: 100%;
    gap: 4px;

    cursor: pointer;
    color: $text-blue;

    font-size: 16px;
  }
}

.AccordionTelematica{
  border: 2px solid $background-card-disabled;
  padding: 36px 60px;

  @media #{$bkp_mobile_only} {
    padding: 24px;
  }

  @media #{$bkp_tablet_only} {
    padding: 32px;
  }

  >.AccordionHead{
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;

    >.IconContainer{
      display: flex;
      flex-direction: row;
      gap: 32px;

      @media #{$bkp_mobile_only} {
       gap: 24px;
      }

      >.PenIcon{
        aspect-ratio: 1 / 1;
        flex-shrink: 0;
        width: 26px;
        background-image: url("/NextAssets/icons/edit_matita.png");
        background-size: cover;
        cursor: pointer;
      }
    }
  }

  >.AccordionContent{
    margin-top: 8px;
    @media #{$bkp_tablet_only} {
      margin-top: 16px;
    }
  }
}
