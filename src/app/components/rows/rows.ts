import { Component, Input } from '@angular/core';
import { IGroup, IRow } from '../../models/models.commons';
@Component({
  selector: 'tpd-rows',
  templateUrl: './rows.html',
  styleUrls: ['./rows.scss'],
})
export class Rows {
  private _rows?: IRow[];
  private _groups: IGroup[] = [];

  constructor() {
    //todo nothing
  }
  @Input() groupClass = ''; // layout class

  @Input() set rows(input: IRow[] | undefined) {
    this._rows = input;
    if (this._rows) {
      for (const row of this._rows) {
        if (row.groups)
          for (const group of row.groups) this._groups.push(group);
      }
    }
  }

  get rows(): IRow[] {
    return this._rows ?? [];
  }

  get groups(): IGroup[] {
    return this._groups;
  }
}
