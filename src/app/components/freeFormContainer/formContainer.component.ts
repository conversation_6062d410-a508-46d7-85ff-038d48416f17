import { Component, Input} from '@angular/core';
import { IGroup } from '../../models/models.commons';
import { StatusService } from '../../services/status.service';

@Component({
  selector: 'tpd-form-container',
  templateUrl: './formContainer.component.html',
  styleUrls: ['./formContainer.component.scss'],
})
export class FormContainer {
  private _groups: IGroup[] = [];

  constructor(private statusService: StatusService) {}

  @Input() set groups(input: IGroup[]) {
    this._groups = input;
    this.statusService.inizializzaFormIndipendente();
  }

  public get groups(): IGroup[]{
    return this._groups || [];
  }
}
