import { Component, ElementRef, <PERSON><PERSON><PERSON>roy } from "@angular/core";
import { FormControl } from "@angular/forms";
import { StatusService } from "../../services/status.service";

@Component({
  template: '<span>BloccoInvio</span>',
  selector: 'submit-blocker'
})
export default class SubmitBlockerComponent implements OnDestroy{
  private _selfUniqueId: string;
  private _standAloneFormIndex: number;

  public constructor(
    private _self: ElementRef,
    private _statusService: StatusService
  ) {
    const self = this._self.nativeElement as HTMLElement;
    self.style.display = 'none';

    this._standAloneFormIndex = this._statusService.currentStandAloneFormIndex;
    this._selfUniqueId = `submit-blocker-${Date.now()}`;

    const formControl = new FormControl();
    formControl.setErrors({block: true});
    this._statusService.addControlFormGroup(this._selfUniqueId, formControl);
  }

  public ngOnDestroy(): void {
    this._statusService.removeControlFormGroup(this._selfUniqueId);
  }
}
