@import "../../../styles.scss";

.tooltip-groups-container {
  cursor: pointer;
}

.box-tooltip {
  display: flex;
  background: #0f3250;
  color: white;
  padding: 12px;
  z-index: 1000;
  position: absolute;
  font-family: "Unipol";
  min-width: 300px;
  justify-content: space-between;

  ::after {
    content: "";
    position: absolute;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #0f3250 transparent transparent transparent;
  }
}

.right-top {
  left: 0;
  bottom: 30px;
}

.right-top {
  ::after {
    top: 100%;
    left: 3%;
  }
}

.left-top {
  bottom: 30px;
  right: 0;
}

.left-top {
  ::after {
    top: 100%;
    right: 3%;
  }
}

.right-bottom {
  left: 1px;
  top: 25px;
}

.right-bottom {
  ::after {
    transform: rotate(180deg);
    bottom: 100%;
    left: 3%;
  }
}

.left-bottom {
  top: 25px;
  right: 0;
}

.left-bottom {
  ::after {
    transform: rotate(180deg);
    bottom: 100%;
    right: 3%;
  }
}
.unipol-popup-background {
  background-color: transparent;
  opacity: 0.9;
  position: fixed;
  display: flex;
  top: 0;
  left: 0;
  margin: auto;
  width: 100%;
  height: 100%;
  z-index: 999;
}
