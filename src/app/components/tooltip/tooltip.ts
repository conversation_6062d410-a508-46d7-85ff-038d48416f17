import { Component, Input } from '@angular/core';
import { IGroup } from '../../models/models.commons';
import { TooltipDirection, UtilsService } from '../../services/utils.service';

const mapperDirectionTooltip = {
  [TooltipDirection.TooltipLD]: 'left-bottom',
  [TooltipDirection.TooltipLU]: 'left-top',
  [TooltipDirection.TooltipRD]: 'right-bottom',
  [TooltipDirection.TooltipRU]: 'right-top',
};

@Component({
  selector: 'tooltip-dx-api',
  templateUrl: './tooltip.html',
  styleUrls: ['./tooltip.scss'],
})
export class Tooltip {
  private _data!: any;
  public filteredGroups: IGroup[] = [];
  public tooltipLayoutGroups: IGroup[] = [];
  public isOpen = false;
  public background = false;
  public directionTooltip = 'left-bottom';

  constructor(private utilsService: UtilsService) {}

  @Input() set data(input: {
    groups: IGroup[];
    tooltipDirection: TooltipDirection;
  }) {
    this._data = input;
    this.tooltipLayoutGroups = this.data.groups;
    this.directionTooltip = mapperDirectionTooltip[this.data.tooltipDirection];
  }

  get data() {
    return this._data;
  }

  openTooltip() {
    this.isOpen = true;
    this.background = true;
  }

  close() {
    if (this.isOpen) {
      this.utilsService.setClosedToolTip(true);
      this.isOpen = false;
      setTimeout(() => {
        this.background = false;
      }, 1500);
    }
  }
}
