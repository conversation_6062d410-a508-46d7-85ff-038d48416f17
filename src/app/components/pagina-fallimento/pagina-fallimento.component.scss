@import "../../../styles.scss";

.PaginaFallimentoContainer{
  $maxWidthContainer: 600px;

  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;

  max-width: 100%;
  width: $maxWidthContainer;
  margin: 0 auto;

  >.FallimentoImage{
    aspect-ratio: 1 / 1;
    display: block;
    width: 40px;
    margin-bottom: 16px;
  }

  >h1{
    font-family: "Unipol Medium";
    font-size: 24px;
    color: $blue-primary;
    margin-top: 0;
    margin-bottom: 12px;

    text-align: center;
    max-width: 100%;
    width: $maxWidthContainer;
  }

  >p{
    font-family: "Unipol";
    font-size: 18px;
    color: $blue-primary;
    margin-bottom: 16px !important;

    text-align: center;
    max-width: 100%;
    width: $maxWidthContainer;
  }

  >.BannerDownloadApp{
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 32px 46px;
    gap: 16px;

    max-width: 100%;
    width: $maxWidthContainer;
    background-color: #eef6fc;

    >h2{
      font-family: "Unipol Medium";
      font-size: 18px;
      color: $blue-primary;
      margin: 0;

      text-align: center;
    }

    >.ZonaPulsanti{
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;

      max-width: 400px;

      >img{
        object-fit: cover;
        width: 100%;
        //filter: invert(1) grayscale(1);
        border-radius: 8px;

        &.scale{
          transform: scaleY(1.1) translateY(-7%);
        }

        cursor: pointer;
      }
    }

    >a{
      font-family: "Unipol Medium";
      font-size: 18px;
      color: $blue-primary;

      text-align: center;
    }
  }
}
