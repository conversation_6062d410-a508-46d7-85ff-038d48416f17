import { Component, ElementRef, Inject, Input, ViewChild } from "@angular/core";
import { AngularMFE, CommonMFE, Helpers } from "@tpd-web-common-libs/nodejs-library";
import { AppProps } from "../../app.props";
import { BehaviorSubject } from "rxjs";

const AngularMicroFrontendClass = AngularMFE.Class.AngularMicrofrontendFactory({
  BehaviorSubject,
  AppProps,
});

@Component({
  selector: 'pagina-fallimento',
  templateUrl: './pagina-fallimento.component.html',
  styleUrls: ['./pagina-fallimento.component.scss']
})
export default class PaginaFallimentoComponent extends AngularMicroFrontendClass{
  @ViewChild('navigationHeader', { read: ElementRef }) public navigationHeader: ElementRef;

  @Input() motivoFallimento: string;

  constructor(@Inject(AppProps) private appProps: AppProps) {
    super(appProps);
  }

  get remoteDependencies(): CommonMFE.Types.TRemoteDependency[] {
    return [
      {
        elementRef: this.navigationHeader.nativeElement,
        props: {
          ...this.appProps,
          __mfeName__: 'tpdNavigationHeader',
          id: `${this.appProps.id}-pagina-fallimento`,
          title: 'Errore',
          hideBackBtn: true,
          hideCloseBtn: false,
          onCloseBtnClicked: () => {Helpers.RouterHelper.goTo('/homepage')}
        },
        updateProps: (newProps) => {
          return {
            ...newProps,
            __mfeName__: 'tpdNavigationHeader',
            id: `${this.appProps.id}-pagina-fallimento`,
            title: 'Errore',
            hideBackBtn: true,
            hideCloseBtn: false,
            onCloseBtnClicked: () => {Helpers.RouterHelper.goTo('/homepage')}
          };
        },
      },
    ];
  }

  public get titolo(): string{
    let esito = '';
    switch (this.motivoFallimento){
      default:
        esito = "CI DISPIACE CHE NON SEI RIUSCITO A CONCLUDERE IL CONTRATTO";
        break;
    }
    return esito;
  }

  public get contenuto(): string[]{
    let esito: string[] = [];

    switch (this.motivoFallimento){
      default:
        esito = [
          "Informa il proprietario del veicolo di recarsi in agenzia oppure iniziare l'acquisto online tramite sito / app.",
          "Scopri le altre protezioni disponibili per te."
        ];
        break;
    }

    return esito
  }

  public handleDownloadAppApple(){
    Helpers.RouterHelper.goTo('https://apps.apple.com/it/app/unipolsai-assicurazioni/id1279702796');
  }

  public handleDownloadAppAndroid(){
    Helpers.RouterHelper.goTo('https://play.google.com/store/apps/details?id=com.UnipolSaiApp');
  }
}
