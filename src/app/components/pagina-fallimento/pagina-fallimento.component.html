<tpd-navigation-header-container #navigationHeader></tpd-navigation-header-container>
<div class="PaginaFallimentoContainer">
  <img alt="ImmagineErrore" class="FallimentoImage" src="/NextAssets/icons/redcheck.png">
  <h1>{{this.titolo}}</h1>
  <p *ngFor="let content of this.contenuto">
    {{content}}
  </p>
  <div class="BannerDownloadApp">
    <h2>Per restare sempre informato, scarica l'app o acceddi all'area riservata</h2>
    <span class="ZonaPulsanti">
      <img (click)="this.handleDownloadAppApple()" alt="DownloadAppApple" src="/UnipolSaiThemeDynamicWAR/themes/html/dynamicSpots/assets/images/footer/applestore.png">
      <img (click)="this.handleDownloadAppAndroid()" alt="DownloadAppAndroid" src="/UnipolSaiThemeDynamicWAR/themes/html/dynamicSpots/assets/images/footer/googleplay.png">
    </span>
    <a href="/accesso">Accedi all'area riservata</a>
  </div>
</div>
