<ng-container *ngIf="this.hasElements">
  <div
    *ngIf="showCarousel"
    class="carousel-container">
    <carousel-arc [pinAzioniRapideMode]="false">
      <ng-container *ngFor="let card of this.cards">
        <layout style="max-width: unset !important; width: auto !important;" [layout]="card"></layout>
      </ng-container>
    </carousel-arc>
  </div>
  <div *ngIf="this.cards.length === 1" class="cards-not-carousel">
    <layout [layout]="this.cards[0]"></layout>
  </div>
  <div
    *ngIf="this.cards.length === 2 && this.screenSize === this.screenSizeEnum.desktop"
    class="cards-not-carousel">
    <layout [layout]="this.cards[0]"></layout>
    <layout [layout]="this.cards[1]"></layout>
  </div>
</ng-container>
