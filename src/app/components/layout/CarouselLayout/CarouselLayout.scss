@import "../../../../styles.scss";

.carousel-container::ng-deep {
  align-self: normal;
  margin-inline: 24px;

  .showArrow{
    padding-left: 64px !important;
    padding-right: 64px !important;

    @media #{$bkp_mobile_only} {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }
  }

  .ARCRightArrow, .ARCLeftArrow{
    aspect-ratio: 1 / 1 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;
    flex-grow: 0 !important;
    color: white !important;
    width: 40px !important;
    background-color: $blue-primary !important;
    border-radius: 50% !important;
    transform: translateY(-50%) !important;

    @media #{$bkp_mobile_only} {
      display: none !important;
    }
  }

  .PinZone{
    display: none !important;
    @media #{$bkp_mobile_only} {
      display: flex !important;
    }
  }

  .CardProtezioneContainer{
    margin-right: 8px;
  }
}

.cards-not-carousel {
  display: flex;
  width: auto;
  gap: 8px;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}
