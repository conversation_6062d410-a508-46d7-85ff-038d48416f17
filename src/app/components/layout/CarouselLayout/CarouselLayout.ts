import { Component, Input, AfterViewInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Helpers } from '@tpd-web-common-libs/nodejs-library';
import { IGroup, ILayout, IView } from "../../../models/models.commons";
import RetrieverService from "../../../services/retriever.service";

export enum ScreenSizeEnum {
  desktop = 'desktop',
  tablet = 'tablet',
  mobile = 'mobile',
}

export const BKPMapper: {[key: string]: number} = {
  'desktop': 1280,
  'tablet': 768,
  'mobile': 350
}

@Component({
  selector: 'carousel-layout',
  templateUrl: './CarouselLayout.html',
  styleUrls: ['./CarouselLayout.scss'],
})
export default class CarouselLayout implements AfterViewInit, OnDestroy{
  public screenSizeEnum = ScreenSizeEnum;

  public screenSize: ScreenSizeEnum;
  public cards: ILayout[] = [];

  public constructor(private _retrieverService: RetrieverService) {
  }

  public ngAfterViewInit(): void {
    if(Helpers.EnvironmentHelper.isClientSide()){
      window.addEventListener('resize', this._checkScreenSize.bind(this));
    }
  }

  @Input() set data(layout: ILayout) {
    if(layout) {
      this._checkScreenSize();
      this._retrieveCards(layout?.groups || []);
    }
  }

  @Input() set fieldData(view: IView){
    if(view){
      this._checkScreenSize();
      this._retrieveCards([{view}]);
    }
  }

  private _retrieveCards(groups: IGroup[]): void {
    const carouselCards = this._retrieverService.getAllCustomComponentsInGroupsByComponentName(groups, 'CarouselCard');
    const cardProtezione = this._retrieverService.getAllCustomComponentsInGroupsByComponentName(groups, 'CardProtezione');
    this.cards.push(...carouselCards, ...cardProtezione);
  }

  private _checkScreenSize() {
    if(Helpers.EnvironmentHelper.isClientSide()) {
      if (window.innerWidth <= BKPMapper[ScreenSizeEnum.tablet]) {
        this.screenSize = ScreenSizeEnum.mobile;
      } else if (
        window.innerWidth > BKPMapper[ScreenSizeEnum.tablet] &&
        window.innerWidth <= BKPMapper[ScreenSizeEnum.desktop]
      ) {
        this.screenSize = ScreenSizeEnum.tablet;
      } else if (window.innerWidth > BKPMapper[ScreenSizeEnum.desktop]) {
        this.screenSize = ScreenSizeEnum.desktop;
      }
    }
  }

  public get hasElements(): boolean {
    return this.cards && this.cards.length > 0;
  }

  public get showCarousel(): boolean{
    let esito = false;
    if(Helpers.EnvironmentHelper.isClientSide()){
      esito = this.cards.length > 2 || ( this.cards.length === 2 && this.screenSize !== this.screenSizeEnum.desktop )
    }
    return esito;
  }

  public ngOnDestroy(): void {
    if(Helpers.EnvironmentHelper.isClientSide()){
      window.removeEventListener('resize', this._checkScreenSize.bind(this));
    }
  }
}
