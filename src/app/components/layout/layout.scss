@import "../../../styles.scss";

.d-none {
  display: none;
}

.w-80 {
  width: 80vw;
}

.advice-highlight {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: $advice-color;

  .sub-layout {
    @media #{$bkp_mobile} {
      width: $width_container_mobile;
      padding: 24px 20px;
    }

    @media #{$bkp_tablet} {
      width: $width_container_tablet;
      padding: 24px 0;
    }

    @media #{$bkp_desktop} {
      width: $width_container_desktop;
      padding: 24px 0;
    }
  }
}

.BoxWebBorderGrayLayout {
  border-style: solid;
  border-color: $border-card-disabled;
}

.CardBorderLayout {
  border: solid $border-card-disabled 0;

  &.bottom {
    border-bottom-width: 1px;
  }

  &.top {
    border-top-width: 1px;
  }

  &.left {
    border-left-width: 1px;
  }

  &.right {
    border-right-width: 1px;
  }
}

.DefaultBgGrey {
  background-color: $ivory;
  flex-grow: 1;
  min-height: 100%;
  height: 100%;

  &.rounded {
    $borderRadius: 24px;
    $desktopTabletPadding: 24px 0;
    $mobilePadding: 16px 16px 24px 16px;

    border-top-right-radius: $borderRadius;
    border-top-left-radius: $borderRadius;

    @media #{$bkp_mobile} {
      padding: $mobilePadding;
    }

    @media #{$bkp_tablet} {
      padding: $desktopTabletPadding;
    }
  }
}

.AColDRowTRowMCol {
  &::ng-deep{
    >groups >.GroupDetector{
      display: flex;
      flex-direction: row;
      align-items: center;

      @media #{$bkp_mobile_only} {
        flex-direction: column;
        align-items: flex-start;
      }
    }
  }

  &:has(.AColDRowTRowMCol.gap16){
    &::ng-deep{
      >groups >.GroupDetector{
        width: 100%;
        align-items: stretch;
        justify-content: center;
      }
    }
  }

  &.gap16{
    &::ng-deep{
      >groups >.GroupDetector{
        justify-content: center;
        gap: 20px;

        @media #{$bkp_mobile_only} {
          gap: 16px;
          align-items: stretch;
        }
      }
    }
  }
}

.CardBgWhite {
  flex-grow: 1;
  min-height: 100%;
  height: 100%;

  background-color: white;
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
}

