import {
  Component,
  ElementRef,
  Input,
  AfterViewInit,
  ViewChild,
} from '@angular/core';
import { IField, IGroup, ILayout } from '../../../models/models.commons';

@Component({
  selector: 'splitted-radio',
  templateUrl: './SplittedRadioLayout.html',
  styleUrls: ['./SplittedRadioLayout.scss'],
})
export default class SplittedRadioLayout implements AfterViewInit {
  private _groups: IGroup[] = []; //Gruppo del layout
  private _radioField: IField | undefined; //Field che identifica il radio

  private _separatorIndex: number | undefined; //Index in cui inserire il separator
  private _radioSeparator: ILayout | undefined; //Layout del separator

  @ViewChild('radioSeparatorLayout')
  public radioSeparatorLayout: ElementRef<HTMLElement>; //Layout del separator renderizzato

  constructor(private _currentElement: ElementRef<HTMLElement>) {}

  public ngAfterViewInit(): void {
    if (this._currentElement && this._radioSeparator) {
      const nativeElement = this._currentElement.nativeElement;

      //Se non trova il container fare un controllo sul template del radio per verificare che sia presente un container dei radio con un attributo splitter_radio_container
      const splittedRadioContainer = nativeElement.querySelectorAll(
        '*[splitted_radio_container]'
      );
      if (splittedRadioContainer.length === 1) {
        const container = splittedRadioContainer.item(0) as HTMLElement;
        if (
          this._separatorIndex &&
          this.radioSeparatorLayout.nativeElement &&
          this.separatorIndex < container.children.length
        ) {
          const separatorLayout = this.radioSeparatorLayout.nativeElement;
          separatorLayout.remove();
          container.insertBefore(
            separatorLayout,
            container.children.item(this._separatorIndex)
          );
        }
      }
    }
  }

  @Input() public set groups(groups: IGroup[]) {
    if (groups) {
      this._groups = groups;
      //Ricerca degli elementi da visualizzare
      for (const group of this._groups) {
        if (group.layout && group.layout.groupFormat === 'RadioSeparator')
          this._radioSeparator = group.layout;
        if (group.field && group.field.control.type === 'pxRadioButtons')
          this._radioField = group.field;
      }

      //Ricerca del layout index
      if (
        this._radioField &&
        this._radioField.customAttributes &&
        this._radioField.customAttributes.separatorLayoutIndex
      )
        this._separatorIndex = parseInt(
          this._radioField.customAttributes.separatorLayoutIndex
        );
    }
  }

  get radioField(): IField | undefined {
    return this._radioField;
  }

  get radioSeparator(): ILayout | undefined {
    return this._radioSeparator;
  }

  get separatorIndex(): number | undefined {
    return this._separatorIndex;
  }
}
