import { Component, ElementRef, Input, OnInit } from '@angular/core';
import { Observable } from 'rxjs';
import {
  regexpCol,
  regexpRow,
  regexpRowSpace,
  regexpCard,
  regexpBox,
} from '../../utils/regex';
import { IGroup, ILayout } from '../../models/models.commons';

import {
  GroupsClass,
  SupportedGroupFormat,
  SupportedLayoutFormat,
  TooltipDirection,
  UtilsService,
} from '../../services/utils.service';
import PuPaddingLayout from './PuPaddingLayout/PuPaddingLayout';

type TTooltip = {
  groups: IGroup[];
  tooltipDirection: TooltipDirection;
};
@Component({
  selector: 'layout',
  templateUrl: './layout.html',
  styleUrls: ['./layout.scss'],
})
export class Layout implements OnInit {
  public constructor(
    private utilsService: UtilsService,
    private _elementRef: ElementRef
  ) {
    /*DO NOTHING*/
  }

  public tooltip: TTooltip = {
    groups: [],
    tooltipDirection: TooltipDirection.TooltipLU,
  };

  private _layout: ILayout;
  public groupFormat!: SupportedGroupFormat;
  public layoutFormat: SupportedLayoutFormat | undefined;

  public supportedGroup = SupportedGroupFormat;

  public columnsWidth: string[];
  public variableAccordionShowAfterValue: string;
  public flagShowContent$: Observable<boolean>;
  public gClass = GroupsClass;
  public styleInstructions = '';

  //region informazioni dinamiche layout
  public boxWebBorderGraySize = '1px';
  public cardBorderDirection = 'bottom';

  wasDynamic = false;
  customAttributes: Record<string, string> = {};

  public ngOnInit(): void {
    if (this.groupFormat === 'showAfterN') {
      const parsedVariableNumber = parseInt(this.variableAccordionShowAfterValue);
      if (!this.utilsService.waitingFor && parsedVariableNumber > 0)
        this.flagShowContent$ = this.utilsService.getVisibility(parsedVariableNumber);
      else this.flagShowContent$ = this.utilsService.getVisibility(0);
    }
  }

  @Input() set layout(input: ILayout) {
    this._layout = input;
    if (
      this._layout &&
      this._layout.groupFormat &&
      this._layout.groupFormat.includes('Dynamic') &&
      this._layout.repeatLayoutFormat
    ) {
      this.wasDynamic = true;
      this._layout.groupFormat = this._layout.repeatLayoutFormat;
    }
    if (this._layout && this._layout.customAttributes) {
      this.customAttributes = this._layout.customAttributes;
    }
    this.tooltip.groups = this._layout.groups ? this._layout.groups : [];
    if (this.layout && this.layout.layoutFormat)
      this.layoutFormat = this.layout.layoutFormat as SupportedLayoutFormat;

    if (this._layout && this._layout.groupFormat) {
      const regex = /\d{2}/;
      if (this.layout.groupFormat.includes(' CS')) {
        const formatList = this._layout.groupFormat?.split(' CS');
        if (formatList) this._layout.groupFormat = formatList[0];
      }
      if (this.layout.groupFormat.includes('Inline grid') && regex.test(this.layout.groupFormat)) {
        const words = this.utilsService
          .htmlDecode(this._layout.groupFormat ?? '')
          .split(' ');

        this.columnsWidth = words.filter((word: any) => regex.test(word));
        const gFormat = this._layout.groupFormat
          ? this._layout.groupFormat.replace(regex, 'x').replace(regex, 'y')
          : undefined;
        this.groupFormat = gFormat
          ? (this.utilsService.mapperGroupFormat as any)[gFormat]
          : undefined;
      } else if (this.layout.groupFormat.includes('ShowAfter') || this.layout.groupFormat.includes('Accordion')) {
        this._setupAccordionLayout()
      } else if (
        new RegExp(regexpCol).test(this.layout.groupFormat) ||
        new RegExp(regexpRow).test(this.layout.groupFormat) ||
        new RegExp(regexpRowSpace).test(this.layout.groupFormat)
      ) {
        const instructions = this.utilsService.htmlDecode(this._layout.groupFormat ?? '').split(' ');
        if (instructions) {
          this.groupFormat = instructions[0].toLowerCase() === 'row' ? SupportedGroupFormat.row : SupportedGroupFormat.col;
          this.styleInstructions = this._layout.groupFormat? this._layout.groupFormat : '';
        }
      } else if (new RegExp(regexpCard).test(this.layout.groupFormat)) {
        this.groupFormat = SupportedGroupFormat.card;
        this.styleInstructions = this._layout.groupFormat ? this._layout.groupFormat: '';

      } else if (
        new RegExp(regexpBox).test(this.layout.groupFormat) &&
        !this._layout.groupFormat.toLowerCase().replace(' ', '').startsWith('boxwebbordergray')
      ) {
        this.groupFormat = SupportedGroupFormat.box;
        this.styleInstructions = this._layout.groupFormat ? this._layout.groupFormat : '';
      } else if (
        PuPaddingLayout.CreateRegex().test(this._layout.groupFormat)
      ) {
        this.groupFormat = SupportedGroupFormat.PuPadding;
      } else if (this._layout.groupFormat.toLowerCase().replace(' ', '').startsWith('boxwebbordergray')) {
        this._setupBoxWebBorderGray(this._layout.groupFormat);
      } else if (this._layout.groupFormat.toLowerCase().startsWith('cardborder')) {
        this._setupCardBorderLayout(this._layout.groupFormat);
      } else {
        this.groupFormat = this._layout.groupFormat ? this.utilsService.mapperGroupFormat[this._layout.groupFormat] : undefined;
      }
    }

    this._setupFlexGrowState();
  }

  public get layout() {
    return this._layout;
  }

  private _setupFlexGrowState(){
    let flexGrowState = false;
    if(this._layout){
      if(this.groupFormat === SupportedGroupFormat.InlineGridDouble)
        flexGrowState = true;
    }

    if(flexGrowState && this._elementRef?.nativeElement)
      (this._elementRef.nativeElement as HTMLElement).style.flexGrow = "1";
  }

  private _setupAccordionLayout(){
    const testGroupFormat = this.utilsService.mapperGroupFormat[this._layout?.groupFormat];
    if(!testGroupFormat){
      const preRegexp = this._layout?.groupFormat?.includes('Accordion') ? 'Accordion' : 'ShowAfter';
      const genericRegExp = new RegExp(`${preRegexp}\\s*(?<type>\\S+?)$`);
      const regexExecution = genericRegExp.exec(this._layout?.groupFormat);
      if(regexExecution?.groups?.type){
        const type = regexExecution?.groups?.type;
        this.variableAccordionShowAfterValue = type;
        const groupFormat = this._layout?.groupFormat?.replace(type, 'N').replace(' ', '');
        this.groupFormat = this.utilsService.mapperGroupFormat[groupFormat];
      }
    }else this.groupFormat = testGroupFormat;
  }

  private _setupBoxWebBorderGray(groupFormat: string) {
    const trimmedString = groupFormat.toLowerCase().replace(' ', '');
    const borderSize = trimmedString.substring(trimmedString.length - 1);
    //Calcoliamo la borderSize
    if (!isNaN(parseInt(borderSize)))
      this.boxWebBorderGraySize = `${borderSize}px`;
    this.groupFormat = this.utilsService.mapperGroupFormat['BoxWebBorderGray'];
  }


  private _setupCardBorderLayout(groupFormat: string) {
    const direction = groupFormat.substring('cardborder'.length).toLowerCase();
    if (
      direction === 'top' ||
      direction === 'bottom' ||
      direction === 'left' ||
      direction === 'right'
    ) {
      this.groupFormat = this.supportedGroup.CardBorderDir;
      this.cardBorderDirection = direction;
    }
  }
}
