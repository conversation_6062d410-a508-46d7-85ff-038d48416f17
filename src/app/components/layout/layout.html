<div
  *ngIf="layout && layout.visible || layout && groupFormat === supportedGroup.dynamic"
  class="w-100"
>
  <!-- <h2 *ngIf="layout.title">{{layout.title}}</h2> -->

  <ng-container [ngSwitch]="groupFormat">
    <div *ngSwitchCase="supportedGroup.stacked" class="stacked-layout">
      <groups
        [customAttributes]="customAttributes"
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.default" class="default">
      <div class="sub-layout">
        <groups
          [customAttributes]="customAttributes"
          [groups]="layout.groups"
          [rows]="layout.rows??[]"
        ></groups>
      </div>
    </div>

    <div
      *ngSwitchCase="supportedGroup.tooltipCard"
      class="tooltip-card-container">
      <groups
        style="flex-grow: 1"
        [groups]="layout.groups"
        [groupClass]="gClass.tooltipCard"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.defaultMW496" class="defaultMW496">
      <div class="sub-layout">
        <groups
          [customAttributes]="customAttributes"
          [groups]="layout.groups"
          [rows]="layout.rows??[]"
        ></groups>
      </div>
    </div>

    <div *ngSwitchCase="supportedGroup.defaultMW528" class="defaultMW528">
      <div class="sub-layout">
        <groups
          [customAttributes]="customAttributes"
          [groups]="layout.groups"
          [rows]="layout.rows??[]"
        ></groups>
      </div>
    </div>

    <div *ngSwitchCase="supportedGroup.defaultBgLight" class="default-bg-light">
      <div class="sub-layout">
        <groups
          [customAttributes]="customAttributes"
          [groups]="layout.groups"
          [rows]="layout.rows??[]"
        ></groups>
      </div>
    </div>

    <div
      *ngSwitchCase="supportedGroup.inlineMiddle"
      class="inlineMiddle-layout"
    >
      <groups [groups]="layout.groups" [rows]="layout.rows??[]"></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.inlineMiddleAlignC">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.inlineMiddleAlignC"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.inlineGridDoubleResponsive">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.inlineDoubleGridLayout"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.inlineGridTriple">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.inlineTripleGridLayout"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.tooltip">
      <tooltip-dx-api [data]="tooltip"></tooltip-dx-api>
    </div>

    <div
      *ngSwitchCase="[supportedGroup.TooltipContentLD,
                      supportedGroup.TooltipContentLU,
                      supportedGroup.TooltipContentRD,
                      supportedGroup.TooltipContentRU ].includes(groupFormat) ? groupFormat: !groupFormat"
    >
      <groups [groups]="layout.groups" [rows]="layout.rows??[]"></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.inlineGridXY">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.inlineGridXY"
        [columnsWidth]="columnsWidth"
      ></groups>
    </div>

    <!-- <div *ngSwitchCase="supportedGroup.card" class="card">
      <groups
        [customAttributes]="customAttributes" [groups]="layout.groups" [rows]="layout.rows??[]"></groups>
    </div> -->

    <div *ngSwitchCase="supportedGroup.cardWithBg" class="card-azzurra">
      <groups
        [customAttributes]="customAttributes"
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.cardBox" class="card-box">
      <groups
        [customAttributes]="customAttributes"
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.cardForm" class="card-form">
      <div class="sub-layout">
        <groups
          [customAttributes]="customAttributes"
          [groups]="layout.groups"
          [rows]="layout.rows??[]"
        ></groups>
      </div>
    </div>

    <div *ngSwitchCase="supportedGroup.inlineGridSpaceBetween">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.inlineSpaceBetween"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.showAfterN" class="stacked-layout">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        *ngIf="flagShowContent$ | async"
      ></groups>
    </div>

    <div
      id="customComponentLayout"
      *ngSwitchCase="supportedGroup.customComponent"
    >
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [isCustomComponentLayout]="true"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.dynamic">
      <tpd-rows
        *ngIf="this.layout && this.layout.rows"
        [rows]="layout.rows"
      ></tpd-rows>
    </div>

    <div *ngSwitchCase="supportedGroup.ResponsiveTwocol">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.responsive2col"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.ResponsiveTwocolPU">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.responsive2colPU"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.ResponsiveTwocol1fr2fr">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.responsive2col1fr2fr"></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.ResponsiveTwocol2fr1fr">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.responsive2col2fr1fr"></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.ResponsiveTwocolPU8">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.responsive2col8"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.mimicASentence">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.mimicASentence"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.mimicASentenceCenter">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.mimicASentenceCenter"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.mimicASentenceRight">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.mimicASentenceRight"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.accordionN">
      <tpd-accordion
        [dataAccordion]="{accordionType: this.variableAccordionShowAfterValue, groups: this.layout.groups, title: this.layout.title}">
      </tpd-accordion>
    </div>

    <div *ngSwitchCase="supportedGroup.accordionHeader">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.mimicASentence"
      ></groups>
    </div>

    <div
      *ngSwitchCase="supportedGroup.accordionOpened"
      class="inlineMiddle-layout w-100"
    >
      <groups [groups]="layout.groups" [rows]="layout.rows??[]"></groups>
    </div>

    <div
      *ngSwitchCase="supportedGroup.accordionClosed"
      class="inlineMiddle-layout w-100"
    >
      <groups [groups]="layout.groups" [rows]="layout.rows??[]"></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.promoSection" class="promo-section">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.mimicASentence"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.BoxHighlight" class="box-highligth">
      <div class="sub-layout">
        <groups
          [customAttributes]="customAttributes"
          [groups]="layout.groups"
          [rows]="layout.rows??[]"
        ></groups>
      </div>
    </div>

    <div *ngSwitchCase="supportedGroup.ResponsiveTwocolLarge" class="w-100">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.responsive2colLarge"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.marginLeftS" class="margin-Left-S">
      <groups
        [customAttributes]="customAttributes"
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.web1colSwapApp2col">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.web1colSwapApp2col"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.ResponsiveTwocolSBCenter">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.responsiveTwocolSBCenter"
      ></groups>
    </div>

    <div
      *ngSwitchCase="supportedGroup.modalContent"
      class="modal-content-layout"
    >
      <groups [groups]="layout.groups" [rows]="layout.rows??[]"></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.modalHeader">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.modalHeader"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.modalHeaderBg">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.modalHeaderBg"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.formContainer" class="stacked-layout">
      <tpd-form-container
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
      ></tpd-form-container>
    </div>

    <div *ngSwitchCase="supportedGroup.cardBorderless" class="card-borderless">
      <div class="sub-layout">
        <groups
          [customAttributes]="customAttributes"
          [groups]="layout.groups"
          [rows]="layout.rows??[]"
        ></groups>
      </div>
    </div>

    <div *ngSwitchCase="supportedGroup.rowCenteredY">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.rowCenteredY"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.inlineTop">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.inlineTop"
      ></groups>
    </div>

    <div *ngSwitchDefault class="stacked-layout">
      <groups
        [customAttributes]="customAttributes"
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.inlineWeb">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.inlineWeb"
      ></groups>
    </div>

    <div
      *ngSwitchCase="supportedGroup.footerButtonLayout"
      class="footerButton-layout"
    >
      <groups [groups]="layout.groups" [rows]="layout.rows??[]"></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.responsive2RS1RL">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.responsive2RS1RL"
      ></groups>
    </div>

    <div
      *ngSwitchCase="supportedGroup.adviceHighlight"
      class="advice-highlight"
    >
      <div class="sub-layout">
        <groups
          [customAttributes]="customAttributes"
          [groups]="layout.groups"
          [rows]="layout.rows??[]"
        ></groups>
      </div>
    </div>

    <div *ngSwitchCase="supportedGroup.adviceBox" class="Advice-box">
      <groups
        [customAttributes]="customAttributes"
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
      ></groups>
    </div>

    <groups
      *ngSwitchCase="supportedGroup.roundedCard"
      [groups]="layout.groups"
      [rows]="layout.rows??[]"
      [groupClass]="gClass.roundedCard">
    </groups>

    <div *ngSwitchCase="supportedGroup.inlineAlignC">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.inlineAlignC"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.inputTooltipContainer">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.inputTooltipContainer"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.BoxInfo" class="box-info-text-layout">
      <groups
        [customAttributes]="customAttributes"
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.RightWebLeftApp" class="left-layout">
      <div class="sub-left-layout">
        <groups
          [customAttributes]="customAttributes"
          [groups]="layout.groups"
          [rows]="layout.rows??[]"
        ></groups>
      </div>
    </div>

    <box-privacy-layout
      *ngSwitchCase="supportedGroup.BoxPrivacy"
      [group]="{layout: this.layout}">
    </box-privacy-layout>

    <div *ngSwitchCase="supportedGroup.Responsive3col">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.responsive3col"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.Responsive3col3FullWT">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.responsive3col3fullwt"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.Responsive4col">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.responsive4col"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.cardBorder" class="card-border">
      <div class="sub-layout">
        <groups
          [customAttributes]="customAttributes"
          [groups]="layout.groups"
          [rows]="layout.rows??[]"
        ></groups>
      </div>
    </div>

    <div *ngSwitchCase="supportedGroup.webBoxBorder" class="web-box-border">
      <div class="sub-layout">
        <groups
          [customAttributes]="customAttributes"
          [groups]="layout.groups"
          [rows]="layout.rows??[]"
        ></groups>
      </div>
    </div>

    <pu-padding-layout
      *ngSwitchCase="supportedGroup.PuPadding"
      [data]="{groups: this.layout.groups, groupFormat: this.layout.groupFormat}"
    ></pu-padding-layout>

    <div
      *ngSwitchCase="supportedGroup.BoxWebBorderGray"
      [style.border-width]="this.boxWebBorderGraySize"
      class="BoxWebBorderGrayLayout"
    >
      <groups [groups]="layout.groups" [rows]="layout.rows??[]"></groups>
    </div>

    <div
      *ngSwitchCase="supportedGroup.CardBorderDir"
      [class]="'CardBorderLayout ' + this.cardBorderDirection"
    >
      <groups [groups]="layout.groups" [rows]="layout.rows??[]"></groups>
    </div>

    <carousel-layout
      *ngSwitchCase="supportedGroup.CarouselLayout"
      [data]="this.layout"
    ></carousel-layout>

    <div class="DefaultBgGrey" *ngSwitchCase="supportedGroup.DefaultBgGrey">
      <groups
        [customAttributes]="customAttributes"
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
      ></groups>
    </div>

    <div
      class="DefaultBgGrey rounded"
      *ngSwitchCase="supportedGroup.RoundedCardBgGrey"
    >
      <groups
        [customAttributes]="customAttributes"
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
      ></groups>
    </div>

    <div class="CardBgWhite" *ngSwitchCase="supportedGroup.CardBgWhite">
      <groups
        [customAttributes]="customAttributes"
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
      ></groups>
    </div>

    <div
      *ngSwitchCase="supportedGroup.ResponsiveAColDRowTRowMCol"
      class="AColDRowTRowMCol">
      <groups [groups]="this.layout.groups" [rows]="this.layout.rows"></groups>
    </div>

    <div
      *ngSwitchCase="supportedGroup.ResponsiveAColDRowTRowMCol16"
      class="AColDRowTRowMCol gap16">
      <groups [groups]="this.layout.groups" [rows]="this.layout.rows"></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.SplittedRadio">
      <splitted-radio [groups]="this.layout.groups"></splitted-radio>
    </div>

    <div *ngSwitchCase="supportedGroup.row">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.row"
        [styleInstructions]="styleInstructions"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.col">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.col"
        [styleInstructions]="styleInstructions"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.card">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.card"
        [styleInstructions]="styleInstructions"
      ></groups>
    </div>

    <div *ngSwitchCase="supportedGroup.box">
      <groups
        [groups]="layout.groups"
        [rows]="layout.rows??[]"
        [groupClass]="gClass.box"
        [styleInstructions]="styleInstructions"
      ></groups>
    </div>

    <accordion-card-protezione
      *ngSwitchCase="this.supportedGroup.AccordionCardProtezione"
      [data]="this.layout.groups">
    </accordion-card-protezione>

    <groups
      *ngSwitchCase="this.supportedGroup.InlineGridDouble"
      [groups]="layout.groups"
      [groupClass]="gClass.InlineGridDouble"
    ></groups>

    <groups
      *ngSwitchCase="this.supportedGroup.InlineWithFirstElement"
      [groups]="layout.groups"
      [groupClass]="this.gClass.InlineWithFirstElement">
    </groups>

    <flat-card-layout
      *ngSwitchCase="this.supportedGroup.FlatCard"
      [groups]="this.layout.groups">
    </flat-card-layout>
  </ng-container>
</div>
