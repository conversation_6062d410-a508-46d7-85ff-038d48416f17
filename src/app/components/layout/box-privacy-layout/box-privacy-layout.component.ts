import { Component, Input, OnDestroy } from "@angular/core";
import { ICaption, IField, IGroup, IParagraph } from "../../../models/models.commons";
import RetrieverService from "../../../services/retriever.service";
import { TextCss, TextHandlerService } from "../../../services/text-handler.service";
import { ListenerErroreFormInterface, StatusService } from "../../../services/status.service";
import { FormGroup } from "@angular/forms";

@Component({
  selector: 'box-privacy-layout',
  templateUrl: './box-privacy-layout.component.html',
  styleUrls: ['./box-privacy-layout.component.scss']
})
export default class BoxPrivacyLayoutComponent implements OnDestroy{
  public accettaCheckbox: IField;
  public checkboxLabel: string;
  public checkboxLabelFormat: TextCss;

  public captionPreventivass: ICaption;
  public pxLinkPreventivass: IField;
  public clausola: IParagraph[] = [];
  public testoCentale: ICaption;
  public testoCheckbox: ICaption;

  public mostraErrore = false;

  private formIndex = -1;
  private errorListener: ListenerErroreFormInterface;

  constructor(
    private _retrieverService: RetrieverService,
    private _textHandlerService: TextHandlerService,
    private _statusService: StatusService
  ) {
    this.formIndex = this._statusService.currentStandAloneFormIndex;
    this.errorListener = this._statusService.registraListenerDiErroreInputInvalido(this.formIndex, () => this._errorListenerMethod())
  }

  @Input() set group(group: IGroup){
    this.accettaCheckbox = this._retrieverService.getFirstFieldInGroupsByType([group], 'pxCheckbox');
    if(this.accettaCheckbox){
      this.accettaCheckbox.showLabel = false;
      this.checkboxLabel = this._retrieverService.getControlValueFromField(this.accettaCheckbox, 'label');
      this.accettaCheckbox.control.label = "";
      const labelFormat = this._retrieverService.getValueFromField(this.accettaCheckbox, 'labelFormat');
      if(labelFormat.length !== 0)
        this.checkboxLabelFormat = this._textHandlerService.getTextCss(labelFormat);
    }

    const mimicASentenceLayout = this._retrieverService.getFirstLayoutInGroupsByGroupFormat([group], 'Mimic a sentence');
    if(mimicASentenceLayout){
      this.captionPreventivass = this._retrieverService.getFirstCaptionInGroups(mimicASentenceLayout.groups);
      this.pxLinkPreventivass = this._retrieverService.getFirstFieldInGroupsByType(mimicASentenceLayout.groups, 'pxLink');

      const paragraphClausola = this._retrieverService.getFirstParagraphInGroups(mimicASentenceLayout.groups);
      try{
        if(paragraphClausola){
          const valueParagraph = paragraphClausola.value;
          const realValue = valueParagraph.split('>')[1].split('<')[0];
          const style = valueParagraph.replace(realValue, 'DATA');

          const paragraphParts = realValue.split(' ');
          for(const paragraphPart of paragraphParts){
            const targetParagraph = JSON.parse(JSON.stringify(paragraphClausola)) as IParagraph;
            targetParagraph.value = `${style.split('DATA')[0]}${paragraphPart}${style.split('DATA')[1]}`;
            this.clausola.push(targetParagraph);
          }
        }
      }catch(e){
        this.clausola.push(paragraphClausola);
        console.error("Errore splitting del paragraph", e);
      }
    }
    this.testoCentale = this._retrieverService.getFirstCaptionByControlFormat([group], 'MiddleText');
    this.testoCheckbox = this._retrieverService.getFirstCaptionByControlFormat([group], 'CheckboxText');
  }

  private _errorListenerMethod(){
    const formGroup = (this.formIndex >= 0 ? this._statusService.getFormArrayChildByIndex(this.formIndex) : this._statusService.getFormGroup()) as FormGroup;
    if(formGroup){
      const control = formGroup.controls[this._retrieverService.getFieldReference(this.accettaCheckbox)];
      if(control)
        this.mostraErrore = control.status === "INVALID";
    }
  }

  public ngOnDestroy(): void {
    this.errorListener && this.errorListener.rimuoviListenerDiErroreInputInvalido();
  }
}
