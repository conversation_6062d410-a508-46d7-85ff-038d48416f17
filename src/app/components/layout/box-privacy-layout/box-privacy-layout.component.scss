@import "../../../../styles.scss";

.BoxPrivacyLayoutContainer{
  display: flex;
  flex-direction: column;
  width: 100%;
  border: 2px solid $ivory;
  gap: 16px;
  padding: 16px;
  margin: 0 auto;

  @media #{$bkp_mobile} {
    max-width: $width_container_mobile;
  }
  @media #{$bkp_tablet} {
    max-width: $width_container_tablet;
  }
  @media #{$bkp_desktop} {
    max-width: $width_container_desktop;
  }

  >.BoxPivacyContentContainer{
    display: flex;
    flex-direction: row;
    column-gap: 4px;
    row-gap: 0;
    align-items: center;
    flex-wrap: wrap;
  }

  >.BoxPrivacyCheckbox{
    display: flex;
    flex-direction: row;
    gap: 16px;
  }

  >.TestoCentrale{
    margin-top: 10px;
  }

  >.ZonaErrore{
    display: flex;
    flex-direction: row;
    align-items: center;
    color: $alert-color;

    font-family: 'Unipol Medium';
    font-size: 14px;
    line-height: 0.88;

    >.icon-Attenzione-pieno{
      font-size: 20px !important;
    }
  }
}
