<div class="BoxPrivacyLayoutContainer">
  <span
    *ngIf="this.captionPreventivass || this.pxLinkPreventivass || this.clausola.length > 0"
    class="BoxPivacyContentContainer">
    <caption-elem *ngIf="this.captionPreventivass" [caption]="this.captionPreventivass"></caption-elem>
    <field *ngIf="this.pxLinkPreventivass" [data]="{field: this.pxLinkPreventivass}"></field>
    <ng-container *ngIf="this.clausola.length > 0">
      <paragraph *ngFor="let clausola of this.clausola" [paragraph]="clausola"></paragraph>
    </ng-container>
  </span>
  <span *ngIf="this.testoCentale" class="TestoCentrale">
    <caption-elem [caption]="this.testoCentale"></caption-elem>
  </span>
  <span class="BoxPrivacyCheckbox">
    <field *ngIf="this.accettaCheckbox" [data]="{field: this.accettaCheckbox}"></field>
    <custom-text-style
      *ngIf="this.checkboxLabel"
      [content]="checkboxLabel"
      [textCss]="checkboxLabelFormat"></custom-text-style>
    <caption-elem
      *ngIf="this.testoCheckbox"
      [caption]="this.testoCheckbox">
    </caption-elem>
  </span>

  <span *ngIf="this.mostraErrore" class="ZonaErrore">
    <span class="icon-Attenzione-pieno"></span>Campo obbligatorio
  </span>
</div>
