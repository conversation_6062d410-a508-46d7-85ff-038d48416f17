import {
  Component,
  ElementRef,
  Input,
  ViewChild,
  On<PERSON><PERSON>roy,
} from '@angular/core';
import { IGroup } from '../../../models/models.commons';

export interface PuPaddingLayoutInputInterface {
  groups?: IGroup[];
  groupFormat?: string;
}

@Component({
  selector: 'pu-padding-layout',
  templateUrl: './PuPaddingLayout.html',
  styleUrls: ['./PuPaddingLayout.scss'],
})
export default class PuPaddingLayout {
  private static _PuPaddingGRegex = '_ (?<paddingData>[\\d{1,5}\\s*]+)'; //Regex generica per l'identificazione della taglia

  private _groups: IGroup[] = [];
  private _groupFormat: string;
  private _containerLayout: HTMLDivElement;
  private _paddingInizializzato = false;

  public static CreateRegex(findCharacter: 'P' | 'D' | 'T' | 'M' = 'P'): RegExp {
    const regexString = PuPaddingLayout._PuPaddingGRegex.replace(
      '_',
      findCharacter[0].toUpperCase()
    );
    return new RegExp(regexString, 'gi');
  }

  private _decodePaddingLine(groupFormat: string, taglia: 'D' | 'T' | 'M'): string {
    let esito = '0';
    if (groupFormat && taglia) {
      const regex = PuPaddingLayout.CreateRegex(taglia);
      if (regex.test(groupFormat)) {
        groupFormat.match(regex);
        const match = regex.exec(groupFormat);
        esito = match && match[1] ? match[1].trim() : '0';
      }
    }

    return esito;
  }

  private _puPaddingToCssPadding(paddingLine: string): string {
    let esito = paddingLine;

    const parti = paddingLine.split(' ');
    switch (parti.length) {
      case 1:
        esito = `${parti[0]}px`
        break;
      case 2:
        esito = `${parti[1]}px ${parti[0]}px`;
        break;
      case 4:
        esito = `${parti[1]}px ${parti[2]}px ${parti[3]}px ${parti[0]}px`;
        break;
    }
    return esito;
  }

  private _setPadding() {
    const paddingDesktop = this._puPaddingToCssPadding( this._decodePaddingLine(this._groupFormat, 'D') );
    const paddingTablet = this._puPaddingToCssPadding( this._decodePaddingLine(this._groupFormat, 'T') );
    const paddingMobile = this._puPaddingToCssPadding( this._decodePaddingLine(this._groupFormat, 'M') );

    if (this._containerLayout) {
      this._containerLayout.style.setProperty('--paddingDesktop', paddingDesktop);
      this._containerLayout.style.setProperty('--paddingTablet', paddingTablet);
      this._containerLayout.style.setProperty('--paddingMobile', paddingMobile);
    }
  }

  @Input()
  public set data(data: PuPaddingLayoutInputInterface) {
    if (data && data.groups && data.groupFormat) {
      this._groups = data.groups;
      this._groupFormat = data.groupFormat;
      if(this._containerLayout && this._groupFormat && !this._paddingInizializzato) {
        this._setPadding();
        this._paddingInizializzato = true;
      }
    }
  }

  @ViewChild('puPaddingDiv') set paddingDiv(containerLayout: ElementRef<HTMLDivElement>){
    this._containerLayout = containerLayout.nativeElement;
    if(this._containerLayout && this._groupFormat && !this._paddingInizializzato) {
      this._setPadding();
      this._paddingInizializzato = true;
    }
  }

  @Input() isCustomComponent = false;

  get groups(): IGroup[] {
    return this._groups;
  }

  get groupFormat(): string {
    return this._groupFormat;
  }
}
