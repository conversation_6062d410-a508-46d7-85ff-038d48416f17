import { Component, Input } from "@angular/core";
import { IField, IGroup, ILayout } from "../../../models/models.commons";
import RetrieverService from "../../../services/retriever.service";
import { IDataInputField } from "../../field/field";
import { TextCss, TextHandlerService } from "../../../services/text-handler.service";

@Component({
  selector: 'accordion-card-protezione',
  templateUrl: './accordion-card-protezione.component.html',
  styleUrls: ['./accordion-card-protezione.component.scss']
})
export default class AccordionCardProtezioneComponent{
  private _protezioni: ILayout[];
  private _button: IField;

  public protezioniVisualizzate: IDataInputField[] = [];

  private vediDiPiuTesto = '';
  private vediDiMenoTesto = '';
  private massimiElementiVisualizzati = 1;

  public stileTestoCta: TextCss;

  public isOpen = false;

  constructor(
    private _retrieverService: RetrieverService,
    private _textHandlerService: TextHandlerService
  ) {
  }

  @Input() set data(groups: IGroup[]){
    this._protezioni = this._retrieverService.getAllCustomComponentsInGroupsByComponentName(groups, 'CardProtezioneDettaglioGaranzia');
    this._button = this._retrieverService.getFirstFieldInGroupsByType(groups, 'pxButton');

    if(this._button){
      this.vediDiPiuTesto = this._retrieverService.getCustomAttributeFromField(this._button, 'textShowMore', 'Vedi di più');
      this.vediDiMenoTesto = this._retrieverService.getCustomAttributeFromField(this._button, 'textShowLess', 'Vedi di meno');
      this.massimiElementiVisualizzati = this._retrieverService.getCustomAttributeFromFieldAsNumber(this._button, 'maxVisibleElementWEB', 3);
      this.stileTestoCta = this._textHandlerService.getTextCss(this._button.labelFormat);
    }

    this.protezioniVisualizzate = this._protezioniVisualizzate;
  }

  private get _protezioniVisualizzate(): IDataInputField[]{
    const protezioniFiltrate = this._protezioni.filter((layout, index) => this.isOpen ? true : index < this.massimiElementiVisualizzati);
    const protezioniMappate = protezioniFiltrate.map(protezione => ({field: protezione.groups[0].field, groups: protezione.groups}))

    return protezioniMappate;
  }

  public toggleAccordionOpen(){
    this.isOpen = !this.isOpen;
    this.protezioniVisualizzate = this._protezioniVisualizzate;
  }

  public get testoPulsante(): string{
    return this.isOpen ? this.vediDiMenoTesto : this.vediDiPiuTesto;
  }
}
