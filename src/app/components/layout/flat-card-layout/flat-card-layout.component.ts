import { Component, Input } from "@angular/core";
import { IGroup } from "../../../models/models.commons";
import RetrieverService from "../../../services/retriever.service";
import { TextCss, TextHandlerService } from "../../../services/text-handler.service";

@Component({
  templateUrl: './flat-card-layout.component.html',
  styleUrls: ['./flat-card-layout-component.scss'],
  selector: 'flat-card-layout'
})
export default class FlatCardLayoutComponent{
  public stileNomeAgenzia: TextCss;
  public nomeAgenzia: string;
  public stileIndirizzoAgenzia: TextCss;
  public indirizzoAgenzia: string;

  constructor(
    private _retrieverService: RetrieverService,
    private _textHandlerService: TextHandlerService
  ) {
    this.stileNomeAgenzia = this._textHandlerService.getTextCss('TEXT APP WHB16 WEB WHB16 WHB16 WHB16');
    this.stileIndirizzoAgenzia = this._textHandlerService.getTextCss('TEXT APP WHB16 WEB WHM13 WHM13 WHM13');
  }

  @Input() set groups(groups: IGroup[]){
    this.nomeAgenzia = this._retrieverService.captionToValue(this._retrieverService.getFirstCaptionByControlFormat(groups, 'NomeAgenzia')).value;
    this.indirizzoAgenzia = this._retrieverService.captionToValue(this._retrieverService.getFirstCaptionByControlFormat(groups, 'IndirizzoAgenzia')).value;
  }
}
