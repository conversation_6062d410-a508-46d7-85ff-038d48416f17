@import "../../../../styles.scss";

.FlatCardLayoutContainer{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 10px;
  padding: 16px;
  align-items: center;
  background-color: $blue-primary;
  margin: 32px auto;
  max-width: 528px;


  >.FlatCardLayoutContent{
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  >.FlatCardLayoutCheck{
    aspect-ratio: 1 / 1;
    display: block;
    position: relative;
    width: 20px;
    background-color: $check-green-color;
    border-radius: 50%;
    cursor: pointer;
    flex-shrink: 0;

    &::after{
      content: "";
      aspect-ratio: 3 / 2;
      width: 40%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateX(-50%) translateY(-55%) rotateZ(-45deg);
      border-left: 3px solid white;
      border-bottom: 3px solid white;
    }
  }
}
