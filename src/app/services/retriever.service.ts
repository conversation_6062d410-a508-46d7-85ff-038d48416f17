import { Injectable } from "@angular/core";
import { ICaption, IField, IGroup, ILayout, IParagraph, IView } from "../models/models.commons";
import { TextCss, TextHandlerService } from "./text-handler.service";
import { UtilsService } from "./utils.service";

@Injectable({
  providedIn: 'root'
})
export default class RetrieverService{
  constructor(
    private _textService: TextHandlerService,
    private _utilsService: UtilsService
  ) {

  }

  private _retrieveAll(groups: IGroup[], condition: (group: IGroup) => boolean, clone = true): IGroup[]{
    const esito: IGroup[] = [];

    if(groups){
      for(const group of groups){
        if(condition(group))
          esito.push(clone ? JSON.parse(JSON.stringify(group)) : group);
        else if(group.layout) {
          esito.push(...this._retrieveAll(group.layout.groups, condition, clone));
          if(group.layout.rows){
            for(const row of group.layout.rows){
              esito.push(...this._retrieveAll(row.groups, condition, clone));
            }
          }
        }else if(group.view)
          esito.push(...this._retrieveAll(group.view.groups, condition, clone));
      }
    }

    return esito;
  }

  private _retrieveFirst(groups: IGroup[], condition: (group: IGroup) => boolean, clone = true): IGroup{
    let esito: IGroup = undefined;

    if(groups){
      for(const group of groups){
        if(condition(group)) {
          esito = clone ? JSON.parse(JSON.stringify(group)) : group;
        }else if(group.layout) {
          esito = this._retrieveFirst(group.layout.groups, condition, clone);
          if(!esito && group.layout.rows){
            for(const row of group.layout.rows){
              esito = this._retrieveFirst(row.groups, condition, clone);
              if(esito)
                break;
            }
          }
        }else if(group.view)
          esito = this._retrieveFirst(group.view.groups, condition, clone);

        if(esito)
          break;
      }
    }

    return esito;
  }

  public getFirstCaptionInGroups(groups: IGroup[]): ICaption{
    return this._retrieveFirst(groups, (group) => !!group.caption)?.caption;
  }

  public getFirstCaptionByControlFormat(groups: IGroup[], controlFormat: string, controllaVisibilita = false): ICaption{
    return this._retrieveFirst(groups, (group) => {
      let esito = false;
      const format = group?.caption?.control?.format;
      if(format) {
        esito = format.split("STYLE")[0].trim() === controlFormat;
        if(esito && controllaVisibilita)
          esito = group?.caption?.visible || false;
      }
      return esito;
    })?.caption;
  }

  public getFirstCaptionWithoutSpecificControlFormat(groups: IGroup[]): ICaption{
    return this._retrieveFirst(groups, (group) => {
      let esito = false;
      const format = group?.caption?.control?.format;
      if(format !== undefined)
        esito = ( !format.includes('STYLE') && format.includes('TEXT APP') ) || format.length === 0;
      return esito;
    })?.caption;
  }

  public getFirstCaptionInGroupsByValue(groups: IGroup[], value: string): ICaption{
    return this._retrieveFirst(groups, (group) => group?.caption?.value === value)?.caption;
  }

  public getFirstCaptionInGroupsByValueNotCloned(groups: IGroup[], value: string): ICaption{
    return this._retrieveFirst(groups, (group) => group?.caption?.value === value, false)?.caption;
  }

  public getEachCaptionInGroups(groups: IGroup[]): ICaption[]{
    return this._retrieveAll(groups, (group) => !!group.caption)?.map(group => group.caption);
  }

  public getFirstParagraphInGroups(groups: IGroup[]): IParagraph{
    return this._retrieveFirst(groups, (group) => !!group.paragraph)?.paragraph;
  }

  public getFirstParagraphByControlFormat(groups: IGroup[], controlFormat: string): IParagraph{
    return this._retrieveFirst(groups, (group) => {
      let esito = false;
      const value = group?.paragraph?.value;
      if(value)
        esito = value.includes('data-pega-style') && value.includes('STYLE TEXT') && value.includes(controlFormat);
      return esito;
    })?.paragraph;
  }

  public getEachVisibleFiledInGroups(groups: IGroup[]): IField[]{
    return this._retrieveAll(groups, (group) => group?.field?.visible).map(group => group.field);
  }

  public getFirstFieldInGroupsByType(groups: IGroup[], fieldType: string, visible?: boolean): IField{
    return this._retrieveFirst(groups, (group) => {
      let esito = group?.field?.control?.type === fieldType;
      if(visible !== undefined)
        esito &&= group?.field?.visible === visible
      return esito;
    })?.field;
  }

  public getFirstFieldInGroupsByComponentPurpose(groups: IGroup[], componentPurpose: string, visible?: boolean): IField{
    return this._retrieveFirst(groups, (group) => {
      let esito = group?.field?.customAttributes?.ComponentPurpose === componentPurpose;
      if(visible !== undefined)
        esito &&= group?.field?.visible === visible
      return esito;
    })?.field;
  }

  public getFirstFieldInGroupsByComponentId(groups: IGroup[], componentId: string): IField{
    return this._retrieveFirst(groups, (group) => group?.field?.customAttributes?.componentID === componentId)?.field;
  }

  public getAllFieldInGroupsNotCloned(groups: IGroup[]): IField[]{
    return this._retrieveAll(groups, (group) => !!group?.field, false).map(group => group.field);
  }

  public getFirstFieldInGroupsByCustomAttributeValue(groups: IGroup[], fieldType: string, customAttributeName: string, customAttributeValue): IField{
    return this._retrieveFirst(groups, (group) => {
      let esito = false;

      if(group?.field?.control?.type === fieldType){
        esito = group?.field?.customAttributes[customAttributeName] === customAttributeValue;
      }

      return esito;
    })?.field;
  }

  public getFirstFieldInGroupsByFieldId(groups: IGroup[], fieldType: string, fieldId: string): IField{
    return this._retrieveFirst(groups, (group) => {
      let esito = false;

      if(group?.field?.control?.type === fieldType){
        esito = group?.field?.fieldID === fieldId;
      }

      return esito;
    })?.field;
  }

  public getLeastButtonInGroupsByLabelNotCloned(groups: IGroup[], labelValue: string): IField{
    const all = this._retrieveAll(groups, (group) => group?.field?.control?.type === 'pxButton' && group?.field?.control.label === labelValue, false);
    return all.length > 0 ?
      all[all.length - 1]?.field : undefined;
  }

  public getFirstIconInGroupsByResource(groups: IGroup[], resourceName: string): IField{
    return this._retrieveFirst(groups, (group) => group?.field?.control?.type === 'pxIcon' && group?.field?.customAttributes?.resource === resourceName)?.field
  }

  public getFirstLayoutInGroups(groups: IGroup[]): ILayout{
    return this._retrieveFirst(groups, (group) => !!group?.layout)?.layout;
  }

  public getFirstLayoutInGroupsByGroupFormat(groups: IGroup[], groupFormat: string): ILayout{
    return this._retrieveFirst(groups, (group) => group?.layout?.groupFormat === groupFormat)?.layout;
  }

  public getFirstLayoutInGroupsByGroupFormatNotCloned(groups: IGroup[], groupFormat: string): ILayout{
    return this._retrieveFirst(groups, (group) => group?.layout?.groupFormat === groupFormat, false)?.layout;
  }

  public getFirstLayoutInGroupsByGroupFormatContain(groups: IGroup[], contain: string): ILayout{
    return this._retrieveFirst(groups, (group) => group?.layout?.groupFormat?.toLowerCase()?.includes(contain?.toLowerCase()))?.layout;
  }

  public getAllLayoutInGroupsByGroupFormat(groups: IGroup[], groupFormat: string): ILayout[]{
    return this._retrieveAll(groups, (group) => group?.layout?.groupFormat === groupFormat).map(group => group.layout);
  }

  public getAllCustomComponentsInGroupsByComponentName(groups: IGroup[], componentName: string): ILayout[]{
    return this._retrieveAll(groups, (group) => {
      let esito = false;

      if(group.layout && group.layout.groupFormat === 'CustomComponent'){
        const pxHidden = group.layout.groups.find(group => group.field && group.field?.control?.type === 'pxHidden');
        esito = pxHidden?.field?.customAttributes?.customType === componentName;
      }

      return esito;
    }).map(group => group.layout);
  }

  public getFirstCustomComponentsInGroupsByComponentName(groups: IGroup[], componentName: string): ILayout{
    return this._retrieveFirst(groups, (group) => {
      let esito = false;

      if(group.layout && group.layout.groupFormat === 'CustomComponent'){
        const pxHidden = group.layout.groups.find(group => group.field && group.field?.control?.type === 'pxHidden');
        esito = pxHidden?.field?.customAttributes?.customType === componentName;
      }

      return esito;
    })?.layout;
  }

  public getFirstViewInGroupsByViewId(groups: IGroup[], viewId: string): IView{
    return this._retrieveFirst(groups, (group) => group?.view?.viewID === viewId)?.view;
  }

  //Retriever di componente

  public getFieldReference(field: IField): string{
    return field?.reference;
  }

  public getCustomAttributeFromField(field: IField, attributeName: string, defaultValue = ''): string{
    return field?.customAttributes[attributeName] ?? defaultValue;
  }

  public getCustomAttributeFromFieldAsNumber(field: IField, attributeName: string, defaultValue = 0): number{
    let esito = defaultValue;
    const data = this.getCustomAttributeFromField(field, attributeName);
    if(data.length !== 0 && !isNaN(parseFloat(data))){
      esito = parseFloat(data)
    }

    return esito;
  }

  public getCustomAttributeFromFieldUnsensitive(field: IField, attributeName: string, defaultValue = ''): string{
    let esito = defaultValue;
    if(field.customAttributes){
      for(const key of Object.keys(field.customAttributes)){
        if(key.toLowerCase() === attributeName.toLowerCase()) {
          esito = field.customAttributes[key];
          break;
        }
      }
    }
    return esito;
  }

  public getCustomAttributeFromFieldAsNumberUnsensitive(field: IField, attributeName: string, defaultValue = 0): number{
    let esito = defaultValue;
    const data = this.getCustomAttributeFromFieldUnsensitive(field, attributeName);
    if(data.length !== 0 && !isNaN(parseFloat(data))){
      esito = parseFloat(data)
    }

    return esito;
  }

  public getControlValueFromField(field: IField, controlName: string, defaultValue = ''): string{
    return field?.control[controlName] ?? defaultValue;
  }

  public getValueFromField(field: IField, fieldName: string, defultValue = ''): string{
    return field[fieldName] ?? defultValue;
  }

  public captionToValue(caption: ICaption): {value?: string, textCss?: TextCss}{
    const esito = {};

    if(caption && caption.visible){
      esito['value'] = this._utilsService.htmlDecode(caption?.value);
      const textCss = this._textService.getTextCss(caption.control.format);
      if(textCss)
        esito['textCss'] = textCss;
    }

    return esito;
  }

  public fieldToValue(field: IField): string{
    let esito = '';

    if(field && field.visible)
      esito = field.value;

    return esito;
  }
}
