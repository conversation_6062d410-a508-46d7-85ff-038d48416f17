import { Injectable } from "@angular/core";

@Injectable({
  providedIn: 'root'
})
export default class ComponentReferenceStorageService{
  private _storageMap: Map<string, unknown> = new Map();
  private _onSetComponentCallbacks: Map<string, ((element: unknown) => void)[]> = new Map();

  public storeComponent<T>(id: string, component: unknown){
    if(this._storageMap.has(id))
      this._storageMap.delete(id);
    this._storageMap.set(id, component);

    if(this._onSetComponentCallbacks.has(id)){
      for(const callback of this._onSetComponentCallbacks.get(id))
        callback(component);
    }
  }

  public getStoredComponent<T>(id: string): T | undefined{
    let esito = undefined;

    if(this._storageMap.has(id))
      esito = this._storageMap.get(id);

    return esito;
  }

  public addOnSetComponentListener<T>(componentId: string, callback: (element: T) => void){
    if(!this._onSetComponentCallbacks.has(componentId))
      this._onSetComponentCallbacks.set(componentId, []);
    this._onSetComponentCallbacks.get(componentId).push(callback);

    if(this._storageMap.has(componentId))
      callback && callback(this._storageMap.get(componentId) as T);
  }
}
