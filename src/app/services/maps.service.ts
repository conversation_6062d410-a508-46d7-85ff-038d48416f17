import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { CommonNetworkService } from '@tpd-web-common-libs/nodejs-library';
import { catchError, map, of } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export default class MapsService {
  private static _ApiLoaded = false;
  private _commonNetworkService = new CommonNetworkService();

  private _mock = ''; //Togliere sempre la chiave mock

  public constructor(private _httpClient: HttpClient) {
    /*DO-NOTHING*/
  }

  /**
   * Effettua il caricamento della api
   */
  public async loadService(): Promise<boolean> {
    let esito = false;

    if (typeof window === 'object') {
      if (!MapsService._ApiLoaded) {
        try {
          if (this._mock) esito = await this._loadApi(this._mock);
          else {
            const networkConfigResponse =
              await this._commonNetworkService.getNetworkConfig();
            if (networkConfigResponse) {
              const networkConfig = await networkConfigResponse.json();
              if (networkConfig && networkConfig['apiKey'])
                esito = await this._loadApi(networkConfig['apiKey']);
            }
          }

          if (esito) MapsService._ApiLoaded = true;
        } catch (exception) {
          console.error(
            'MapService: Errore nel caricamento del servizio di google'
          );
        }
      }
    }

    return esito;
  }

  /**
   * Effettua il caricamento della api
   * @param key Chiave di accesso alla api
   * @private
   */
  private _loadApi(key: string): Promise<boolean> {
    console.warn('MapsService: Loading google api');
    return new Promise((resolve) => {
      this._httpClient
        .jsonp(
          `https://maps.googleapis.com/maps/api/js?key=${key}&libraries=places`,
          'callback'
        )
        .pipe(
          map(() => {
            console.warn('Api loaded is true');
            MapsService._ApiLoaded = true;
            return true;
          }),
          catchError(() => of(false))
        )
        .subscribe((next) => {
          resolve(next);
        });
    });
  }
}
