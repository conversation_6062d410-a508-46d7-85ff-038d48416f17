import { Injectable } from '@angular/core';
import {
  AnalyticsBridge,
  CommonFunctions,
  LocalStorageLayer,
  AnalyticsPublisher,
} from '@tpd-web-angular-libs/angular-library';
import { LoginHelper } from '@tpd-web-common-libs/nodejs-library/dist/src/helpers';
import { isArray } from 'lodash';
import { DxCategory } from '../models/config-interprete';

@Injectable({ providedIn: 'root' })
export class AnalyticsInterprete {
  constructor(
    private commonFunctions: CommonFunctions,
    private localStorageLayer: LocalStorageLayer,
    private analyticsBridge: AnalyticsBridge,
    private analyticsPublisher: AnalyticsPublisher
  ) {}
  private part = true;
  private _analyticsPEGAContainer: any = {};
  private _analyticsMapper: any = {};
  private _userContext: any = {};
  private _analyticsPopup: any = {};
  private _analyticsPegaPopup: any = {};
  private _analyticsContainer: any = {};

  findElement(structure: any) {
    // FIND ELEMENT PER ANALYTICS
    if (isArray(structure))
      structure.forEach((element) => this.findElement(element));
    else if (structure) {
      const groups = structure.groups;
      const layout = structure.layout;
      const view = structure.view;
      const rows = structure.rows;
      const field = structure.field;

      if (groups) this.findElement(groups);
      else if (layout || view) {
        const element = layout || view;
        if (layout && layout?.layoutFormat === 'REPEATINGLAYOUT')
          this.findElement(element.rows);
        else this.findElement(element.groups);
      } else if (rows) this.findElement(rows);
      else if (
        field &&
        field.control &&
        field.control.type === 'pxHidden' &&
        field.customAttributes &&
        (field.customAttributes?.customType === 'analyticsPageFields' ||
          field.customAttributes?.customType === 'analyticsPageFieldSpecific')
      ) {
        delete field.customAttributes['customType'];
        this._analyticsPEGAContainer = {
          ...this._analyticsPEGAContainer,
          ...field.customAttributes,
        };
      } else if (
        field &&
        field.control &&
        field.control.type === 'pxHidden' &&
        field.customAttributes &&
        field.customAttributes?.customType === 'analyticsPopupFields'
      ) {
        delete field.customAttributes['customType'];
        this._analyticsPegaPopup = {
          ...this._analyticsPegaPopup,
          ...field.customAttributes,
        };
      }
    }
  }

  createBodyAnalytics(
    pegaBodyResponse: any,
    analyticsBffResponse?: any,
    dXModule?: ModuloAnalyticsDx,
    productModule?: any
  ) {
    this.findElement(pegaBodyResponse);

    if (dXModule) {
      this._analyticsMapper = {
        ...this._analyticsMapper,
        ...dXModule,
        funnel_type: dXModule.proposal_dx_flow
          ? dXModule.proposal_dx_flow === 'richiesta'
            ? 'nuovo'
            : 'modifica'
          : '',
      };
    }
    if (productModule) {
      this._analyticsMapper = { ...this._analyticsMapper, ...productModule };
    }
    if (analyticsBffResponse) {
      this._analyticsMapper = {
        ...this._analyticsMapper,
        ...this._analyticsPEGAContainer,
        ...analyticsBffResponse,
      };
    } else {
      this._analyticsMapper = {
        ...this._analyticsMapper,
        ...this._analyticsPEGAContainer,
      };
    }

    // console.log('analyticsContainer', this._analyticsMapper)
  }

  async sendAnalytics() {
    const baseViewObject = this.commonFunctions.buildPortalObject();
    console.log('track data', baseViewObject);

    this._userContext = await this.getUserDataForAnalytics();

    if (this.part) {
      this.analyticsPublisher.pageViewPart({
        ...baseViewObject,
        ...this._analyticsMapper,
        ...this._userContext,
      }); //tracciamento che parte in avvio di pagina
      this.part = false;
    } else {
      this.analyticsPublisher.pageView({
        ...baseViewObject,
        ...this._analyticsMapper,
        ...this._userContext,
      }); //tracciamento che parte nel momento in cui viene invocato
    }
    this._analyticsContainer = {
      ...baseViewObject,
      ...this._analyticsMapper,
      ...this._userContext,
    };
    console.log('analyticsContainer', this._analyticsContainer);
  }

  sendAnalyticsAction(payload: any) {
    const otherProperty = {
      virtual: 'action',
      action_tech: 'click',
      action_timestamp: this.analyticsBridge.getAnalyticsTimestamp(),
      action_element_position: 'body',
    };

    const action = { ...otherProperty, ...payload };
    console.log('Azioni per analytics', action);
    this.analyticsPublisher.link(action);
  }

  async getUserDataForAnalytics() {
    let userData = {}; //rimettere let dopo aver tolto i commenti
    let email;

    if (LoginHelper.userLogged) {
      const userDataFromService = await this.localStorageLayer.getData1();
      console.log('userDataFromService', userDataFromService);
      const trackingViewObject =
        typeof window === 'object' && (window as any)['utag_data'];

      if (userDataFromService && userDataFromService.registry) {
        for (const contact of userDataFromService.registry
          .contactInformations) {
          if (contact.type == '4') {
            email = contact.value;
          }
        }
      }

      userData = {
        user_username: userDataFromService.username,
        user_email: email,
        user_type: trackingViewObject.user_type || '',
        user_segment: trackingViewObject.user_segment || '',
        user_category1: trackingViewObject.user_category1 || '',
        user_category2: trackingViewObject.user_category2 || '',
        user_service_level: trackingViewObject.user_service_level || '',
        user_privacy_level: trackingViewObject.user_privacy_level || '',
        user_loyalty_program: trackingViewObject.user_loyalty_program || '',
        user_loyalty_score: trackingViewObject.user_loyalty_score || '',
        user_agency_id: trackingViewObject.user_agency_id || '',
        telematics_vehicle_device_id:
          trackingViewObject.telematics_vehicle_device_id || '',
        telematics_devices: trackingViewObject.telematics_devices || '',
        user_customer_type: trackingViewObject.user_customer_type || '',
        site_agency_area: trackingViewObject.site_agency_area || '',
        site_agency_id: trackingViewObject.site_agency_id || '',
      };

      return userData;
    }
    return;
  }

  async getData1() {
    const responseGetData = await this.localStorageLayer.getData1();
    return responseGetData;
  }

  sendAnalyticsPaymentError(
    eType: any,
    eId: any,
    eMessages: any,
    eElements: any
  ) {
    const trackingViewObject =
      (typeof window === 'object' && (window as any)['utag_data']) || {};
    trackingViewObject['errors_type'] = eType;
    trackingViewObject['errors_id'] = eId;
    trackingViewObject['errors_messages'] = eMessages;
    trackingViewObject['errors_elements'] = eElements;

    this.analyticsBridge.sendAnalyticsLinkPart([
      { errorWithBaseInfoMap: trackingViewObject },
    ]);
  }

  setStorageAnalytics(key: string) {
    localStorage.setItem(key, JSON.stringify(this._analyticsContainer));
  }

  async sendTYPAnalytics(
    isPYOk: boolean,
    codErrorPayment: string,
    messageErrorPayment: string
  ) {
    const dataAnalyticsPagamentoOk = JSON.parse(
      localStorage.getItem('dataAnalyticsPagamentoOk') as any
    );
    if (dataAnalyticsPagamentoOk) {
      let payload = {};
      if (isPYOk) {
        payload = {
          page_name: 'vendita ibrida:acquista polizza:thankyou page',
          funnel_step: 'end',
        };
      } else {
        payload = {
          errors_type: 'utente',
          errors_id: codErrorPayment,
          errors_messages: messageErrorPayment,
          errors_elements: 'pagamento',
          page_name: '',
          funnel_step: 'end',
        };
      }
      this._analyticsMapper = { ...dataAnalyticsPagamentoOk, ...payload };
    }
    localStorage.removeItem('dataAnalyticsPagamentoOk');
    await this.sendAnalytics();
  }

  sendAnalyticsPopup(pegaBodyResponse?: any, analytisPopup?: any) {
    if (analytisPopup)
      this._analyticsPopup = { ...this._analyticsPopup, ...analytisPopup };
    else if (pegaBodyResponse) {
      this.findElement(pegaBodyResponse);
      this._analyticsPopup = {
        ...this._analyticsPopup,
        ...this._analyticsPegaPopup,
      };
    }

    this.analyticsBridge.sendAnalyticsLinkPart([
      { popup: this._analyticsPopup },
    ]);
  }
}

export interface ModuloAnalyticsDx {
  proposal_dx_category?: DxCategory;
  proposal_dx_flow?: flow;
  proposal_dx_pega_id?: string;
  proposal_dx_private_area_id?: string;
  proposal_dx_source?: source;
}
export interface ModuloAnalyticsHome {
  home_type?: string;
  home_property?: string;
  home_living_type?: string;
  home_residence_type?: string;
  home_street?: string;
  home_number?: string;
  home_city?: string;
  home_postal_code?: string;
  home_province?: string;
  home_earthquake_build_typology?: string;
  home_applicant_email?: string;
  home_applicant_phone?: string;
  home_applicant_fiscal_code?: string;
}

export interface ModuloAnalyticsPet {
  pet_type?: string;
  pet_pedigree?: string;
  pet_number?: string;
  pet_age?: string;
  pet_system_identifier?: string;
  pet_applicant_email?: string;
  pet_applicant_phone?: string;
  pet_applicant_fiscal_code?: string;
  pet_address?: string;
  pet_street?: string;
  pet_street_number?: string;
  pet_city?: string;
  pet_province?: string;
  pet_postal_code?: string;
}

export interface ModuloAnalyticsViaggi {
  travel_duration?: string;
  travel_destination?: string;
  travel_companions?: string;
  travel_applicant_email?: string;
  travel_applicant_phone?: string;
  travel_applicant_fiscal_code?: string;
  travel_street?: string;
  travel_street_number?: string;
  travel_city?: string;
  travel_province?: string;
  travel_postal_code?: string;
}

export type flow = 'recupero' | 'richiesta';
export type source = 'web' | 'agenzia';
