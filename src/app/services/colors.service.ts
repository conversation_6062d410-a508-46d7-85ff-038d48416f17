import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export default class ColorsService {
  /**
   * Colori registrati all'interno dell'interprete
   * @private
   */
  private _registeredColors = {
    RE: '#C4151C',
    WH: '#FFFFFF',
    WO: '#FFFFFFCC',
    GD: '#5C5C5C',
    GR: '#9B9B9B',
    GL: '#F1F1F1',
    BD: '#193A56',
    BB: '#1F5B8E',
    BL: '#5393BC',
    BA: '#193A57E6',
    BC: '#0F3250',
    CB: '#E2F0F9',
    GN: '#CCCCCC',
    GS: '#747474',
    DEAFULT: '#9B9B9B',
  };

  /**
   * Restituisce il colore registrato se identifica una corrispondenza
   * @param key Chiave di ricerca del colore
   */
  public getColor(key: string): string {
    let esito = this._registeredColors['DEAFULT'];
    if (Object.keys(this._registeredColors).includes(key))
      esito = this._registeredColors[key];
    return esito;
  }
}
