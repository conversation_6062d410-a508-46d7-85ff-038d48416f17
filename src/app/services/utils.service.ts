import { Injectable } from '@angular/core';

import { of, Observable, BehaviorSubject } from 'rxjs';
import { delay } from 'rxjs/operators';
import {
  BaseCommunicator,
  Request,
  TpdIndirizziService,
} from '@tpd-web-angular-libs/angular-library';
import { ProductTypeBoxReasonWhy } from '../models/config-interprete';
import { PreventivoAcquistoResponseDettaglioOfferta } from '@tpd-web-angular-libs/angular-library/configuratore-common-lib/interfaces/acquisto-preventivo.interfaces';
import { LoginHelper } from '@tpd-web-common-libs/nodejs-library/dist/src/helpers';
import { Helpers } from '@tpd-web-common-libs/nodejs-library';

const BASE_URL_BOX_REASON_WHY = `/wcm/connect/UnipolSai-Contenuto-Vendita-Ibrida/contenuti-box-reason-why`;

export enum SupportedLayoutFormat {
  SIMPLELAYOUT = 'SIMPLELAYOUT',
  REPEATINGROW = 'REPEATINGROW',
  REPEATINGLAYOUT = 'REPEATINGLAYOUT',
  SCREENLAYOUT = 'SCREENLAYOUT',
}

export interface InputSuccess {
  nomeContraente?: string;
  mailContraente?: string;
  agenzia?: {
    descrizione?: string;
    nome?: string;
  };
  uniboxPresente?: boolean;
}

export enum SupportedGroupFormat {
  carrelloPU = 'carrelloPU',
  stacked = 'stacked',
  default = 'default',
  defaultMW496 = 'DefaultMW496',

  defaultMW528 = 'DefaultMW528',
  defaultBgLight = 'defaultBgLight',
  inlineMiddle = 'inlineMiddle',
  inlineMiddleAlignC = 'inlineMiddleAlignC',
  inlineGridDoubleResponsive = 'inlineGridDouble',
  inlineGridTriple = 'inlineGridTriple',
  inlineGridXY = 'inlineGridXY',
  ResponsiveTwocol = 'ResponsiveTwocol',
  ResponsiveTwocolPU = 'ResponsiveTwocolPU',
  ResponsiveTwocol1fr2fr = 'ResponsiveTwocol1fr2fr',
  ResponsiveTwocol2fr1fr = 'ResponsiveTwocol2fr1fr',
  ResponsiveTwocolPU8 = 'ResponsiveTwocolPu8',
  ResponsiveTwocolLarge = 'ResponsiveTwocolLarge',
  ResponsiveTwocolSBCenter = 'ResponsiveTwocolSBCenter',
  Responsive3col = 'Responsive3col',
  Responsive3col3FullWT = 'Responsive3col3FullWT',
  Responsive4col = 'Responsive4col',
  mimicASentence = 'mimicASentence',
  mimicASentenceCenter = 'mimicASentenceCenter',
  mimicASentenceRight = 'mimicASentenceRight',
  inlineTop = 'inlineTop',
  tooltip = 'tooltip',
  TooltipContentLD = 'TooltipContentLD',
  TooltipContentLU = 'TooltipContentLU',
  TooltipContentRD = 'TooltipContentRD',
  TooltipContentRU = 'TooltipContentRU',
  grid = 'grid',
  dynamic = 'dynamic',

  card = 'card',
  box = 'box',
  cardBox = 'cardBox',
  cardForm = 'cardForm',
  cardWithBg = 'cardWithBg',
  BoxHighlight = 'BoxHighlight',
  cardBorderless = 'cardBorderless',
  cardBorder = 'cardBorder',
  inlineGridSpaceBetween = 'inlineGridSpaceBetween',
  showAfterN = 'showAfterN',
  customComponent = 'customComponent',
  accordionN = 'accordionN',
  accordionHeader = 'accordionHeader',
  accordionOpened = 'accordionOpened',
  accordionClosed = 'accordionClosed',
  promoSection = 'promoSection',
  marginLeftS = 'marginLeftS',
  web1colSwapApp2col = 'web1colSwapApp2col',
  modalContent = 'modalContent',
  modalHeader = 'modalHeader',
  modalHeaderBg = 'modalHeaderBg',
  formContainer = 'formContainer',
  rowCenteredY = 'rowCenteredY',
  responsive2RS1RL = 'responsive2RS1RL',
  inlineWeb = 'inlineWeb',
  footerButtonLayout = 'footerButtonLayout',
  adviceHighlight = 'adviceHighlight',
  adviceBox = 'adviceBox',
  roundedCard = 'roundedCard',
  inlineAlignC = 'inlineAlignC',
  inputTooltipContainer = 'inputTooltipContainer',
  BoxInfo = 'BoxInfo',
  RightWebLeftApp = 'RightWebLeftApp',
  BoxPrivacy = 'BoxPrivacy',
  webBoxBorder = 'WebBoxBorder',
  BoxWebBorderGray = 'boxwebbordergray',
  tooltipCard = 'TooltipCard',
  CardBorderDir = 'CardBorderDir',
  CarouselLayout = 'CarouselLayout',
  PuPadding = 'PuPadding',
  DefaultBgGrey = 'DefaultBgGrey',
  RoundedCardBgGrey = 'RoundedCardBgGrey',
  CardBgWhite = 'CardBgWhite',
  ResponsiveAColDRowTRowMCol = 'ResponsiveAColDRowTRowMCol',
  ResponsiveAColDRowTRowMCol16 = 'ResponsiveAColDRowTRowMCol16',
  SplittedRadio = 'SplittedRadio',
  row = 'row',
  col = 'col',
  AccordionCardProtezione = 'AccordionCardProtezione',
  InlineGridDouble = 'InlineGridDouble',
  InlineWithFirstElement = 'InlineWithFirstElement',
  FlatCard = 'FlatCard',
}

export enum SupportedCustomType {
  carrelloPu = 'CarrelloPu',
  unicoProtezione = 'UnicoProtezione',
  stepper = 'stepper',
  addressAutoComplete = 'addressAutoComplete',
  separator = 'separator',
  cardGaranzie = 'cardGaranzie',
  cardGaranzieHeader = 'cardGaranzieHeader',
  cardProtezione = 'cardProtezione',
  captcha = 'captcha',
  header = 'header',
  verticalSpace = 'verticalSpace',
  footerSticky = 'footerSticky',
  boxIndirizzo = 'boxIndirizzo',
  circularStepper = 'circularStepper',
  paymentPage = 'paymentPage',
  customActionModal = 'customActionModal',
  modalUnderwriting = 'modalUnderwriting',
  footerStickyPU = 'footerStickyPU',
  cmsBoxReasonWhy = 'cmsBoxReasonWhy',
  cmsBoxDisclaimer = 'cmsBoxDisclaimer',
  marketingCard = 'PUBoxReasonWhy',
  carouselWeb = 'carouselWeb',
  carouselCard = 'carouselCard',
  assurancePackage = 'assurancePackage',
  cardSezione = 'CardSezione',
  cardProtezioneDettaglioGaranzia = 'cardProtezioneDettaglioGaranzia',
  toastCard = 'toastCard',
  boxPrivacy = 'BoxPrivacy',
  boxPagamento = 'BoxPagamento',
  CardTelematica = 'CardTelematica',
  WorkshopLocator = 'WorkshopLocator',
  AgencyLocator = 'AgencyLocator',
  AccordionCheckbox = 'AccordionCheckbox',
  TabBarFrazionamento = 'TabBarFrazionamento',
  MultiStepper = 'MultiStepper'
}

export enum GroupsClass {
  inlineDoubleGridLayout = 'inlineDoubleGridLayout',
  inlineTripleGridLayout = 'inlineTripleGridLayout',
  inlineGridXY = 'inlineGridXY',
  inlineMiddle = 'inlineMiddle-layout',
  inlineMiddleAlignC = 'inlineMiddleAlignC',
  inlineSpaceBetween = 'inlineSpaceBetween',
  responsive2col = 'responsive2col',
  responsive2colPU = 'responsive2colPU',
  responsive2col1fr2fr = 'responsive2col1fr2fr',
  responsive2col2fr1fr = 'responsive2col2fr1fr',
  responsive2col8 = 'responsive2col8',
  mimicASentence = 'mimicASentence',
  responsive2colLarge = 'responsive2colLarge',
  mimicASentenceCenter = 'mimicASentenceCenter',
  mimicASentenceRight = 'mimicASentenceRight',
  responsive3col = 'responsive3col',
  responsive3col3fullwt = 'responsive3col3fullwt',
  responsive4col = 'responsive4col',
  web1colSwapApp2col = 'web1colSwapApp2col',
  responsiveTwocolSBCenter = 'responsiveTwocolSBCenter',
  modalHeader = 'modalHeader',
  modalHeaderBg = 'modalHeaderBg',
  rowCenteredY = 'rowCenteredY',
  responsive2RS1RL = 'responsive2RS1RL',
  inlineTop = 'inlineTop',
  inlineWeb = 'inlineWeb-layout',
  roundedCard = 'rounded-card',
  inlineAlignC = 'inlineAlignC',
  inputTooltipContainer = 'input-tooltip-container',
  tooltipCard = 'tooltip-card',
  card = 'card',
  box = 'box',
  /*Gestione stile standard layout: Row & Col*/
  row = 'row-layout',
  col = 'col-layout',
  InlineGridDouble = 'InlineGridDouble',
  InlineWithFirstElement = 'InlineWithFirstElement'
}

// TOOLTIP
export enum TooltipDirection {
  TooltipLU = 'TooltipLU', // left up
  TooltipLD = 'TooltipLD', // left down
  TooltipRU = 'TooltipRU', // rigth up
  TooltipRD = 'TooltipRD', // rigth down
}

export const TooltipDirectionMapper = {
  'lu': TooltipDirection.TooltipLU,
  'ld': TooltipDirection.TooltipLD,
  'ru': TooltipDirection.TooltipRU,
  'rd': TooltipDirection.TooltipRD,
  'tooltiplu': TooltipDirection.TooltipLU,
  'Tooltipld': TooltipDirection.TooltipLD,
  'Tooltipru': TooltipDirection.TooltipRU,
  'Tooltiprd': TooltipDirection.TooltipRD

}

const VOWELS = ['a', 'e', 'i', 'o', 'u'];
const CONSONANTS = [
  'b',
  'c',
  'd',
  'f',
  'g',
  'h',
  'j',
  'k',
  'l',
  'm',
  'n',
  'p',
  'q',
  'r',
  's',
  't',
  'v',
  'w',
  'x',
  'y',
  'z',
];

@Injectable({
  providedIn: 'root',
})
export class UtilsService {
  lastControlID = 0;
  private _waitingFor = false;
  civicoAssente!: string;
  viaAssente$: BehaviorSubject<string> = new BehaviorSubject('');
  closeToolTip$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  addressErrorVisible!: boolean;

  contatorePaginaGaranzie = 0;
  scrollOnTop = true;
  sendAnalytics = true;

  closeToolTip = this.closeToolTip$.asObservable();
  viaAssente = this.viaAssente$.asObservable();

  setReference(value: string) {
    this.viaAssente$.next(value);
  }

  setClosedToolTip(value: boolean) {
    this.closeToolTip$.next(value);
  }
  resetCloseToolTip() {
    this.closeToolTip$.next(false);
  }

  constructor(
    private indirizziService: TpdIndirizziService,
    private baseCommunicator: BaseCommunicator
  ) {
    /*DO-NOTHING*/
  }

  private boxReasonWhyConfig: any;
  private boxDisclaimerConfig: any;
  reasonWhyCallStatus: BehaviorSubject<
    'KO' | 'OK' | 'PENDING' | 'NOT_EXECUTED'
  > = new BehaviorSubject('NOT_EXECUTED' as any);

  /*
   * gestione callback custom per componentID (Button component custom)
   */
  callbackComponentID = {
    manualAddressButton: () => {
      /* DONOTHING */
    },
    cancelAddressButton: () => {
      /* DONOTHING */
    },
    closeModal: (ation?: boolean) => {
      /* DONOTHING */
    },
    confirmAddressButton: () => {
      /* DONOTHING */
    },
    closeModalAndReloadPage: (action?: boolean) => {
      /* DONOTHING */
    },
    closeModalAndRefreshPage: (action?: boolean) => {
      /* DONOTHING */
    },
  };

  /*
   * gestione callback custom per componentID (Button component custom) per fare delle azioni dopo la retrieve
   */
  callbackAfterRetrieveByComponentID = {
    confirmAddressButton: () => {
      /* DONOTHING */
    },
    closeModalAndReloadPage: (action?: boolean) => {
      /* DONOTHING */
    },
    closeModalAndRefreshPage: (action?: boolean) => {
      /* DONOTHING */
    },
  };

  mapperGroupFormat = {
    Stacked: SupportedGroupFormat.stacked,
    Default: SupportedGroupFormat.default,
    DefaultMW496: SupportedGroupFormat.defaultMW496,
    DefaultMW528: SupportedGroupFormat.defaultMW528,
    DefaultWithBg: SupportedGroupFormat.defaultBgLight,
    Inline: SupportedGroupFormat.mimicASentence,
    'Inline middle': SupportedGroupFormat.inlineMiddle,
    'Inline middle align C': SupportedGroupFormat.inlineMiddleAlignC,
    CenterWebLeftApp: SupportedGroupFormat.inlineMiddle,
    'Inline grid double responsive':
      SupportedGroupFormat.inlineGridDoubleResponsive,
    'Inline grid triple': SupportedGroupFormat.inlineGridTriple,
    Grid: SupportedGroupFormat.grid,
    Dynamic: SupportedGroupFormat.dynamic,
    Tooltip: SupportedGroupFormat.tooltip,
    TooltipContentLD: SupportedGroupFormat.TooltipContentLD,
    TooltipContentLU: SupportedGroupFormat.TooltipContentLU,
    TooltipContentRD: SupportedGroupFormat.TooltipContentRD,
    TooltipContentRU: SupportedGroupFormat.TooltipContentRU,
    'Inline grid x y': SupportedGroupFormat.inlineGridXY,
    Card: SupportedGroupFormat.card,
    Box: SupportedGroupFormat.box,
    CardNoPadding: SupportedGroupFormat.card,
    CardBox: SupportedGroupFormat.cardBox,
    CardForm: SupportedGroupFormat.cardForm,
    CardBorder: SupportedGroupFormat.cardBorder,
    CardWithBg: SupportedGroupFormat.cardWithBg,
    CardBorderless: SupportedGroupFormat.cardBorderless,
    'Inline grid space between': SupportedGroupFormat.inlineGridSpaceBetween,
    'SpaceBetween Inline grid x y': SupportedGroupFormat.inlineGridSpaceBetween,
    'SpaceBeetween Inline grid x y':
      SupportedGroupFormat.inlineGridSpaceBetween,
    'ShowAfterN': SupportedGroupFormat.showAfterN,
    CustomComponent: SupportedGroupFormat.customComponent,
    'Responsive 2col': SupportedGroupFormat.ResponsiveTwocol,
    'Responsive 2col PU': SupportedGroupFormat.ResponsiveTwocolPU,
    'Responsive 2Col PU': SupportedGroupFormat.ResponsiveTwocolPU,
    'Responsive 2col 1fr2fr': SupportedGroupFormat.ResponsiveTwocol1fr2fr,
    'Responsive 2col 2fr1fr': SupportedGroupFormat.ResponsiveTwocol2fr1fr,
    'Responsive 2col 8': SupportedGroupFormat.ResponsiveTwocolPU8,
    'Responsive 2col L': SupportedGroupFormat.ResponsiveTwocolLarge,
    'Responsive 3col': SupportedGroupFormat.Responsive3col,
    'Responsive 3col App Row': SupportedGroupFormat.Responsive3col,
    'Responsive 3col 3FullWT': SupportedGroupFormat.Responsive3col3FullWT,
    'Responsive 4col': SupportedGroupFormat.Responsive4col,
    'Mimic a sentence': SupportedGroupFormat.mimicASentence,
    'AccordionN': SupportedGroupFormat.accordionN,
    'Mimic a sentence center': SupportedGroupFormat.mimicASentenceCenter,
    'Mimic a sentence center web': SupportedGroupFormat.mimicASentenceCenter,
    'Mimic a sentence right': SupportedGroupFormat.mimicASentenceRight,
    AccordionHeader: SupportedGroupFormat.accordionHeader,
    AccordionOpened: SupportedGroupFormat.accordionOpened,
    AccordionClosed: SupportedGroupFormat.accordionClosed,
    PromoSection: SupportedGroupFormat.promoSection,
    BoxHighlight: SupportedGroupFormat.BoxHighlight,
    'Web 2col App 1col': SupportedGroupFormat.inlineGridDoubleResponsive,
    'Margin Left S': SupportedGroupFormat.marginLeftS,
    'Web 1 col swap App 2col': SupportedGroupFormat.web1colSwapApp2col,
    'Web App 1 col swap': SupportedGroupFormat.web1colSwapApp2col,
    'Responsive 2 col SB Center': SupportedGroupFormat.ResponsiveTwocolSBCenter,
    'Responsive 2 col SB': SupportedGroupFormat.ResponsiveTwocolSBCenter,
    ModalContent: SupportedGroupFormat.modalContent,
    ModalHeader: SupportedGroupFormat.modalHeader,
    ModalHeaderWithBkg: SupportedGroupFormat.modalHeaderBg,
    BoxWebBorderGray: SupportedGroupFormat.BoxWebBorderGray,
    StackedTPD: SupportedGroupFormat.stacked,
    DefaultTPD: SupportedGroupFormat.default,
    InlineTPD: SupportedGroupFormat.mimicASentence,
    InlineTopTPD: SupportedGroupFormat.inlineTop,
    'InlineTPD middle': SupportedGroupFormat.inlineMiddle,
    'MimicTPD a sentence': SupportedGroupFormat.mimicASentence,
    RowCenteredY: SupportedGroupFormat.rowCenteredY,
    'Margin Horizontal S': SupportedGroupFormat.default,
    FooterButtonPaddingLayout: SupportedGroupFormat.footerButtonLayout,
    FormContainer: SupportedGroupFormat.formContainer,
    'Responsive 2RS 1RL': SupportedGroupFormat.responsive2RS1RL,
    'InlineWeb DefaultApp': SupportedGroupFormat.inlineWeb,
    'Web SpaceB App Stacked': SupportedGroupFormat.inlineGridSpaceBetween,
    AdviceHighlight: SupportedGroupFormat.adviceHighlight,
    AdviceBox: SupportedGroupFormat.adviceBox,
    RoundedCard: SupportedGroupFormat.roundedCard,
    inlineAlignC: SupportedGroupFormat.inlineAlignC,
    'InputWithTooltip Container': SupportedGroupFormat.inputTooltipContainer,
    BoxInfo: SupportedGroupFormat.BoxInfo,
    RightWebLeftApp: SupportedGroupFormat.RightWebLeftApp,
    BoxPrivacy: SupportedGroupFormat.BoxPrivacy,
    'InlineTPD middle Center': SupportedGroupFormat.inlineMiddleAlignC,
    WebBoxBorder: SupportedGroupFormat.webBoxBorder,
    TooltipCard: SupportedGroupFormat.tooltipCard,
    Carousel: SupportedGroupFormat.CarouselLayout,
    CarouselCard: SupportedGroupFormat.CarouselLayout,
    DefaultBgGrey: SupportedGroupFormat.DefaultBgGrey,
    RoundedCardBgGrey: SupportedGroupFormat.RoundedCardBgGrey,
    CardBgWhite: SupportedGroupFormat.CardBgWhite,
    'Responsive ACol DRow TRow MCol': SupportedGroupFormat.ResponsiveAColDRowTRowMCol,
    'Responsive ACol16 DRow TRow MCol': SupportedGroupFormat.ResponsiveAColDRowTRowMCol16,
    SplittedRadio: SupportedGroupFormat.SplittedRadio,
    AccordionCardProtezione: SupportedGroupFormat.AccordionCardProtezione,
    'Inline grid double': SupportedGroupFormat.InlineGridDouble,
    'Inline with first element': SupportedGroupFormat.InlineWithFirstElement,
    FlatCard: SupportedGroupFormat.FlatCard
  };

  mapperCustomType = {
    CarrelloPU: SupportedCustomType.carrelloPu,
    CarrelloPUFooter: SupportedCustomType.carrelloPu,
    UnicoProtezione: SupportedCustomType.unicoProtezione,
    Stepper: SupportedCustomType.stepper,
    'Address Autocomplete': SupportedCustomType.addressAutoComplete,
    'CMS BoxReasonWhy Home': SupportedCustomType.cmsBoxReasonWhy,
    'CMS BoxReasonWhyDisclaimer Home': SupportedCustomType.cmsBoxDisclaimer,
    'CMS BoxReasonWhy Travel': SupportedCustomType.cmsBoxReasonWhy,
    'CMS BoxReasonWhyDisclaimer Travel': SupportedCustomType.cmsBoxDisclaimer,
    'CMS BoxReasonWhy Pet': SupportedCustomType.cmsBoxReasonWhy,
    'CMS BoxReasonWhyDisclaimer Pet': SupportedCustomType.cmsBoxDisclaimer,
    PUBoxReasonWhy: SupportedCustomType.marketingCard,
    Separator: SupportedCustomType.separator,
    CardGaranzie: SupportedCustomType.cardGaranzie,
    CardGaranzieHeader: SupportedCustomType.cardGaranzieHeader,
    CardProtezione: SupportedCustomType.cardProtezione,
    Captcha: SupportedCustomType.captcha,
    Header: SupportedCustomType.header,
    verticalSpace: SupportedCustomType.verticalSpace,
    StickyFooter: SupportedCustomType.footerSticky,
    BoxIndirizzo: SupportedCustomType.boxIndirizzo,
    CircularStepper: SupportedCustomType.circularStepper,
    PaymentPage: SupportedCustomType.paymentPage,
    // "AgencyChoice":SupportedCustomType.agencyChoice,
    CustomActionModal: SupportedCustomType.customActionModal,
    PopupUnderwriting: SupportedCustomType.modalUnderwriting,
    StickyFooterPU: SupportedCustomType.footerStickyPU,
    //"ModalLoader": SupportedCustomType.modalLoader
    CarouselWeb: SupportedCustomType.carouselWeb,
    CarouselCard: SupportedCustomType.carouselCard,
    'Assurance Package': SupportedCustomType.assurancePackage,
    CardSezione: SupportedCustomType.cardSezione,
    CardProtezioneDettaglioGaranzia:
      SupportedCustomType.cardProtezioneDettaglioGaranzia,
    ToastCard: SupportedCustomType.toastCard,
    BoxPrivacy: SupportedCustomType.boxPrivacy,
    BoxPagamento: SupportedCustomType.boxPagamento,
    CardTelematica: SupportedCustomType.CardTelematica,
    WorkshopLocator: SupportedCustomType.WorkshopLocator,
    AgencyLocator: SupportedCustomType.AgencyLocator,
    AccordionCheckbox: SupportedCustomType.AccordionCheckbox,
    TabBarFrazionamento: SupportedCustomType.TabBarFrazionamento,
    MultiStepper: SupportedCustomType.MultiStepper
  };

  static htmlDecode_local(sVal: string): string {
    if (sVal === 'u20AC') {
      sVal = '\u20AC';
    }
    const doc = new DOMParser().parseFromString(sVal, 'text/html');

    return doc.documentElement.textContent ?? '';
  }

  htmlDecode(sVal: string): string {
    let esito = '';

    if (sVal) esito = UtilsService.htmlDecode_local(sVal);

    return esito;
  }

  getClassFromFormat(labelFormat: string) {
    const supportedAlign = [
      'Default',
      'Left',
      'Right',
      'Center',
      'default',
      'center',
      'left',
      'right',
    ];
    let align;
    let cleanLabel = labelFormat;
    let match;

    supportedAlign.forEach((a) => {
      if (labelFormat.includes(a)) {
        match = a;
        align = `text-align-${a}`;
      }
    });
    if (match) {
      cleanLabel = labelFormat.replace(`${match}`, '').trim();
    }

    // replace spaces with "-"
    // remove ( ) for css class mapping
    cleanLabel = cleanLabel.replace(/\(+(.*?)\)+/g, '$1').replace(/\s+/gi, '-');
    return align ? `${cleanLabel} ${align}` : cleanLabel;
  }

  getVisibility(sec: any): Observable<boolean> {
    this.waitingFor = true;
    return of(this.waitingFor).pipe(delay(sec * 1000));
  }

  checkNameSurnameOnFiscalCode(
    valToCompare: string,
    controlName: string,
    type: 'name' | 'surname' | 'taxcode',
    compareType?: 'name' | 'surname' | 'taxcode'
  ) {
    let letterFromCF = '';
    let nameRequired = false;
    let tipo = type;
    let cf: string = valToCompare;
    let compareName: string = controlName;

    if (tipo === 'taxcode' && compareType) {
      tipo = compareType;
      cf = controlName;
      compareName = valToCompare;
    }
    if (tipo === 'surname') {
      letterFromCF = cf.length > 0 ? cf.substring(0, 3).toLowerCase() : '';
    } else if (tipo === 'name') {
      letterFromCF = cf.length > 0 ? cf.substring(3, 6).toLowerCase() : '';
      nameRequired = true;
    }

    const error =
      letterFromCF.length > 0
        ? this.getConsonantsFromNameOrSurname(
        compareName.toLowerCase(),
        nameRequired
      ) !== letterFromCF
        : false;

    return error;
  }

  private getConsonantsFromNameOrSurname(
    nameOrSurname: string,
    nameRequired: boolean
  ) {
    const lettersNameOrSurname: string[] = [];
    let numConsonants = 0;
    let flag2LetterName = true;
    for (const character of nameOrSurname) {
      if (numConsonants === 3) {
        break;
      }
      if (CONSONANTS.includes(character.toLowerCase())) {
        if (
          numConsonants === 1 &&
          this.getNumVowelsConsonants(nameOrSurname).numOfConsonants > 3 &&
          nameRequired &&
          flag2LetterName
        ) {
          flag2LetterName = false;
          continue;
        }
        lettersNameOrSurname.push(character);
        numConsonants += 1;
      }
    }

    let numVowelsConsonants = numConsonants;
    if (numConsonants < 3) {
      for (const character of nameOrSurname) {
        if (numVowelsConsonants == 3) {
          break;
        }
        if (VOWELS.includes(character.toLowerCase())) {
          lettersNameOrSurname.push(character);
          numVowelsConsonants += 1;
        }
      }
      if (numVowelsConsonants < 3) {
        while (lettersNameOrSurname.length != 3) {
          lettersNameOrSurname.push('x');
        }
      }
    }
    return lettersNameOrSurname.join('');
  }

  private getNumVowelsConsonants(nameOrSurname: string) {
    //(Vowels:Int, Consonants:Int)
    let numOfVowels = 0;
    let numOfConsonants = 0;
    for (const character of nameOrSurname) {
      if (character.toLowerCase()) {
        if (VOWELS.includes(character.toLowerCase())) ++numOfVowels;

        if (CONSONANTS.includes(character.toLowerCase())) ++numOfConsonants;
      }
    }
    return {
      numOfVowels: numOfVowels,
      numOfConsonants: numOfConsonants,
    };
  }

  /**
   *
   * @param componentID custom attribute componentID
   * @desc Metodo richiamato in button component, serve per intercettare
   *  l'azione di click e eseguire azioni custom. L'azione custom va definita
   *  nel componente in cui saraà iniettata la logica (vedi addressAutocomplete)
   * @returns restituisce la callback associata a quel componentID
   */
  getCustomCallbackByComponentID(componentID: string) {
    return (this.callbackComponentID as any)[componentID];
  }

  getCallbackAfterRetrieveByComponentID(componentID: string) {
    return (this.callbackAfterRetrieveByComponentID as any)[componentID];
  }

  normalizzaIndirizzi(normalizzaRequest: any) {
    let response: any;
    if (LoginHelper.userLogged) {
      response = this.indirizziService.normalizzaIndirizzo(
        normalizzaRequest.indirizzo,
        normalizzaRequest.comune,
        normalizzaRequest.cap,
        normalizzaRequest.provincia
      );
    } else {
      response = this.indirizziService.normalizzaIndirizzo(
        normalizzaRequest.indirizzo,
        normalizzaRequest.comune,
        normalizzaRequest.cap,
        normalizzaRequest.provincia
      );
    }
    return response.toPromise();
  }

  cleanUrl() {
    const uri = window.location.href.toString();
    if (uri.indexOf('?') > 0) {
      const clean_uri = uri.substring(0, uri.indexOf('?'));
      window.history.replaceState({}, document.title, clean_uri);
    }
  }

  async retrieveBoxReasonWhy(product: ProductTypeBoxReasonWhy) {
    this.reasonWhyCallStatus.next('PENDING');
    const queryParams = {
      filtroProdotto: `UnipolSai-Disegno-Area-Pubblica/Tassonomia_Blocchi_CTA/${product}`,
    };
    const headers = {
      'x-unipol-canale': LoginHelper.userLogged ? 'AR' : 'AP',
    };
    const request = new Request(BASE_URL_BOX_REASON_WHY, {
      headers: headers,
      queryParams: queryParams,
    });
    return await this.baseCommunicator
      .get(request)
      .then((e: any) => {
        if (e && e.elements && e.elements.length > 0) {
          const firstElem = e.elements[0];
          this.boxReasonWhyConfig = firstElem.boxReasonWhy;
          this.boxDisclaimerConfig = firstElem.boxDisclaimer;
          this.reasonWhyCallStatus.next('OK');
        }
      })
      .catch(() => {
        this.boxReasonWhyConfig = undefined;
        this.boxDisclaimerConfig = undefined;
        this.reasonWhyCallStatus.next('KO');
      });
  }

  getBoxReasonWhyConfig() {
    return this.boxReasonWhyConfig;
  }

  getBoxReasonDisclaimerConfig() {
    return this.boxDisclaimerConfig;
  }

  getDettaglioOfferta(
    idOfferta: string,
    versione: string
  ): Promise<PreventivoAcquistoResponseDettaglioOfferta> {
    const headers = {
      'x-unipol-canale': LoginHelper.userLogged ? 'AR' : 'AP',
    };
    const request = new Request(
      `acquisto/v1/${
        LoginHelper.userLogged ? 'secure' : 'unsecure'
      }/preventivi/dettaglio-offerta`,
      {
        headers,
        queryParams: {
          flagUtenteLoggato: LoginHelper.userLogged.toString(),
          idOfferta: idOfferta,
          versione: versione,
        },
      }
    );
    return this.baseCommunicator.get(request);
  }
  set waitingFor(bol: boolean) {
    this._waitingFor = bol;
  }
  get waitingFor() {
    return this._waitingFor;
  }

  public async setInputSuccess(
    mail: string,
    nomeAgenzia: string,
    descAgenzia: string,
    nomeContraente: string,
    uniboxPresente: string
  ): Promise<void> {
    const inputSuccess = {
      nomeContraente: this.htmlDecode(nomeContraente),
      mailContraente: this.htmlDecode(mail),
      agenzia: {
        nome: this.htmlDecode(nomeAgenzia),
        descrizione: this.htmlDecode(descAgenzia),
      },
      uniboxPresente: uniboxPresente === 'true'
    };
    await Helpers.SessionHelper.setData('INPUT_SUCCESS', JSON.stringify(inputSuccess));
  }

  public async getInputSuccess(): Promise<InputSuccess | undefined> {
    let inputSuccess;
    try {
      inputSuccess = JSON.parse(await Helpers.SessionHelper.getData('INPUT_SUCCESS'));
      await Helpers.SessionHelper.deleteData('INPUT_SUCCESS');
    } catch (e) {
      console.error('Errore nel recupero dell\' INPUT SUCCESS');
    }
    return inputSuccess;
  }

  checkLetter(fiscalCode: string): number {
    let totale = 0;
    for (let i = 0; i < fiscalCode.length - 1; i++) {
      if ((i + 1) % 2 == 0) {
        totale += (pari as any)[fiscalCode[i]];
      } else {
        totale += (dispari as any)[fiscalCode[i]];
      }
    }
    const rest = totale % 26;
    return rest;
  }

  dataNascitaFromCF(codiceFiscale: any, typeDate = false) {
    const MESICF = {
      A: '01',
      B: '02',
      C: '03',
      D: '04',
      E: '05',
      H: '06',
      L: '07',
      M: '08',
      P: '09',
      R: '10',
      S: '11',
      T: '12',
    };

    codiceFiscale = codiceFiscale.toUpperCase();
    const mese = (MESICF as any)[codiceFiscale.substring(8, 9)];
    let [anno, giorno] = [
      codiceFiscale.substring(6, 8) as string,
      codiceFiscale.substring(9, 11) as string,
    ];
    if (+giorno > 40) {
      giorno = (+giorno - 40) as unknown as string;
      giorno = +giorno > 9 ? giorno : '0' + giorno;
    }
    const currentYear = +new Date().getFullYear().toString().substring(2, 4);

    anno = (+anno < currentYear ? '20' : '19') + anno;

    const giornoMeseAnno = `${giorno}/${mese}/${anno}`;
    const date = new Date(+anno, mese - 1, +giorno);
    return typeDate ? date : giornoMeseAnno;
  }
}

export const pari = {
  A: 0,
  B: 1,
  C: 2,
  D: 3,
  E: 4,
  F: 5,
  G: 6,
  H: 7,
  I: 8,
  J: 9,
  K: 10,
  L: 11,
  M: 12,
  N: 13,
  O: 14,
  P: 15,
  Q: 16,
  R: 17,
  S: 18,
  T: 19,
  U: 20,
  V: 21,
  W: 22,
  X: 23,
  Y: 24,
  Z: 25,
  '0': 0,
  '1': 1,
  '2': 2,
  '3': 3,
  '4': 4,
  '5': 5,
  '6': 6,
  '7': 7,
  '8': 8,
  '9': 9,
};

export const dispari = {
  A: 1,
  B: 0,
  C: 5,
  D: 7,
  E: 9,
  F: 13,
  G: 15,
  H: 17,
  I: 19,
  J: 21,
  K: 2,
  L: 4,
  M: 18,
  N: 20,
  O: 11,
  P: 3,
  Q: 6,
  R: 8,
  S: 12,
  T: 14,
  U: 16,
  V: 10,
  W: 22,
  X: 25,
  Y: 24,
  Z: 23,
  '0': 1,
  '1': 0,
  '2': 5,
  '3': 7,
  '4': 9,
  '5': 13,
  '6': 15,
  '7': 17,
  '8': 19,
  '9': 21,
};
export const letterNumberCf = {
  A: 0,
  B: 1,
  C: 2,
  D: 3,
  E: 4,
  F: 5,
  G: 6,
  H: 7,
  I: 8,
  J: 9,
  K: 10,
  L: 11,
  M: 12,
  N: 13,
  O: 14,
  P: 15,
  Q: 16,
  R: 17,
  S: 18,
  T: 19,
  U: 20,
  V: 21,
  W: 22,
  X: 23,
  Y: 24,
  Z: 25,
};
