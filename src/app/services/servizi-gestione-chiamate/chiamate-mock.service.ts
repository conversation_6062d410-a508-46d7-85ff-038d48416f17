import { Injectable } from "@angular/core";
import { GenericDataType, IActionLoadRequest } from "../../models/bff.interprete.model";
import { TpdInterpreteDxApi } from "../../tpd-interprete-angular-dx-api-pu/tpd-interprete-angular-dx-api-pu";
import { PEGA_API } from "../bffinterprete.service";
import { StatusService } from "../status.service";

@Injectable({
  providedIn: 'root'
})
export default class ChiamateMockService{
  private _mappaDatiChiamata: {[key: string]: [GenericDataType, string, string]} = {
    'cane': [{
      'Ambito.Bene.Pet.TipologiaAnimale': '1',
      'Ambito.Bene.Pet.Eta': '3'
    }, 'UNICO', 'PUPET'],
    'gatto': [{
      'Ambito.Bene.Pet.TipologiaAnimale': '2',
      'Ambito.Bene.Pet.Eta': '3'
    }, 'UNICO', 'PUPET'],
    'pg': [{
      'Ambito.Bene.Auto.RecuperoClasseAltroVeicolo' : false,
      'Ambito.Bene.Auto.Targa' : 'GJ111BA',
      'Ambito.Bene.Auto.TipoVeicolo' : '1',
      'Ambito.Proprietario.IndirizzoSedeLegale.Cap' : '74024',
      'Ambito.Proprietario.IndirizzoSedeLegale.Comune' : 'Manduria',
      'Ambito.Proprietario.IndirizzoSedeLegale.NomeStrada' : 'Via Per Manduria',
      'Ambito.Proprietario.IndirizzoSedeLegale.NumeroCivico' : '21',
      'Ambito.Proprietario.IndirizzoSedeLegale.Provincia' : 'TA',
      'Ambito.Proprietario.IndirizzoSedeLegale.Stato' : 'Italia',
      'Ambito.Proprietario.NumeroAddettiInferioreOUgualeACinque' : true,
      'Ambito.Proprietario.PartitaIva' : '04976201006',
      'Ambito.Proprietario.TipologiaPartitaIva' : '1',
      'Ambito.Proprietario.TipoPersona' : 'PG'
    }, 'UNICO', 'PUVEICOLO'],
    'auto': [{
      'Ambito.Bene.Auto.TipoVeicolo': '1',
      'Ambito.Bene.Auto.Targa': 'GH002AV',
      'Ambito.Proprietario.DataDiNascita': '03/01/1957',
      'Ambito.Bene.Auto.RecuperoClasseAltroVeicolo': false,
      'Ambito.Proprietario.TipoPersona': 'PF',
      'Ambito.Proprietario.Residenza.NomeStrada': 'Via Per Manduria',
      'Ambito.Proprietario.Residenza.NumeroCivico': '21',
      'Ambito.Proprietario.Residenza.Comune': 'Manduria',
      'Ambito.Proprietario.Residenza.Provincia': 'TA',
      'Ambito.Proprietario.Residenza.Cap': '74024',
      'Ambito.Proprietario.Residenza.Stato': 'Italia',
      'Ambito.Proprietario.Residenza.CodiceCatastaleComune': 'E882'
    }, 'UNICO', 'PUVEICOLO'],
    'auto-elettrica': [{
      'Ambito.Bene.Auto.TipoVeicolo': '1',
      'Ambito.Bene.Auto.Targa': 'GH002AV',
      'Ambito.Proprietario.DataDiNascita': '03/01/1956',
      'Ambito.Bene.Auto.RecuperoClasseAltroVeicolo': false,
      'Ambito.Proprietario.TipoPersona': 'PF',
      'Ambito.Proprietario.Residenza.NomeStrada': 'Via Per Manduria',
      'Ambito.Proprietario.Residenza.NumeroCivico': '21',
      'Ambito.Proprietario.Residenza.Comune': 'Manduria',
      'Ambito.Proprietario.Residenza.Provincia': 'TA',
      'Ambito.Proprietario.Residenza.Cap': '74024',
      'Ambito.Proprietario.Residenza.Stato': 'Italia',
      'Ambito.Proprietario.Residenza.CodiceCatastaleComune': 'E882'
    }, 'UNICO', 'PUVEICOLO'],
    'auto-st': [{
      'Ambito.Bene.Auto.TipoVeicolo': '1',
      'Ambito.Bene.Auto.Targa': '',
      'Ambito.Proprietario.DataDiNascita': '04/04/1976',
      'Ambito.Bene.Auto.RecuperoClasseAltroVeicolo': false,
      'Ambito.Proprietario.TipoPersona': 'PF',
      'Ambito.Proprietario.Residenza.NomeStrada': 'Via Per Manduria',
      'Ambito.Proprietario.Residenza.NumeroCivico': '21',
      'Ambito.Proprietario.Residenza.Comune': 'Manduria',
      'Ambito.Proprietario.Residenza.Provincia': 'TA',
      'Ambito.Proprietario.Residenza.Cap': '74024',
      'Ambito.Proprietario.Residenza.Stato': 'Italia',
      'Ambito.Proprietario.Residenza.CodiceCatastaleComune': 'E882'
    }, 'UNICO', 'PUVEICOLO'],
    'auto-cm': [{
      'Ambito.Bene.Auto.TipoVeicolo': '1',
      'Ambito.Bene.Auto.Targa': 'GH002AV',
      'Ambito.Proprietario.DataDiNascita': '03/01/1957',
      'Ambito.Bene.Auto.RecuperoClasseAltroVeicolo': true,
      'Ambito.Proprietario.TipoPersona': 'PF',
      'Ambito.Proprietario.Residenza.NomeStrada': 'Via Per Manduria',
      'Ambito.Proprietario.Residenza.NumeroCivico': '21',
      'Ambito.Proprietario.Residenza.Comune': 'Manduria',
      'Ambito.Proprietario.Residenza.Provincia': 'TA',
      'Ambito.Proprietario.Residenza.Cap': '74024',
      'Ambito.Proprietario.Residenza.Stato': 'Italia',
      'Ambito.Proprietario.Residenza.CodiceCatastaleComune': 'E882'
    }, 'UNICO', 'PUVEICOLO'],
    'auto-st-cm': [{
      'Ambito.Bene.Auto.TipoVeicolo': '1',
      'Ambito.Bene.Auto.Targa': '',
      'Ambito.Proprietario.DataDiNascita': '04/04/1976',
      'Ambito.Bene.Auto.RecuperoClasseAltroVeicolo': true,
      'Ambito.Proprietario.TipoPersona': 'PF',
      'Ambito.Proprietario.Residenza.NomeStrada': 'Via Per Manduria',
      'Ambito.Proprietario.Residenza.NumeroCivico': '21',
      'Ambito.Proprietario.Residenza.Comune': 'Manduria',
      'Ambito.Proprietario.Residenza.Provincia': 'TA',
      'Ambito.Proprietario.Residenza.Cap': '74024',
      'Ambito.Proprietario.Residenza.Stato': 'Italia',
      'Ambito.Proprietario.Residenza.CodiceCatastaleComune': 'E882'
    }, 'UNICO', 'PUVEICOLO'],
    'ciclomotore': [{
      'Ambito.Bene.Auto.TipoVeicolo': '9',
      'Ambito.Bene.Auto.Targa': 'X4N58Y',
      'Ambito.Proprietario.DataDiNascita': '30/04/1965',
      'Ambito.Bene.Auto.RecuperoClasseAltroVeicolo': false,
      'Ambito.Proprietario.TipoPersona': 'PF',
      'Ambito.Proprietario.Residenza.NomeStrada': 'Via Per Manduria',
      'Ambito.Proprietario.Residenza.NumeroCivico': '21',
      'Ambito.Proprietario.Residenza.Comune': 'Manduria',
      'Ambito.Proprietario.Residenza.Provincia': 'TA',
      'Ambito.Proprietario.Residenza.Cap': '74024',
      'Ambito.Proprietario.Residenza.Stato': 'Italia',
      'Ambito.Proprietario.Residenza.CodiceCatastaleComune': 'E882'
    }, 'UNICO', 'PUVEICOLO'],
    'motociclo': [{
      'Ambito.Bene.Auto.TipoVeicolo': '8',
      'Ambito.Bene.Auto.Targa': 'DM06087',
      'Ambito.Proprietario.DataDiNascita': '01/05/1990',
      'Ambito.Bene.Auto.RecuperoClasseAltroVeicolo': false,
      'Ambito.Proprietario.TipoPersona': 'PF',
      'Ambito.Proprietario.Residenza.NomeStrada': 'Via Per Manduria',
      'Ambito.Proprietario.Residenza.NumeroCivico': '21',
      'Ambito.Proprietario.Residenza.Comune': 'Manduria',
      'Ambito.Proprietario.Residenza.Provincia': 'TA',
      'Ambito.Proprietario.Residenza.Cap': '74024',
      'Ambito.Proprietario.Residenza.Stato': 'Italia',
      'Ambito.Proprietario.Residenza.CodiceCatastaleComune': 'E882'
    }, 'UNICO', 'PUVEICOLO'],
    'mobilita': [{
      'Ambito.Proprietario.DataDiNascita': '08/07/1970',
      'Ambito.Proprietario.Residenza.NomeStrada': 'Via per Manduria',
      'Ambito.Proprietario.Residenza.NumeroCivico': '21',
      'Ambito.Proprietario.Residenza.Comune': 'Manduria',
      'Ambito.Proprietario.Residenza.Provincia': 'TA',
      'Ambito.Proprietario.Residenza.Cap': '74024',
      'Ambito.Proprietario.Residenza.Stato': 'Italia'
    }, 'UNICO', 'PUMOBILITA'],
    'casa': [{
      'Ambito.Bene.Casa.TipologiaAbitazione': 1,
      'Ambito.Bene.Casa.Indirizzo.NomeStrada': 'Via Pomerio',
      'Ambito.Bene.Casa.Indirizzo.NumeroCivico': '100',
      'Ambito.Bene.Casa.Indirizzo.Comune': 'Capua',
      'Ambito.Bene.Casa.Indirizzo.Provincia': 'CE',
      'Ambito.Bene.Casa.Indirizzo.Cap': '81043',
      'Ambito.Bene.Casa.Indirizzo.Stato': 'ITALIA',
      'Ambito.Proprietario.TipoPersona': 'PF'
    }, 'UNICO', 'PUCASA'],
    'casa-ps': [{
      'Ambito.Bene.Casa.TipologiaAbitazione': 2,
      'Ambito.Bene.Casa.Indirizzo.NomeStrada': 'Via Pomerio',
      'Ambito.Bene.Casa.Indirizzo.NumeroCivico': '100',
      'Ambito.Bene.Casa.Indirizzo.Comune': 'Capua',
      'Ambito.Bene.Casa.Indirizzo.Provincia': 'CE',
      'Ambito.Bene.Casa.Indirizzo.Cap': '81043',
      'Ambito.Bene.Casa.Indirizzo.Stato': 'ITALIA',
      'Ambito.Proprietario.TipoPersona': 'PF',
      'Ambito.Bene.Casa.PresenzaPannelliSolari': true
    }, 'UNICO', 'PUCASA'],
    'famiglia': [{
      'Ambito.Bene.Famiglia.Immobile.TipologiaAbitazione': '1',
      'Ambito.Proprietario.Residenza.NomeStrada': 'Via Per Manduria',
      'Ambito.Proprietario.Residenza.NumeroCivico': '21',
      'Ambito.Proprietario.Residenza.Comune': 'Manduria',
      'Ambito.Proprietario.Residenza.Provincia': "TA",
      'Ambito.Proprietario.Residenza.Cap': '74024',
      'Ambito.Proprietario.Residenza.Stato': 'Italia',
      'Ambito.Proprietario.Residenza.CodiceCatastaleComune': 'E882',
      'Ambito.Proprietario.TipoPersona': 'PF'
    }, 'UNICO', 'PUFAMIGLIA'],
    'viaggio': [{
      "Ambito.Bene.Viaggio.PaeseDestinazione": "1101",
      "Ambito.Bene.Viaggio.NumeroViaggiatori": "2",
      "Ambito.Bene.Viaggio.DataViaggioAndata": "1/11/2024",
      "Ambito.Bene.Viaggio.DataViaggioRitorno": "25/11/2024"
    }, 'UNICO', 'PUVIAGGI'],
    'infortuni': [{
      "Ambito.Proprietario.DataDiNascita": "08/04/1998",
      "Ambito.Proprietario.Professione": "1120",
      "Ambito.Proprietario.TipoOccupazione": "D",
      "Ambito.Proprietario.StatusFamiliare": "0",
      "Ambito.Proprietario.Residenza.NomeStrada": "Via per Manduria",
      "Ambito.Proprietario.Residenza.NumeroCivico": "21",
      "Ambito.Proprietario.Residenza.Comune": "Manduria",
      "Ambito.Proprietario.Residenza.Provincia": "TA",
      "Ambito.Proprietario.Residenza.Cap": "74024",
      "Ambito.Proprietario.Residenza.Stato": "Italia",
      "Ambito.Proprietario.TipoPersona": "PF"
    }, 'UNICO', 'PUINFORTUNI'],
    'salute': [{
      "Ambito.Proprietario.DataDiNascita": "08/04/1998",
      "Ambito.Proprietario.Professione": "9740",
      "Ambito.Proprietario.TipoOccupazione": "D",
      "Ambito.Proprietario.Residenza.NomeStrada": "Via per Manduria",
      "Ambito.Proprietario.Residenza.NumeroCivico": "21",
      "Ambito.Proprietario.Residenza.Comune": "Manduria",
      "Ambito.Proprietario.Residenza.Provincia": "TA",
      "Ambito.Proprietario.Residenza.Cap": "74024",
      "Ambito.Proprietario.Residenza.Stato": "Italia",
      "Ambito.Proprietario.TipoPersona": "PF"
    }, 'UNICO', 'PUSALUTE']
  }

  public constructor(
    private _statusService: StatusService
  ) {
  }

  public async effettuaChiamataMock(tipoChiamata: string): Promise<boolean>{
    let chiamataEffettuata = false;

    const data = this._mappaDatiChiamata[tipoChiamata];
    if(data)
      chiamataEffettuata = await this._effettuaChiamataMock(data[0], data[1], data[2]);

    return chiamataEffettuata;
  }

  private async _effettuaChiamataMock(data: GenericDataType, env: string, productType: string): Promise<boolean>{
    let chiamataEffettuata = false;

    try{
      TpdInterpreteDxApi.TpdInterpreteDxApiContext.setInitData(data);
      TpdInterpreteDxApi.TpdInterpreteDxApiContext.setEnv(env);
      TpdInterpreteDxApi.TpdInterpreteDxApiContext.setProductType(productType);
      await TpdInterpreteDxApi.TpdInterpreteDxApiContext.createRequest(PEGA_API.loadPage, IActionLoadRequest.create, 'Creazione chiamata mock');
      chiamataEffettuata = true;
    }catch (e){
      console.error("Non é stato possibile effettuare la chiamata mock", e);
    }

    return chiamataEffettuata;
  }
}
