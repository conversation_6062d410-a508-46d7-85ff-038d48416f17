import { Injectable } from "@angular/core";
import { Helpers } from "@tpd-web-common-libs/nodejs-library";

@Injectable({
  providedIn: 'root'
})
export default class OtpManagerService{
  private _tentativiRimasti = 0;
  private _tempoRimasto = 0;
  private _managerInizializzato = false;
  private _timerId: number;

  private _ultimoTentativoInErrore = false;

  public inizializzaManagerOtp(numeroTentativi: number, tempoDisponibile: number, callbackAggiornamento: () => void){
    this._tentativiRimasti = numeroTentativi;
    this._tempoRimasto = tempoDisponibile;
    this._startaIntervallo(callbackAggiornamento)
  }

  public collegaTimer(callbackAggiornamento: () => void){
    this._startaIntervallo(callbackAggiornamento);
  }

  private _startaIntervallo(callbackAggiornamento: () => void){
    this._fermaIntervallo();
    this._managerInizializzato = true;
    callbackAggiornamento && callbackAggiornamento();
    if(Helpers.EnvironmentHelper.isClientSide())
      this._timerId = window.setInterval(() => this._funzioneAggiornamentoTimer(callbackAggiornamento), 1000);
  }

  private _funzioneAggiornamentoTimer(callbackAggiornamento: () => void){
    if(this._tentativiRimasti > 0)
      this._tempoRimasto--;
    callbackAggiornamento && callbackAggiornamento();
    this._tempoRimasto === 0 && this._fermaIntervallo();
  }

  private _fermaIntervallo(){
    if(Helpers.EnvironmentHelper.isClientSide()){
      if(this._timerId){
        window.clearInterval(this._timerId);
        this._timerId = undefined;
      }
    }
    this._managerInizializzato = false;
  }

  public scalaTentativo(){
    this._tentativiRimasti--;
    if(this._tentativiRimasti < 0) {
      this._tentativiRimasti = 0;
      this._fermaIntervallo();
    }
    this._ultimoTentativoInErrore = true;
  }

  public get tenativiRimasti(): string{
    return this._tentativiRimasti.toString();
  }

  public get tempoRimasto(): string{
    const minuti = Math.floor(this._tempoRimasto / 60);
    const secondi = this._tempoRimasto - (minuti * 60);

    return `${minuti}:${secondi < 10 ? '0' : ''}${secondi}`;
  }

  public get tempoRimastoNumber(): number{
    return this._tempoRimasto;
  }

  public get isManagerInizializzato(): boolean{
    return this._managerInizializzato;
  }

  public set ultimoTentativoInErrore(value: boolean) {
    this._ultimoTentativoInErrore = value;
  }

  public get ultimoTentativoInErrore(): boolean{
    return this._ultimoTentativoInErrore;
  }
}
