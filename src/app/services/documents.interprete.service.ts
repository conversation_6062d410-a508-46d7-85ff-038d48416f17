import { Injectable } from '@angular/core';
import {
  BaseCommunicator,
  CommonFunctions,
  Request,
} from '@tpd-web-angular-libs/angular-library';
import { Helpers } from '@tpd-web-common-libs/nodejs-library';
import { MessagesService } from "./messages.service";

@Injectable({
  providedIn: 'root',
})
export class DocumentsService {
  constructor(
    private baseCommunicator: BaseCommunicator,
    private commonFunctions: CommonFunctions,
    private _messagesService: MessagesService
  ) {}

  public getDocument(url: string) {
    const headers = {
      'content-type': 'application/text',
      accept: 'application/text',
    };
    const request = new Request(url, { headers: headers });
    return this.baseCommunicator.get(request, false);
  }

  public downloadPdf(url: string, title: string) {
    this.getDocument(url)
      .then((response) => this.commonFunctions.downloadPDF(response as any, title))
      .catch((error) => console.error(error));
  }

  public getDocumentPu(href: string){
    const isUserLogged = Helpers.LoginHelper.userLogged

    const endPoint = '/documents/pu/document';
    const baseUrl = isUserLogged ? 'v1' : 'v1/unsecure';

    const request = new Request(
      `documenti/stampa/${baseUrl}${endPoint}?href=${href}&type=OUTBOUND_DYNAMIC&decryptRequested=true`
    );
    return this.baseCommunicator.get(request);
  }

  public downloadDocumentoPu(href: string, title: string){
    this._messagesService.setLoading(true);
    this.getDocumentPu(href).then(response => {
      this._messagesService.setLoading(false);
      const base64 = atob(response?.document);
      if (Helpers.EnvironmentHelper.isClientSide()) {
        const pdfWindow = window.open('');
        pdfWindow.document.head.innerHTML = `<title>${title}</title>`
        pdfWindow.document.body.innerHTML = "<iframe width='100%' height='100%' src='data:application/pdf;base64, " + encodeURI(base64) + "'></iframe>";
      }
    }).catch(e => {
      this._messagesService.setLoading(false);
      console.error("No é stato possibile scaricare il documento PU", e);
    });
  }
}
