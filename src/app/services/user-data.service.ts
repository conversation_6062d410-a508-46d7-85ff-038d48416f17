import { Injectable } from "@angular/core";
import { LocalStorageLayer } from "@tpd-web-angular-libs/angular-library";
import { Helpers } from "@tpd-web-common-libs/nodejs-library";
import { GenericDataType } from "../models/bff.interprete.model";
import { StatusService } from "./status.service";

@Injectable({
  providedIn: 'root'
})
export default class UserDataService{
  private _storedUserData: GenericDataType;

  public constructor(
    private _localStorageLayer: LocalStorageLayer
  ) {
  }

  public async resetStoredUserData(){
    const storedUserData = await this.getStoredUserData();
    this._storedUserData = storedUserData || {};
  }

  public async storeUserData(formValue: GenericDataType){
    this._storedUserData = {...this._storedUserData, ...await StatusService.GlobalStatusService.getDatiUtente(formValue)};
    StatusService.GlobalStatusService.resetReferencesContraente();

    try {
      await Helpers.SessionHelper.setData('dati_utente', this._storedUserData);
    } catch (error) {
      console.error('Errore durante la memorizzazione dei dati utente', String(error));
    }
  }

  public async getStoredUserData(){
    let esito = {};
    if(this._storedUserData)
      esito = this._storedUserData;
    else {
      try{
        esito = await Helpers.SessionHelper.getData('dati_utente');
      }catch (error){
        console.error('Errore nel recupero dei dati utente', String(error))
      }
    }

    if(StatusService.GlobalStatusService.modalitaRedirect){
      esito = {
        ...esito,
        /*'Contraente.CodiceFiscale': "****************",
        'Contraente.Nome': 'Luigi',
        'Contraente.Cognome': 'Ferraris'*/
      }
    }

    return esito;
  }

  public async storedCodiceFiscale(): Promise<string>{
    let codiceFiscale = undefined;

    if(Helpers.LoginHelper.userLogged){
      try{
        const data1 = await this._localStorageLayer.getData1();
        codiceFiscale = data1?.registry?.taxCodeVAT;
      }catch(e){
        console.error("Non é stato possibile recuperare il codice fiscale dal data1", e);
      }
    }else{
      const storedData = await this.getStoredUserData();
      if(storedData){
        const key = Object.keys(storedData).find(key => key.toLowerCase().includes('codicefiscale'));
        if(key)
          codiceFiscale = storedData[key];
      }
    }

    return codiceFiscale;
  }

  public async clearStoredData(){
    await Helpers.SessionHelper.deleteData('dati_utente');
  }
}
