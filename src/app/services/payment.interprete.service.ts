import { Injectable } from '@angular/core';
import {
  CommonFunctions,
  DatiDiPagamento,
  DatiDiPagamentoXPayBuild,
  PagamentoResponse,
  TpdPagamentiService,
} from '@tpd-web-angular-libs/angular-library';
import { LoginHelper } from '@tpd-web-common-libs/nodejs-library/dist/src/helpers';
import { Subject } from 'rxjs';
import { AnalyticsInterprete } from './analytics.interprete.service';
import { MessagesService } from './messages.service';
import { Helpers } from '@tpd-web-common-libs/nodejs-library';
import { IStoragePaymentData } from "../utils/payment-utils";
import { StatusService } from "./status.service";

@Injectable({
  providedIn: 'root',
})
export class PaymentInterpreteService {
  public static ErrorMessage = "L'operazione non è andata a buon fine";

  constructor(
    private _messageService: MessagesService,
    private _tpdPagamentiService: TpdPagamentiService,
    private _analyticsService: AnalyticsInterprete,
    private _statusService: StatusService,
    private _commonFunctions: CommonFunctions
  ) {}

  private callbackRicorrente$ = new Subject<PagamentoResponse>();
  public callbackRicorrente = this.callbackRicorrente$.asObservable();

  private callbackError$ = new Subject<any>();
  public callbackError = this.callbackError$.asObservable();

  public async getPaymentData(): Promise<IStoragePaymentData>{
    let esito;

    try {
      const paymentProductType = await Helpers.SessionHelper.getData('Payment_ProductType');
      await Helpers.SessionHelper.deleteData('Payment_ProductType');
      const paymentKey = paymentProductType ? `Payment_Data_${paymentProductType}` : '';
      esito = await this.getStorageDataPayment(paymentKey);
      await this.removeStorageDataPayment(paymentKey);
    }catch (error){
      console.error("Non é stato possibile recuperare i dati di pagamento", String(error));
    }

    return esito;
  }

  public getPaymentResponseUrlData(): {esito: string, codiceEsito: string, messaggio: string, xpayNonce}{
    const esito = this._commonFunctions.getParameterByName('esito');
    const codiceEsito = this._commonFunctions.getParameterByName('codiceEsito');
    const messaggio = this._commonFunctions.getParameterByName('messaggio');
    const xpayNonce = this._commonFunctions.getParameterByName('xpayNonce');

    return {esito, codiceEsito, messaggio, xpayNonce};
  }

  public async abilitaPagamenti() {
    if (LoginHelper.userLogged) {
      try{
        await this._tpdPagamentiService.pagamenti.abilitaPagamento();
        console.warn("Pagamenti abilitati per l'utente");
      }catch(error){
        console.error("Non é stato possibile abilitare i pagamenti per l'utente", error);
      }
    }
  }

  public autorizzaPagamento(
    nonceRequestId: string,
    paymentService: string,
    paymentMethodKey: string,
    importo: string,
    urlRisposta: string
  ) {
    this._tpdPagamentiService.pagamenti.autorizza(
      nonceRequestId,
      paymentService as any,
      paymentMethodKey,
      importo,
      urlRisposta
    ).then((response: {html: string}) => {
      this._messageService.setLoading(false);
      this.redirect3DSecure(response.html);
    }).catch(() => {
      this._messageService.setLoading(false);
      this._analyticsService.sendAnalyticsPaymentError(
        ['utente'],
        ['11'],
        [PaymentInterpreteService.ErrorMessage],
        ['pagamento']
      );
      this._messageService.setError(true);
    })
  }

  /**
   * Redirect PSD2 - 3D Secure Nexi
   * @param nexiHtml Html da scrivere
   */
  public redirect3DSecure(nexiHtml: string) {
    if (Helpers.EnvironmentHelper.isClientSide()) {
      window.document.write(nexiHtml);
    }
  }

  private async _acquistaOk(datiPagamento: DatiDiPagamento, pagamentoResponse: PagamentoResponse){
    if(pagamentoResponse && datiPagamento){
      this._messageService.setLoading(false);
      if (datiPagamento.mezzoPagamentoRicorrente) {
        //this.statusService.pagamentoApplePay = isApplePay;
        if (pagamentoResponse.esitoCodice === '000')
          this.callbackRicorrente$.next(pagamentoResponse);
        else this.callbackError$.next(pagamentoResponse);
      } else if (
        datiPagamento.mezzoPagamentoOneShot &&
        pagamentoResponse.urlPagamento
      ) {
        Helpers.RouterHelper.goTo(pagamentoResponse.urlPagamento);
      } else if(
        datiPagamento.mezzoPagamentoOneShot
      ) {
        //Gestisce il pagamento concluso con l'xpayBuild
        Helpers.RouterHelper.goTo(`${window.location.href}?esito=OK`);
      } else {
        console.error("Errore nella procedura di acquisto");
        this._messageService.setLoading(false);
        this._analyticsService.sendAnalyticsPaymentError(
          ['utente'],
          ['11'],
          [PaymentInterpreteService.ErrorMessage],
          ['pagamento']
        );
        this._messageService.setError(true);
      }
    }else{
      console.error("Non é stato recuperata la response di pagamento o mancano i dati");
    }
  }

  private async _acquistaKo(datiPagamento: DatiDiPagamento, errore: any){
    console.error("Errore nella procedura di acquisto KO");
    if(datiPagamento){
      datiPagamento.mezzoPagamentoRicorrente && this.callbackError$.next(errore);
      this._messageService.setLoading(false);
      this._analyticsService.sendAnalyticsPaymentError(
        ['utente'],
        ['11'],
        [PaymentInterpreteService.ErrorMessage],
        ['pagamento']
      );
      this._messageService.setError(true);
    }else{
      console.error("Non sono stati recuperati i dati di pagamento");
    }
  }

  public async acquista(
    datiDiPagamento: DatiDiPagamento,
    versione: string,
    idPreventivo: string,
    codiceFiscale: string,
    isApplePay = false,
    datiDiPagamentoXPayBuild?: DatiDiPagamentoXPayBuild
  ) {
    this._messageService.setLoading(true);
    try{
      if(!LoginHelper.userLogged)
        datiDiPagamento.datiRiconciliazione = { codiceFiscale };
      const response = await (LoginHelper.userLogged ? this._tpdPagamentiService.pagamenti.acquista : this._tpdPagamentiService.pagamenti.acquistaAP)(idPreventivo, versione, datiDiPagamento, datiDiPagamentoXPayBuild);
      this._acquistaOk(datiDiPagamento, response);
    }catch(errore){
      this._acquistaKo(datiDiPagamento, errore);
    }
  }

  //Storage setters & getters

  public async setStorageDataPayment(key: string, data: IStoragePaymentData) {
    await Helpers.SessionHelper.setData(key, JSON.stringify(data));
  }

  public async getStorageDataPayment(key: string): Promise<IStoragePaymentData> {
    try {
      const storageData = await Helpers.SessionHelper.getData(key);
      return storageData ? JSON.parse(storageData) : undefined;
    } catch (errore) {
      console.error('Non é stato possibile recuperare i dati dallo storage');
    }
  }

  public async removeStorageDataPayment(key: string) {
    await Helpers.SessionHelper.deleteData(key);
  }

  public async salvaProductTypePagamento(){
    try{
      await Helpers.SessionHelper.setData(
        'Payment_ProductType',
        this._statusService.configInterprete.productType
      );
    }catch (errore){
      console.error("Non é stato possibile salvare il productType", errore);
    }
  }

  public async eliminaProductTypePagamento(){
    await Helpers.SessionHelper.deleteData('Payment_ProductType');
  }
}
