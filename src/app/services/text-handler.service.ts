import { Injectable } from '@angular/core';
import ColorsService from './colors.service';

export interface TextProps {
  color: string;
  fontFamily: string;
  weight: string;
  transformation?: string;
  size: string;
  alignment?: string;
}

export interface TextConfig {
  desktop: TextProps;
  tablet: TextProps;
  mobile: TextProps;
}

export interface TextCss {
  desktopCss: CssElement;
  tabletCss: CssElement;
  mobileCss: CssElement;
}

export interface CssElement {
  color: string;
  fontSize: string;
  fontWeight: string;
  fontFamily: string;
  textAlign: string;
  textDecoration?: string;
  textTransform?: string;
  fontStyle?: string;
}

@Injectable({
  providedIn: 'root',
})
export class TextHandlerService {

  public constructor(private _colorService: ColorsService) {}

  public getTextCss(str: string): TextCss | null {
    try{
      if(str.split("STYLE").length === 2)
        str = str.split("STYLE")[1].trim();

      const textConfig = this.getTextConfig(str);
      if (textConfig) {
        const desktopCss = this.getCssFromProps(textConfig.desktop);
        const tabletCss = this.getCssFromProps(textConfig.tablet);
        const mobileCss = this.getCssFromProps(textConfig.mobile);
        return {
          desktopCss: desktopCss,
          tabletCss: tabletCss,
          mobileCss: mobileCss,
        };
      }
    }catch(e){
      //console.error("Non é stato possibile recuperare il css dal formato passato", e);
    }
    return null;
  }

  public formattaValuta(valuta: string): string{
    let esito = '0';

    const value = parseFloat(valuta);
    if(!isNaN(value) && value !== 0){
      const euro = new Intl.NumberFormat('it-IT', {
        style: 'currency',
        currency: 'EUR'
      });
      esito = euro.format(value).replace('€', '').trim();
    }

    return esito;
  }

  private getCssFromProps(props: TextProps): CssElement {
    const css: CssElement = {
      color: `${props.color}`,
      fontSize: `${props.size}`,
      fontFamily: `${props.fontFamily}`,
      fontWeight: `${props.weight}`,
      textAlign: `${props.alignment}`,
    };
    if (props.transformation) {
      if (props.transformation.includes('text-decoration')) {
        const textDecoration =
          props.transformation.split('text-decoration: ')[1];
        css['textDecoration'] = textDecoration;
      } else if (props.transformation.includes('text-transform')) {
        const textTransform = props.transformation.split('text-transform: ')[1];
        css['textTransform'] = textTransform;
      } else {
        const fontStyle = props.transformation.split('font-style: ')[1];
        css['fontStyle'] = fontStyle;
      }
    }
    return css;
  }

  private getTextConfig(text: string): TextConfig | null {
    if (
      text &&
      (text.includes('Text') || text.includes('TEXT')) ||
      (text.includes(' WEB ') || text.includes(' web '))
    ) {
      const textWebConfig = text.split('WEB ')[1];
      if (textWebConfig) {
        const text = textWebConfig.split(' ');
        const desktopConfig = this.getTextProps(text[0]);
        const tabletConfig = this.getTextProps(text[1]);
        const mobileConfig = this.getTextProps(text[2]);

        const textConfig: TextConfig = {
          desktop: desktopConfig,
          tablet: tabletConfig,
          mobile: mobileConfig,
        };
        return textConfig;
      }
    }
    return null;
  }

  private getTextProps(config: string): TextProps {
    const color = this.getTextColor(config.slice(0, 2));
    const weight = this.getTextWeight(config.charAt(2));
    let size = '0';
    let transformation = '';
    if (!this.is_numeric(config[config.length - 1])) {
      transformation = this.getTextTransformation(
        config.charAt(config.length - 1)
      );
    }
    if (this.is_numeric(config[3])) {
      size = this.getFontSize(config, 3);
    } else {
      transformation = this.getTextTransformation(config.charAt(3));
      size = this.getFontSize(config, 4);
    }
    const alignment = this.getTextAlignment(config);

    const textConfig: TextProps = {
      color: color,
      ...weight,
      size: size,
      transformation: transformation,
      alignment: alignment,
    };

    return textConfig;
  }

  private is_numeric(str: string): boolean {
    return /^\d+$/.test(str);
  }

  private getTextColor(color: string): string {
    return this._colorService.getColor(color);
  }

  private getTextWeight(peso: string): {weight: string, fontFamily: string} {
    const weight = TEXT_WEIGHT[peso] ?? TEXT_WEIGHT.DEFAULT;
    const fontFamily = TEXT_FONT[peso] ?? TEXT_FONT.DEFAULT;
    return {weight, fontFamily};
  }

  private getFontSize(str: string, startingIndex: number): string {
    let size = '';
    for (let i = startingIndex; i < str.length; i++) {
      if (this.is_numeric(str[i])) {
        size = `${size}${str[i]}`;
      } else break;
    }
    return `${size}px`;
  }

  private getTextAlignment(str: string): string {
    const lastChar = str.charAt(str.length - 1);
    const alignment = this.is_numeric(lastChar) ? '' : lastChar;
    const textAligment = (TEXT_ALIGNMENT as any)[alignment];
    return textAligment ? textAligment : TEXT_ALIGNMENT.DEFAULT;
  }

  private getTextTransformation(transformation: string): string {
    const textTransformation = (TEXT_TRANSFORMATION as any)[transformation];
    return textTransformation ? textTransformation : '';
  }
}

export const TEXT_WEIGHT = {
  E: '100',
  L: 'Normal',
  M: '500',
  B: 'Bold',
  DEFAULT: 'Normal',
};

export const TEXT_FONT = {
  E: 'Unipol',
  L: 'Unipol',
  M: 'Unipol Medium',
  B: 'Unipol Bold',
  DEFAULT: 'Unipol',
}

export const TEXT_TRANSFORMATION = {
  S: 'text-decoration: line-through',
  N: 'text-decoration: underline',
  U: 'text-transform: uppercase',
  I: 'font-style: italic',
};

export const TEXT_ALIGNMENT = {
  R: 'right',
  C: 'center',
  DEFAULT: 'left',
};
