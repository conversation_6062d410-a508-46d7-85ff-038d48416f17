import { Injectable } from '@angular/core';
import {
  CommonNetworkService,
  Helpers,
} from '@tpd-web-common-libs/nodejs-library';
import { Data } from '@angular/router';
import { InterpreteService } from "./bffinterprete.service";

const CART_URL_CONSTANTS = {
  BASE_URL_SECURE: '/api/priv/acquisto/',
  BASE_URL_UNSECURE: '/api/pub/acquisto/',
  URL_SECURE: `v1/secure/`,
  URL_UNSECURE: `v1/unsecure/`,
}

export interface IActiveCart {
  AssignmentID: string;
  CaseID: string;
  NumeroItems: number;
}

@Injectable({
  providedIn: 'root',
})
export class CartService {

  public recuperaCarrelloAttivo<T = Data>(cf: string): Promise<T> {
    const completeUrl = `${Helpers.LoginHelper.userLogged ? CART_URL_CONSTANTS.BASE_URL_SECURE : CART_URL_CONSTANTS.BASE_URL_UNSECURE}${Helpers.LoginHelper.userLogged ? CART_URL_CONSTANTS.URL_SECURE : CART_URL_CONSTANTS.URL_UNSECURE}recuperaCarrelloAttivo?codiceFiscale=${cf}`;

    const commonNetworkService = InterpreteService.redirectedCommonNetworkService();
    return commonNetworkService.get<T>(InterpreteService.getRedirectedUrl(completeUrl));
  }

  public async recuperaQuantitaItemCarrelloAttivo(cf: string): Promise<number>{
    let esito = -1;

    const carrelloAttivo = await this.recuperaCarrelloAttivo(cf);
    if(carrelloAttivo){
      esito = 0;
      if(carrelloAttivo.CarrelloAttivo && carrelloAttivo.CarrelloAttivo.length > 0){
        esito = carrelloAttivo.CarrelloAttivo[0].NumeroItems;
      }
    }

    return esito;
  }
}
