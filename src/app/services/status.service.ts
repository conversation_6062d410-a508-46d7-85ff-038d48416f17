import { Injectable } from '@angular/core';
import { AbstractControl, FormArray, FormGroup } from '@angular/forms';
import { Helpers } from '@tpd-web-common-libs/nodejs-library';
import { cloneDeep } from 'lodash';
import { BehaviorSubject, Subject } from 'rxjs';
import { AppProps } from '../app.props';
import { ConfigInterpreteDxAPI } from '../models/config-interprete';
import { IField } from "../models/models.commons";
import { GenericDataType } from "../models/bff.interprete.model";
import UserDataService from "./user-data.service";
import { CartService } from "./cart.service";

export interface ListenerErroreFormInterface{
  rimuoviListenerDiErroreInputInvalido: () => void
}

@Injectable({
  providedIn: 'root',
})
export class StatusService {
  public static GlobalStatusService: StatusService;

  constructor(
    private _cartService: CartService,
    private _userDataService: UserDataService
  ) {
    StatusService.GlobalStatusService = this;
  }
  private _props$: BehaviorSubject<AppProps | undefined> = new BehaviorSubject(undefined);
  private _assignmentId!: string;
  private _caseID!: string;
  private tooltipElementContainer: any = {};
  private stateAccordion1: any = {};
  private statoAccordionPostalizzazione = {};
  private _configInterprete!: ConfigInterpreteDxAPI;

  public clearForm = false;
  public props = this._props$.asObservable();

  // props
  setProps(props: AppProps) {
    this._props$.next(props);
  }

  // assignmentn ID
  set assignmentId(id: string) {
    this._assignmentId = id;
  }

  get assignmentId() {
    return this._assignmentId;
  }

  // caseID
  set caseID(id: string) {
    this._caseID = id;
  }

  get caseID() {
    return this._caseID;
  }

  //region Gestione Captcha

  private _captchaToken!: string;

  public set captchaToken(token: string) {
    this._captchaToken = token;
  }

  public get captchaToken() {
    return this._captchaToken;
  }

  //end-region

  //region GestioneBackRoc
  private _assignmentOrigineRoc: string;
  private _useAssignmentOrigineRoc = false;

  public setAssignmentOrigineRoc(assignmentOrigine: string){
    this._assignmentOrigineRoc = assignmentOrigine;
  }

  public getAssignmentOrigineRoc(): string | undefined{
    return this._useAssignmentOrigineRoc ? this._assignmentOrigineRoc : undefined;
  }

  public getCaseIdOrigineRoc(): string | undefined{
    let esito = this.getAssignmentOrigineRoc();

    if(esito !== undefined){
      try {
        esito = esito.replace('ASSIGN-WORKLIST ', '').split('!')[0];
      }catch (e){
        esito = undefined;
        console.error("Non é stato possibile recuperare il case di origine dall'Assignemt di origine ROC", e);
      }
    }

    return esito;
  }

  public engageAssignmentOrigineRoc(){
    this._useAssignmentOrigineRoc = true;
  }

  public disengageAssignmentOrigineRoc(){
    this._useAssignmentOrigineRoc = false;
  }

  public resetAssignmentOrigineRoc(){
    this._assignmentOrigineRoc = undefined;
    this._useAssignmentOrigineRoc = false;
  }

  //endregion

  //region Form
  private _formGroupIstance: FormGroup;

  public initFormGroupInstance(input: FormGroup) {
    this._formGroupIstance = input;
  }

  public initFormArrayInstance(input: FormArray) {
    this._formArrayIstance = input;
  }

  public getFormGroup() {
    return this._formGroupIstance;
  }

  public getFormArray() {
    return this._formArrayIstance;
  }

  public getControlsValueOfFormArray() {
    let controlsValue = {};
    for (let i = 0; i < this._formArrayIstance.controls.length; i++) {
      if ((this._formArrayIstance.at(i) as FormGroup).contains('ProcessManagement.Otp')) {
        const valueOTP = cloneDeep(this._formArrayIstance.at(i).value);
        controlsValue = { ...controlsValue, ...valueOTP };
      }
      for (const prop in (this._formArrayIstance.at(i) as FormGroup).controls) {
        if ((this._formArrayIstance.at(i) as FormGroup).controls[prop].valid || prop.includes('Indirizzo')) {
          const singleValue = cloneDeep((this._formArrayIstance.at(i) as FormGroup).controls[prop].value);
          (controlsValue as any)[prop] = singleValue;
        }
      }
    }

    return controlsValue;
  }

  getFormGroupValue() {
    const controlsValue = {};
    const controls = this.getFormGroup().controls;
    for (const name in controls) {
      if (controls[name].valid || name.includes('Indirizzo')) {
        (controlsValue as any)[name] = controls[name].value;
      }
    }
    return controlsValue;
  }

  public hasControlName(controlName: string): FormGroup | false {
    let esito: FormGroup | false = false;

    if (this._formArrayIstance) {
      for (let i = 0; i < this._formArrayIstance.length && esito === false; i++) {
        const test: FormGroup = this._formArrayIstance.at(i) as FormGroup;
        if (test.contains(controlName)) esito = test;
      }
    }

    if (esito === false && this._formGroupIstance.contains(controlName))
      esito = this._formGroupIstance;

    return esito;
  }

  public addControlFormGroup(controlName: string, control: AbstractControl, standAloneFormIndex?: number) {
    const testGroup: FormGroup | false = this.hasControlName(controlName);
    if (testGroup !== false) {
      const oldControl = testGroup.controls[controlName];
      control.setValue(oldControl.value);
      testGroup.removeControl(controlName);
      testGroup.addControl(controlName, control);
    } else {
      let formGroup: FormGroup;
      if (this.standAloneForm && this._formArrayIstance.at(this.standAloneFormIndex))
        formGroup = this._formArrayIstance.at(this.standAloneFormIndex) as FormGroup;
      else if (standAloneFormIndex !== undefined && this._formArrayIstance.at(standAloneFormIndex))
        formGroup = this._formArrayIstance.at(standAloneFormIndex) as FormGroup;
      else formGroup = this._formGroupIstance;

      formGroup.addControl(controlName, control);
    }
  }

  public removeControlFormGroup(controlName: string) {
    if (this._formArrayIstance) {
      this._formArrayIstance.controls.forEach((element, index) => {
        (element as FormGroup).removeControl(controlName);
      });
    }
    this._formGroupIstance.removeControl(controlName);
  }

  public isValidForm(): boolean {
    let formArrayCondition = true;
    if (this.getFormArray())
      formArrayCondition = this.getFormArray().status === 'VALID';
    return this.getFormGroup().status === 'VALID' && formArrayCondition;
  }

  //endregion

  //region StandAloneForm

  public standAloneForm = false;
  public standAloneFormIndex: number = undefined;
  private _formArrayIstance: FormArray;

  public inizializzaFormIndipendente(){
    if(this.standAloneFormIndex === undefined)
      this.standAloneFormIndex = 0;
    else this.standAloneFormIndex ++;
    this.standAloneForm = true;
    this._formArrayIstance.push(new FormGroup({}));
  }

  public deinizializzaFormIndipendente(){
    this.standAloneForm = false;
  }

  public resetStatoFormIndipendenti(){
    this.standAloneFormIndex = undefined;
    this.standAloneForm = false;
    this._formArrayIstance.clear();
  }

  public getFormArrayChildByIndex(index: number) {
    return this._formArrayIstance.at(index);
  }

  public get currentStandAloneFormIndex(): number{
    let esito = -1;

    if(this.standAloneForm && this.standAloneFormIndex !== undefined)
      esito = this.standAloneFormIndex;

    return esito;
  }

  //endregion

  // payment service
  set configInterprete(input: ConfigInterpreteDxAPI) {
    this._configInterprete = input;
  }

  get configInterprete() {
    return this._configInterprete;
  }

  // tooltip cache

  addTooltipElement(tooltipID: string, tooltipGroups: any) {
    this.tooltipElementContainer[tooltipID] = tooltipGroups;
  }

  getTooltipElemByID(tooltipID: string) {
    return this.tooltipElementContainer[tooltipID];
  }

  resetTooltipElement() {
    this.tooltipElementContainer = {};
  }

  // accordion garanzie status
  setStatusAccordionGaranzie(titleId: string, isOpen: boolean) {
    this.stateAccordion1[titleId] = isOpen;
  }

  getStatusAccordionGaranzieByID(titleId: string) {
    return this.stateAccordion1[titleId];
  }

  // accordion postalizzazione status
  setStatoAccordionPostalizzazione(titleId: string, isOpen: boolean) {
    (this.statoAccordionPostalizzazione as any)[titleId] = isOpen;
  }

  getStatoAccordionPostalizzazioneByID(titleId: string) {
    return (this.statoAccordionPostalizzazione as any)[titleId];
  }

  // status firma
  private _numberFailed!: number;
  getNumberFailed(maxAttempts: any) {
    if (isNaN(this._numberFailed)) this._numberFailed = maxAttempts;

    return this._numberFailed;
  }

  failedValidationOTP(outcomeValidation: 'OK' | '' | 'KO') {
    if (this._numberFailed && outcomeValidation === 'KO') this._numberFailed--;
  }

  resetNumberFail() {
    this._numberFailed = 0; //undefined
  }

  private timerIsRunning = false;
  private timerOTP: any;
  private timerEvent: Subject<any> = new Subject();

  observableTimer = this.timerEvent.asObservable();

  setTimerOTP(startTimer: string) {
    if (!this.timerIsRunning) {
      let timeleft = parseFloat(startTimer);
      clearInterval(this.timerOTP);
      if (typeof document === 'object') {
        const countdown = document.getElementById('countdown');
        this.timerOTP = setInterval(() => {
          if (timeleft <= 0) {
            this.timerIsRunning = false;
            this.timerEvent.next(this.timerIsRunning);
            if (countdown) {
              countdown.innerHTML = `Codice valido ancora per 0:00`;
            }
            clearInterval(this.timerOTP);
            // close modal
          } else {
            this.timerIsRunning = true;
            this.timerEvent.next(this.timerIsRunning);
            const minutesLeft = Math.floor(timeleft / 60);
            let seconds: any = timeleft - minutesLeft * 60;
            seconds <= 9 ? (seconds = `0${seconds}`) : seconds;
            if (countdown) {
              countdown.innerHTML = `Codice valido ancora per ${minutesLeft}:${seconds}`;
            }
          }
          timeleft -= 1;
        }, 1000);
      }
    }
  }

  resetTimerOTP() {
    this.timerIsRunning = false;
    this.timerOTP && clearInterval(this.timerOTP);
  }

  //region Gestione e propagazione degli errori nei form

  private _listenerErroriInputInvalidoUniqueIndex = 0;
  private _listenerErroriInputInvalido = new Map<number, {id: number, callback: (statoErrore: boolean) => void}[]>();

  public registraListenerDiErroreInputInvalido(standAloneFormIndex: number, callback: (statoErrore: boolean) => void): ListenerErroreFormInterface{
    const uniqueIndex = this._listenerErroriInputInvalidoUniqueIndex++;
    const standAloneIndex = standAloneFormIndex === undefined ? -1 : standAloneFormIndex;
    if(!this._listenerErroriInputInvalido.has(standAloneIndex))
      this._listenerErroriInputInvalido.set(standAloneIndex, []);

    this._listenerErroriInputInvalido.get(standAloneIndex).push({id: uniqueIndex, callback});
    return {
      rimuoviListenerDiErroreInputInvalido: () => {
        if(this._listenerErroriInputInvalido.has(standAloneIndex))
          this._listenerErroriInputInvalido.set(standAloneIndex, this._listenerErroriInputInvalido.get(standAloneFormIndex).filter(l => l.id !== uniqueIndex));
      }
    }
  }

  public impostaStatoErroreInput(statoErrore: boolean, formIndex: number): void {
    if(formIndex !== -1){
      if(this._listenerErroriInputInvalido.has(formIndex)){
        for(const l of this._listenerErroriInputInvalido.get(formIndex))
          l.callback && l.callback(statoErrore);
      }
    }else{
      for(const formListenerGroup of Array.from(this._listenerErroriInputInvalido)){
        for(const l of formListenerGroup[1])
          l.callback && l.callback(statoErrore);
      }
    }

  }

  //endregion

  //region ReplaceActionId (Nelle situazioni in cui su un popup dobbiamo mandare l'action sottostante)

  private _replaceActionId: string;

  public hasReplaceActionId(): boolean{
    return this._replaceActionId !== undefined;
  }

  public setReplaceActionId(replaceActionId: string){
    this._replaceActionId = replaceActionId;
  }

  public useReplaceActionId(): string{
    const data = this._replaceActionId;
    this._replaceActionId = undefined
    return data;
  }

  //endregion

  //region RecuperoDatiContraente (Per il recupero delle reference che servono per inviare i dati dell'utente)

  private _referencesContraenteStorage = {
    cf: '',
    nome: '',
    cognome: '',
    cellulare: '',
    email: '',
  };

  public setReferencesContraente(field: IField){
    console.warn("Settaggio riferimenti contraente");
    if(field?.customAttributes){
      if (field?.customAttributes.memorizzaCFContraente)
        this._referencesContraenteStorage.cf = field.reference;
      if (field?.customAttributes.memorizzaNomeContraente)
        this._referencesContraenteStorage.nome = field.reference;
      if (field?.customAttributes.memorizzaCognomeContraente)
        this._referencesContraenteStorage.cognome = field.reference;
      if (field?.customAttributes.memorizzaCellulareContraente)
        this._referencesContraenteStorage.cellulare = field.reference;
      if (field?.customAttributes.memorizzaEmailContraente)
        this._referencesContraenteStorage.email = field.reference;
    }
  }

  public async getDatiUtente(formValue: Record<string, any>): Promise<Record<string, any>> {
    const datiUtente = {};
    if(this._referencesContraenteStorage.cf.length !== 0 && formValue[this._referencesContraenteStorage.cf])
      await this.inizializzaStatoCarrelloAttivo(formValue[this._referencesContraenteStorage.cf]);
    for (const key in this._referencesContraenteStorage) {
      const reference = this._referencesContraenteStorage[key];
      if (formValue[reference])
        datiUtente[reference] = formValue[reference];
    }
    return datiUtente;
  }

  public resetReferencesContraente() {
    this._referencesContraenteStorage.cf = '';
    this._referencesContraenteStorage.nome = '';
    this._referencesContraenteStorage.cognome = '';
    this._referencesContraenteStorage.email = '';
    this._referencesContraenteStorage.cellulare = '';
  }

  //endregion

  //region SingleReference (Quando sulle update page é necessario inviare a PEGA solo le informazioni del campo inviato)

  private _referenceSingolo: string;

  public set referenceSingolo(referenceSingolo: string){
    this._referenceSingolo = referenceSingolo;
  }

  public get isReferenceSingolo(): boolean{
    return this._referenceSingolo && this._referenceSingolo.length > 0;
  }

  public filtraReferenceSingoli(referenceDaFiltrare: {[key: string]: unknown}): {[key: string]: unknown}{
    let esito = {};

    if(this.isReferenceSingolo){
      for(const key of Object.keys(referenceDaFiltrare)){
        if(key === this._referenceSingolo){
          esito[key] = referenceDaFiltrare[key];
          this._referenceSingolo = undefined;
          break;
        }
      }
    }else esito = referenceDaFiltrare;

    return esito;
  }

  //endregion

  //region submitCallback (Utile quando é necessario applicare per determinate reference delle azioni al momento della costruzione del payload da inviare)

  private _submitCallbacks: {reference: string, callback: (value: unknown) => unknown}[] = [];

  public aggiungiSubmitCallback(reference: string, callback: (value: unknown) => unknown){
    this._submitCallbacks.push({reference, callback});
  }

  public applicaSubmitCallback (referenceDaFiltrare: {[key: string]: unknown}): {[key: string]: unknown}{
    for(const key of Object.keys(referenceDaFiltrare)){
      const callback = this._submitCallbacks.find(callback => callback.reference === key);
      if(callback){
        referenceDaFiltrare[key] = callback.callback(referenceDaFiltrare[key]);
      }
    }

    this._submitCallbacks = [];
    return referenceDaFiltrare;
  }

  //endregion

  //region ModalitaRedirect (Per il debug sulla porta locale)

  public get modalitaRedirect(): boolean{
    let esito = false;

    if(Helpers.EnvironmentHelper.isClientSide() && window.location.href.includes('redirect=true')){
      esito = true;
    }

    return esito;
  }

  public get clearRestoreCaseDataMode(): boolean{
    let esito = false;

    if(Helpers.EnvironmentHelper.isClientSide() && window.location.href.includes('clear=true')){
      esito = true;
    }

    return esito;
  }

  //endregion

  //region Indirizzo( Quando bisogna inviare solo i dati del form indirizzo )

  private _inviaReferenceIndirizzo = false;
  private _referenceIndirizzo: string[];

  public get referenceIndirizzo(): string[]{
    this._inviaReferenceIndirizzo = false;
    const referenceIndirizzo = this._referenceIndirizzo;
    this._referenceIndirizzo = undefined;

    return referenceIndirizzo;
  }

  public set referenceIndirizzo(referenceIndirizzo: string[]){
    this._inviaReferenceIndirizzo = true;
    this._referenceIndirizzo = referenceIndirizzo;
  }

  public get inviaReferenceIndirizzo(): boolean{
    return this._inviaReferenceIndirizzo;
  }

  public filtraReferenceIndirizzo(reference: GenericDataType): GenericDataType{
    let esito: GenericDataType = reference;

    if(this.inviaReferenceIndirizzo){
      const filtro = this.referenceIndirizzo;
      esito = {};
      for(const key of filtro){
        esito[key] = reference[key];
      }
    }

    return esito;
  }

  //endregion

  //region Stato carrello attivo

  private _esecuzioneInizializzazione = false;
  private _statoCarrelloAttivoInizializzato = false;
  private _ultimoStatoRecuperatoCarrelloAttivo = 0;

  public async inizializzaStatoCarrelloAttivo(storedCf?: string){
    if(!this._statoCarrelloAttivoInizializzato && !this._esecuzioneInizializzazione) {
      this._esecuzioneInizializzazione = true;
      try{
        const cf = storedCf || await this._userDataService.storedCodiceFiscale();
        if(cf){
          const numeroItem = await this._cartService.recuperaQuantitaItemCarrelloAttivo(cf);
          if(numeroItem >= 0){
            this._statoCarrelloAttivoInizializzato = true;
            this._ultimoStatoRecuperatoCarrelloAttivo = numeroItem;
          }
        }
      }catch (e){
        console.log("Non é stato possibile inizializzare lo stato del carrello attivo", e);
      }
      this._esecuzioneInizializzazione = false;
    }
  }

  public async itemAggiuntoAlCarrello(): Promise<boolean>{
    let esito = false;

    if(this._statoCarrelloAttivoInizializzato){
      try {
        const cf = await this._userDataService.storedCodiceFiscale();
        if(cf){
          const numeroItem = await this._cartService.recuperaQuantitaItemCarrelloAttivo(cf);
          if(numeroItem >= 0){
            esito = numeroItem > this._ultimoStatoRecuperatoCarrelloAttivo;
          }
        }
      }catch (e){
        console.error("Non é stato possibile determinare l'aggiunta di un elemento al carrello", e)
      }
    }

    return esito;
  }

  //endregion
}
