import { Injectable } from "@angular/core";

@Injectable({
  providedIn: 'root'
})
export default class PaddingService{
  public trasformaPaggingPegaToPaddingCss(paddingPega: string): string{
     let esito = "0px";

     const splittedValues = paddingPega.split(' ');
     switch (splittedValues.length){
       case 4:
         esito = `${splittedValues[1]}px ${splittedValues[2]}px ${splittedValues[3]}px ${splittedValues[0]}px`
         break;
       case 2:
         esito = `${splittedValues[1]}px ${splittedValues[0]}px`;
         break;
       case 1:
         esito = `${splittedValues[0]}px`;
         break;
     }

     return esito;
  }
}
