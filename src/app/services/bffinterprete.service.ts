import { Injectable } from '@angular/core';
import { Data } from '@angular/router';
import {
  Request,
} from '@tpd-web-angular-libs/angular-library';
import { LoginHelper } from '@tpd-web-common-libs/nodejs-library/dist/src/helpers';
import {
  GenericDataType,
  IGoNextPageRequest,
  ILoadPageRequest,
  IUpdatePageRequest
} from "../models/bff.interprete.model";
import { CommonNetworkService } from "@tpd-web-common-libs/nodejs-library";
import { StatusService } from "./status.service";

export enum PEGA_API {
  loadPage,
  nextPage,
  updatePage
}

@Injectable({ providedIn: 'root' })
export class InterpreteService {
  public static redirectSit = false;

  public mappaChiamateApi = {
    [PEGA_API.loadPage]: (request: ILoadPageRequest | GenericDataType) => {
      return this.loadPage(request as ILoadPageRequest);
    },
    [PEGA_API.nextPage]: (request: IGoNextPageRequest | GenericDataType) => {
      return this.nextPage(request as IGoNextPageRequest);
    },
    [PEGA_API.updatePage]: (request: IUpdatePageRequest | GenericDataType) => {
      return this.updatePage(request as IUpdatePageRequest);
    },
  };

  public constructor(
    private _statusService: StatusService,
  ) {
  }

  private _generateBaseUrl(pegaApi: PEGA_API): string{
    let esito;

    const secureUrl = `interprete-web/v1/secure`;
    const unsecureUrl = `interprete-web/v1/unsecure`;

    const isUserLogged = LoginHelper.userLogged;
    switch (pegaApi){
      case PEGA_API.loadPage:
        esito = `${isUserLogged ? secureUrl : unsecureUrl}/load-page`;
        break;
      case PEGA_API.nextPage:
        esito = `${isUserLogged ? secureUrl : unsecureUrl}/go-next-page`;
        break;
      case PEGA_API.updatePage:
        esito = `${isUserLogged ? secureUrl : unsecureUrl}/update-page`
        break;
      default:
        esito = `${isUserLogged ? secureUrl : unsecureUrl}/load-page`;
        break;
    }

    return esito;
  }

  public static redirectedCommonNetworkService(): CommonNetworkService{
    const commonNetworkService = new CommonNetworkService();
    if(StatusService.GlobalStatusService.modalitaRedirect){
      if(!this.redirectSit){
        const oldMethod = (commonNetworkService as any).createHeaderAndCallFunction;
        (commonNetworkService as any).createHeaderAndCallFunction = async (headers: any) => {
          const response = await oldMethod(headers);

          response["x-ibm-client-id"] = "e6b5d646-485f-4e45-b937-752bf6ae84ee";
          response["x-ibm-client-secret"] = "Y1iJ3cB1xU1gA5vD8lB4tB7yA4fY1lN8cS1sD5wQ6eN5dV5lL4";
          response["x-unipol-requestid"] = (commonNetworkService as any).generateRandomRequestId();

          return response;
        }
      }else{
        const oldMethod = (commonNetworkService as any).createHeaderAndCallFunction;
        (commonNetworkService as any).createHeaderAndCallFunction = async (headers: any) => {
          const response = await oldMethod(headers);

          response["x-ibm-client-id"] = "c13117fd-af7e-45b6-8418-ff7b32a7422f";
          response["x-ibm-client-secret"] = "bS5qE8iM2yW8eL0qM7fU7pI2pN2dK3yP0cM7oM3gI1xW0mG6bS";
          response["x-unipol-requestid"] = (commonNetworkService as any).generateRandomRequestId();

          return response;
        }
      }
    }

    return commonNetworkService;
  }

  public static getRedirectedUrl(url: string): string{
    let esito = url;
    if(StatusService.GlobalStatusService.modalitaRedirect){
      if(!InterpreteService.redirectSit){
        esito = `https://evo-dev.unipolsai.it${url}`;
      }else{
        esito = `https://evo-sit.unipolsai.it${url}`;
      }
    }
    return esito;
  }

  private async _baseCall(pegaApi: PEGA_API, body: Data): Promise<Data>{
    const headers = {
      'x-unipol-canale': LoginHelper.userLogged ? 'AR' : 'AP'
    };

    const request = new Request(this._generateBaseUrl(pegaApi), {
      body,
      headers
    });

    const commonNetworkService = InterpreteService.redirectedCommonNetworkService();
    return commonNetworkService.post(InterpreteService.getRedirectedUrl(request.completeUrl), request.body, request.headers, true);
  }

  public async loadPage(loadPageBody: ILoadPageRequest): Promise<Data> {
    return this._baseCall(PEGA_API.loadPage, loadPageBody);
  }

  public async nextPage(nextPageBody: IGoNextPageRequest): Promise<Data> {
    return this._baseCall(PEGA_API.nextPage, nextPageBody);
  }

  public async updatePage(updatePageBody: IUpdatePageRequest): Promise<Data> {
    return this._baseCall(PEGA_API.updatePage, updatePageBody);
  }
}
