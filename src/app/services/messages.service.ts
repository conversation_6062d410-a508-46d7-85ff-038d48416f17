import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import {
  IActionSet,
  IGestioneProcesso,
  IProcessManagement
} from "../models/models.commons";
import { EventsManaged, SupportedActions } from '../utils/events.enum';

@Injectable({
  providedIn: 'root',
})
export class MessagesService {
  private actionSubject = new Subject<any>();
  private overlayDataSubject = new Subject<any>();

  private loading$: BehaviorSubject<boolean> = new BehaviorSubject(false);
  private error$: BehaviorSubject<boolean> = new BehaviorSubject(false);

  public customLoadingFlag = false;
  public customLoadingType: 'showPrice' | 'warranty' = 'showPrice';

  public loading = this.loading$.asObservable();
  public error = this.error$.asObservable();

  public setLoading(status: boolean) {
    this.loading$.next(status);
  }

  public setError(error: boolean) {
    this.error$.next(error);
  }

  public sendActionMessage(
    sActionName: string,
    oAction: any,
    processManagement?: IProcessManagement,
    gestioneProcesso?: IGestioneProcesso,
    tooltipID?: string,
    callBackAfterRetrieve?: any,
    retrieveType?: string,
    VediEModifica?: any
  ) {
    this.actionSubject.next({
      actionName: sActionName,
      action: oAction,
      processManagement: processManagement,
      gestioneProcesso: gestioneProcesso,
      tooltipID: tooltipID,
      callBackAfterRetrieve: callBackAfterRetrieve,
      retrieveType: retrieveType,
      VediEModifica: VediEModifica,
    });
  }

  public  sendOverlayDataSubject(dataSubject: { id: string; data: any }) {
    this.overlayDataSubject.next(dataSubject);
  }

  public  getOverlayData(): Observable<{ id: string; data: any }> {
    return this.overlayDataSubject.asObservable();
  }

  public  getActionMessage(): Observable<any> {
    return this.actionSubject.asObservable();
  }

  public onEventExecuted(
    actionsSet: IActionSet[],
    event: EventsManaged,
    processManagement: IProcessManagement,
    gestioneProcesso: IGestioneProcesso,
    tooltipID?: string,
    callBackAfterRetrieve?: unknown,
    retrieveType?: string,
    VediEModifica?: unknown
  ) {
    if (actionsSet) {
      //Prentiamo le actions che hanno l'evento cercato
      const targetActionsSet = actionsSet.filter(action => action.events.map(targetEvent => targetEvent.event).includes(event));
      for(const actionSet of targetActionsSet){
        const actions = actionSet.actions.filter(action => SupportedActions.includes(action.action));
        for(const action of actions){
          this.sendActionMessage(
            action.action,
            action,
            processManagement,
            gestioneProcesso,
            tooltipID,
            callBackAfterRetrieve,
            retrieveType,
            VediEModifica
          );
        }
      }
    }
  }
}
