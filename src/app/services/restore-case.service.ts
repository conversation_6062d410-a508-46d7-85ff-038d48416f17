import { Injectable } from '@angular/core';
import { Helpers } from '@tpd-web-common-libs/nodejs-library';
import { StatusService } from "./status.service";

export interface RestoreDataModel {
  assignmentId: string,
  caseId: string,
  env: string,
  productType: string
}

@Injectable({
  providedIn: 'root'
})
export default class RestoreCaseService{
  public constructor(
    private _statusService: StatusService
  ) {
  }

  public set restoreData(data: RestoreDataModel){
    if(Helpers.EnvironmentHelper.isClientSide() && this._statusService.modalitaRedirect){
      sessionStorage.setItem('restoreInterpreteData', JSON.stringify(data));
    }
  }

  public get restoreData(): RestoreDataModel{
    let data = undefined;

    if(this._statusService.modalitaRedirect){
      if(this._statusService.clearRestoreCaseDataMode){
        this.restoreData = undefined;
        window.location.href = window.location.href.replace('&clear=true', '');
      }else{
        try{
          data = JSON.parse(sessionStorage.getItem('restoreInterpreteData'));
        }catch (e){
          console.error("Non é stato possibile recuperare i dati dallo storage per eseguire il restore", e);
        }
      }

    }

    return data;
  }
}
