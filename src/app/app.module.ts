import {
  NgModule,
  Injector,
  ApplicationRef,
  Inject,
  CUSTOM_ELEMENTS_SCHEMA,
} from '@angular/core';
import { AppProps } from './app.props';
import { Utils } from '@tpd-web-common-libs/nodejs-library';
import { importsModule } from './imports-module';
import { providersModule } from './providers-module';
import { TpdInterpreteDxApi } from './tpd-interprete-angular-dx-api-pu/tpd-interprete-angular-dx-api-pu';
import { declarationsModule } from './declarations-module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

export const DynamicAppModule = (props: AppProps) => {
  @NgModule({
    declarations: [...declarationsModule()],
    imports: [...importsModule(), BrowserAnimationsModule],
    providers: [
      {
        provide: AppProps,
        useValue: props,
      },
      ...providersModule(),
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA],
  })
  class Module {
    appRef;

    constructor(
      @Inject(AppProps) private props: AppProps,
      private injector: Injector
    ) {}

    ngDoBootstrap(appRef: ApplicationRef) {
      appRef.bootstrap(
        TpdInterpreteDxApi,
        Utils.getAngularWebComponentSelector(
          this.props.__mfeName__,
          this.props.id
        )
      );
      this.appRef = appRef;
    }
  }

  return Module;
};
