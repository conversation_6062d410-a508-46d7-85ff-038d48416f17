@import "../../variables.scss";

* {
  box-sizing: border-box;
  &::ng-deep{
    p {
      margin: 0 !important;
    }
  }
}

html {
  font-size: 10px;
  -webkit-tap-highlight-color: transparent;
  -webkit-text-size-adjust: 100%;
}

body {
  font-family: Unipol;
  font-size: 16px;
  margin: 0;
  padding: 0;
  line-height: 1.42857143;
  color: #333;
  background-color: white;
  display: block;
  transition: opacity ease-in 0.2s;
}

.wrapper-container {
  height: 100%;
  width: 100%;
}
.d-flex {
  display: flex;
}
.space-between {
  justify-content: space-between;
}
.fd-row-reverse {
  flex-direction: row-reverse;
}
.fd-column {
  flex-direction: column;
}
.jsf-center {
  justify-content: center;
}

.align-center {
  align-items: center;
}

.w-100 {
  width: 100%;
}

.mrg-t-24 {
  margin-top: 24px;
}
.mt-05em {
  margin-top: 0.5em;
}
.leafPosition {
  align-self: center;
  margin-right: 20px;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

// font

.Heading-White-Bold {
  font-weight: bold;
  font-family: $font-family-bold;
  color: white;

  @media #{$bkp_mobile} {
    font-size: $font-heading-white-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-heading-white-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-heading-white-desktop-size;
  }
}

.Heading-White {
  font-weight: normal;
  font-family: $font-family-default;
  color: white;

  @media #{$bkp_mobile} {
    font-size: $font-heading-white-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-heading-white-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-heading-white-desktop-size;
  }
}

.Heading-1,
.HeadingTPD-1 {
  font-weight: bold;
  font-family: $font-family-bold;
  text-transform: uppercase;
  color: $medium-light-blue;

  @media #{$bkp_mobile} {
    font-size: $font-heading1-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-heading1-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-heading1-desktop-size;
  }
}

.Heading-2,
.HeadingTPD-2,
.Heading-6,
.HeadingTPD-6 {
  font-weight: bold;
  font-family: $font-family-medium;
  line-height: 1.5;
  color: $main_color;

  @media #{$bkp_mobile} {
    font-size: $font-heading2-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-heading2-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-heading2-desktop-size;
  }
}

.HeadingTPD-2-white {
  font-weight: bold;
  font-family: $font-family-medium;
  line-height: 1.5;
  color: white;

  @media #{$bkp_mobile} {
    font-size: $font-heading2-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-heading2-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-heading2-desktop-size;
  }
}

.Heading-3,
.HeadingTPD-3,
.HeadingTPD-3-LOW {
  font-weight: bold;
  font-family: $font-family-medium;
  line-height: 1.5;
  color: $main_color;

  @media #{$bkp_mobile} {
    font-size: $font-heading3-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-heading3-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-heading3-desktop-size;
  }
}

.Heading-4,
.HeadingTPD-4 {
  font-weight: normal;
  font-family: $font-family-default;
  line-height: 1.5;
  color: $main_color;

  @media #{$bkp_mobile} {
    font-size: $font-heading4-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-heading4-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-heading4-desktop-size;
  }
}

.Heading-5,
.HeadingTPD-5 {
  font-weight: normal;
  font-family: $font-family-bold;
  font-size: $font-heading5-size;
  line-height: 1.6;
  color: $main_color;
}

.SubtitleLogin {
  font-family: $font-family-default;
  font-size: $font-text-xs-mobile-size;
  color: $main_color;
}

.Text-responsive,
.Address-Text {
  font-family: $font-family-default;
  color: $main_color;

  @media #{$bkp_mobile} {
    font-size: $font-text-responsive-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-text-responsive-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-text-responsive-desktop-size;
  }
}

.Text-responsive-SSL,
.Text-responsive-2 {
  font-family: $font-family-default;
  color: $main_color;

  @media #{$bkp_mobile} {
    font-size: $font-text-responsive-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-text-responsive-mobile-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-text-responsive-desktop-size;
  }
}

.Text-responsive-medium,
.Text-responsive-L,
.Text-responsive-medium-M {
  font-family: $font-family-medium;
  color: $main_color;

  @media #{$bkp_mobile} {
    font-size: $font-text-responsive-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-text-responsive-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-text-responsive-desktop-size;
  }
}

.Text-responsive-white {
  font-family: $font-family-default;
  color: white;

  @media #{$bkp_mobile} {
    font-size: $font-text-responsive-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-text-responsive-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-text-responsive-desktop-size;
  }
}

.Text-responsive-medium-S {
  font-family: $font-family-medium;
  color: $main_color;

  @media #{$bkp_mobile} {
    font-size: $font-text-responsiveS-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-text-responsiveS-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-text-responsiveS-desktop-size;
  }
}

.Text-responsive-S,
.Text-responsive-light-S {
  font-family: $font-family-default;
  color: $main_color;

  @media #{$bkp_mobile} {
    font-size: $font-text-responsiveS-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-text-responsiveS-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-text-responsiveS-desktop-size;
  }
}

.Text-responsive-M {
  font-family: $font-family-default;
  color: $main_color;

  @media #{$bkp_mobile} {
    font-size: $font-text-responsiveM-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-text-responsiveM-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-text-responsiveM-desktop-size;
  }
}
.Text-responsive-M-white {
  font-family: $font-family-medium;
  color: white;

  @media #{$bkp_mobile} {
    font-size: $font-text-responsiveM-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-text-responsiveM-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-text-responsiveM-desktop-size;
  }
}

.Text-responsive-bold-M-white {
  font-family: $font-family-bold;
  color: white;

  @media #{$bkp_mobile} {
    font-size: $font-text-responsiveM-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-text-responsiveM-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-text-responsiveM-desktop-size;
  }
}

.Text-responsive-bold-M,
.Titolo-sezione,
.Heading-7,
.HeadingTPD-7 {
  font-family: $font-family-bold;
  color: $main_color;

  @media #{$bkp_mobile} {
    font-size: 18px;
  }

  @media #{$bkp_tablet} {
    font-size: 24px;
  }

  @media #{$bkp_desktop} {
    font-size: 24px;
  }
}

.Heading-8,
.HeadingTPD-8 {
  font-family: $font-family-bold;
  color: $blue-primary; /*TODO: quando commons presenti*/ //return-color(blu-unipol);*/
  @media #{$bkp_desktop} {
    font-size: $font-heading8-size;
    font-weight: 600;
    line-height: 30px;
  }
}

.Text-responsive-bold-M-secondary,
.Consents-Heading {
  font-family: $font-family-bold;
  color: $medium-light-blue;

  @media #{$bkp_mobile} {
    font-size: $font-text-responsiveM-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-text-responsiveM-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-text-responsiveM-desktop-size;
  }
}

.Text-responsive-bold,
.NomeGaranzia,
.PrezzoGaranzia,
.Text-responsive-L-bold {
  font-family: $font-family-bold;
  color: $main_color;
  font-weight: bold;

  @media #{$bkp_mobile} {
    font-size: $font-text-responsive-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-text-responsive-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-text-responsive-desktop-size;
  }
}

.Text-responsive-italic-grey,
.Text-responsive-Italic-grey {
  font-family: $font-family-default;
  color: $middle-grey;
  font-style: italic;

  @media #{$bkp_mobile} {
    font-size: $font-text-responsive-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-text-responsive-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-text-responsive-desktop-size;
  }
}

.Text-responsive-italic {
  font-family: $font-family-default;
  color: $main_color;
  font-style: italic;

  @media #{$bkp_mobile} {
    font-size: $font-text-responsive-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-text-responsive-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-text-responsive-desktop-size;
  }
}

.Text {
  font-family: $font-family-default;
  font-size: $font-text-size;
  color: $main_color;
}

.Text-italic-S {
  font-family: $font-family-default;
  font-size: $font-text-size;
  color: $main_color;
  font-style: italic;
}

.Text-white {
  font-family: $font-family-default;
  font-size: $font-text-size;
  color: white;
}

.Text-grey {
  font-family: $font-family-default;
  font-size: $font-text-size;
  color: $middle-grey;
}

.Text-grey-XS {
  @extend .Text-grey;
  font-size: $font-text-xs-mobile-size;
  color: $middle-grey-pu;
  @media #{$bkp_tablet} {
    font-size: $font-text-xs-tablet-size;
  }
}

.Text-italic,
.ChiaveAttributoGaranzia,
.ValoreAttributoGaranzia {
  font-family: $font-family-default;
  font-size: $font-text-size;
  color: $main_color;
}

.Text-medium,
.Incomplete-Address {
  font-family: $font-family-medium;
  font-size: $font-text-size;
  color: $main_color;
  font-weight: 500;
  line-height: 1.25;
}

.Text-white-medium {
  font-family: $font-family-medium;
  font-size: $font-text-size;
  color: white;
  font-weight: 500;
  line-height: 1.25;
}

.Text-bold,
.Consents-Accordion-Heading {
  font-family: $font-family-bold;
  font-size: $font-text-size;
  color: $main_color;
  font-weight: 500;
  line-height: 1.25;
}

.Text-bold-white {
  font-family: $font-family-bold;
  font-size: $font-text-size;
  color: white;
  font-weight: 500;
  line-height: 1.25;
}

.Text-medium-underline {
  font-family: $font-family-medium;
  font-size: $font-text-size;
  color: $main_color;
  text-decoration: underline;
  font-weight: 500;
  line-height: 1.25;
}

.Text-bold-underline {
  font-family: $font-family-bold;
  font-size: $font-text-size;
  color: $main_color;
  text-decoration: underline;
  font-weight: 500;
  line-height: 1.25;
}

.Text-Promo {
  font-family: $font-family-medium;
  font-size: $font-text-promo-size;
  color: $main_color;
  opacity: 0.6;
  font-weight: 500;
  line-height: 1.43;
}

.Text-strike {
  font-family: $font-family-default;
  font-size: $font-tag-size;
  color: $main_color;
  font-weight: normal;
  text-decoration: line-through;
}

.Text-BlueGrey-M {
  font-family: $font-family-medium;
  font-weight: 500;
  color: $blue-primary;

  @media #{$bkp_mobile} {
    font-size: $font-text-responsiveM-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-text-responsiveM-mobile-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-text-responsiveM-desktop-size;
  }
}

.Strong {
  font-family: $font-family-bold;
  font-size: $font-text-size;
  font-weight: 100;
  color: $main_color;
}

.Tag-alert {
  font-family: $font-family-medium;
  color: white;
  font-size: $font-tag-size;
  background-color: $alert-color;
  padding: 0.4rem 1rem;
  margin: 1em 0;
  border-radius: 1.5rem;
}

.Tag-property {
  font-family: $font-family-medium;
  color: white;
  font-size: $font-tag-size;
  background-color: $property-light-color;
  padding: 0.4rem 1rem;
  margin: 1em 0;
  border-radius: 1.5rem;
}

.RoundedCardText,
.RoundedCardText-Bold,
.RoundedCardText-bold {
  font-family: $font-family-bold;
  color: $main_color;

  @media #{$bkp_mobile} {
    font-size: $font-rounded-card-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-rounded-card-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-rounded-card-desktop-size;
  }
}

// custom class color

.color-title {
  color: $medium-light-blue;
}

// layout

.default {
  display: flex;
  flex-direction: row;
  justify-content: center;

  .sub-layout {
    &:has(.DocumentationButtonPuContainer){
      padding: 16px !important;
    }

    @media #{$bkp_mobile} {
      width: 100%;
      padding: 0;
    }
    @media #{$bkp_tablet} {
      width: 100%;
      padding: 0;
    }
    @media #{$bkp_desktop} {
      width: 100%;
      padding: 0;
    }
  }
}

/*Custom layout per larghezza di 526px*/
.defaultMW528 {
  display: flex;
  flex-direction: row;
  justify-content: center;
  height: auto;

  @media #{$bkp_mobile} {
    margin-top: 0;
  }

  @media #{$bkp_tablet} {
    margin-top: 32px;
  }

  @media #{$bkp_desktop} {
    margin-top: 33px;
  }

  > .sub-layout {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;

    width: 100%;
    height: auto;

    padding: 0;

    @media #{$bkp_mobile} {
      max-width: 100%;

      padding: 16px 10px;
      margin: 24px 0;
    }

    @media #{$bkp_tablet} {
      max-width: 528px;

      margin: 0;
      padding: 0;
    }

    @media #{$bkp_desktop} {
      max-width: 528px;

      margin: 0;
      padding: 0;
    }

    gap: 32px;

    /*
       * All'evenienza inserire sotto le classi che devono sottostare alla regola GAP 32px
       */
    .flatCardRadioContainer {
      margin-top: 32px;
    }
  }
}

.default-bg-light {
  display: flex;
  flex-direction: row;
  justify-content: center;
  background-color: $light_blue;
  margin-top: 24px;
  .sub-layout {
    border: 0 !important;
    @media #{$bkp_mobile} {
      width: $width_container_mobile;
      padding: 0 20px;
      margin: 24px 0;
    }
    @media #{$bkp_tablet} {
      width: $width_container_tablet;
      padding: 0;
      margin: 24px 0;
    }
    @media #{$bkp_desktop} {
      width: $width_container_desktop;
      padding: 0;
      margin: 24px 0;
    }
  }

  .card-form {
    .sub-layout::ng-deep {
      margin: 0;
      .pdn-label-input {
        padding: 0 0 12px 0 !important;
      }
    }
  }
}

.inlineMiddle-layout::ng-deep {
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.inlineMiddleAlignC-layout * {
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.card-form {
  display: flex;
  flex-direction: row;
  justify-content: center;
  background-color: white;
  width: 100%;

  .sub-layout::ng-deep {
    border: solid 2px $soft-grey;
    @media #{$bkp_mobile} {
      width: $width_container_mobile;
      //padding: 22px;
      padding: 24px 24px 28px;
      .input-dati-utente {
        width: 100% !important;
      }
    }
    @media #{$bkp_tablet} {
      max-width: $width_container_tablet;
      width: 100%;
      padding: 32px 60px;
    }
    @media #{$bkp_desktop} {
      max-width: $width_container_desktop;
      width: 100%;
      padding: 32px 60px;
    }

    .accordion {
      width: 100% !important;
    }
    .layout-input {
      @media #{$bkp_mobile} {
        width: 100% !important;
      }
    }
    .layout-input-date {
      @media #{$bkp_mobile} {
        width: 100% !important;
      }
    }

    .box-indirizzo-width {
      width: 100%;
      min-width: 0;
    }
    .radio-size {
      max-width: none;
      width: 45%;
    }

    .radio-horizontal {
      width: 100%;
    }

    .responsive3col {
      .pdn-label-input::ng-deep {
        padding: 0 !important;
      }
      .select::ng-deep {
        margin-bottom: 0 !important;
      }
      input::ng-deep {
        margin: 0 !important;
      }
    }

    .responsive4col {
      .pdn-label-input::ng-deep {
        padding: 0 !important;
      }
      .select::ng-deep {
        margin-bottom: 0 !important;
      }
      input::ng-deep {
        margin: 0 !important;
      }
    }

    // @media #{$bkp_mobile} {
    //   .input-dati-utente {
    //     max-width: 550px;
    //     width: 100%;
    //   }
    // }
  }
}

.card-border {
  display: flex;
  flex-direction: row;
  justify-content: center;
  background-color: white;
  width: 288px;
  .sub-layout::ng-deep {
    border: solid 1px $border-card-disabled;
    border-radius: 24px;
    padding: 16px;
    .mimicASentence {
      align-items: center !important;
      tpd-px-icon {
        display: flex;
        margin-right: 12px;
      }
    }
  }
}

.web-box-border {
  display: flex;
  flex-direction: row;
  justify-content: center;
  background-color: white;
  .sub-layout::ng-deep {
    border: solid 1px $border-card-disabled;
    padding: 16px;
  }
}

.tooltip-card-container {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  max-width: 100%;
  width: 100%;
  gap: 8px;
  padding: 12px;
  background-color: #EEF6FC;

  &::before{
    content: "";
    aspect-ratio: 1 / 1;
    width: 26px;
    flex-shrink: 0;
    display: block;
    background-image: url("/NextAssets/icons/icon-Info.svg");
    background-size: cover;
  }

  .min-w {
    min-width: unset;
  }
}

.tooltip-card-blue {
  position: relative;
  flex-shrink: 0;

  background-color: $blue-primary;

  width: 60px;
  @media #{$bkp_mobile_only} {
    width: 56px;
  }

  &::before {
    content: "";
    display: block;
    aspect-ratio: 1 / 1;
    width: 20px;

    background-color: white;
    border-radius: 50%;

    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
  }

  &::after {
    content: "";
    display: block;
    aspect-ratio: 1 / 1;

    width: 12px;
    border-radius: 50%;

    background-image: url("/NextAssets/icons/icon-Info.svg");
    background-position: center;
    background-size: 250%;

    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);

    filter: invert(28%) sepia(12%) saturate(1691%) hue-rotate(166deg)
      brightness(93%) contrast(91%);
  }
}

.tooltip-card {
  width: 100%;
}

.card-box {
  border: solid 2px $medium-light-blue;
  background-color: white;
  @media #{$bkp_mobile} {
    padding: 12px 16px;
  }
}

.card-azzurra::ng-deep {
  margin-top: 20px;
  margin-bottom: 20px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 10px 3%;
  background-color: $secondary-lightest;
  width: 100%;
  .mimicASentence {
    align-items: center;
    gap: 1.5em;
  }
}
.Advice-box::ng-deep {
  margin-top: 20px;
  margin-bottom: 20px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 10px 3%;
  background-color: $advice-color;
  width: 100%;
  .mimicASentence {
    align-items: center;
    gap: 1.5em;
  }
}

.rounded-card {
  border-radius: 24px;
  border: solid 2px $main_color;
  display: flex;
  flex-direction: row;
  justify-content: stretch;
  padding: 24px 12px;
  width: 100%;

  >*{
    width: 100% !important;
  }
}

.card-borderless {
  display: flex;
  flex-direction: row;
  justify-content: center;
  background-color: white;

  .sub-layout::ng-deep {
    // border: solid 2px $soft-grey;
    @media #{$bkp_mobile} {
      width: $width_container_mobile;
      padding: 22px;
      padding-top: 5px;
    }
    @media #{$bkp_tablet} {
      width: $width_container_tablet;
      padding: 10px 60px 32px 60px;
    }
    @media #{$bkp_desktop} {
      width: $width_container_desktop;
      padding: 10px 60px 32px 60px;
    }

    .accordion {
      width: 100% !important;
    }
    .layout-input {
      @media #{$bkp_mobile} {
        width: 100% !important;
      }
    }
  }
}

.promo-section {
  display: flex;
  flex-direction: row;
  justify-content: center;
  background-color: $main_color;
  align-items: center;
  min-height: 55px;
}

.box-highligth {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  background-color: $secondary-lightest;
  min-height: 164px;

  .sub-layout {
    @media #{$bkp_mobile} {
      width: $width_container_mobile;
      padding: 24px 20px;
    }
    @media #{$bkp_tablet} {
      width: $width_container_tablet;
      padding: 24px 0;
    }
    @media #{$bkp_desktop} {
      width: $width_container_desktop;
      padding: 24px 0;
    }
  }
}

.box-info-text-layout {
  background-color: transparent;

  border: solid 1px $border-card-disabled;
  padding: 16px;
}

.BoxInfoText {
  color: $middle-grey-pu;
  font-size: $font-text-size;
  font-weight: 500;
}

.left-layout {
  display: flex;

  .sub-left-layout {
    margin-left: auto;
  }
}

.box-privacy-layout::ng-deep {
  padding: 16px;
  border: solid 2px #f0f0f0;
  margin: auto;
  display: flex;
  flex-direction: row;
  align-items: center;

  @media #{$bkp_desktop} {
    max-width: 100%;
  }

  .Consents-Accordion-Heading {
    color: $middle-grey-pu !important;
    font-family: $font-family-medium;
    font-weight: 550;
  }

  .mimicASentence {
    align-items: center;
    gap: 16px;
  }
}

.margin-Left-S {
  margin-left: 25px;
}

.modal-content-layout::ng-deep {
  width: 92%;
  margin: 0 auto;
  .inlineDoubleGridLayout {
    align-items: center;
  }
}

.footerButton-layout {
  width: 100%;
  @media #{$bkp_mobile} {
    padding: 10px 0 0 0;
    width: 90vw;
  }
  @media #{$bkp_tablet} {
    padding: 10px 0;
    width: 100%;
  }
}

//BOX di testo disabled
.disabled-input-box {
  background-color: $ivory !important;
  color: $grey !important;
  font-size: $font-text-responsiveS-desktop-size;
  font-family: $font-family-default;
  line-height: 24px;
  height: 48px !important;
  pointer-events: none;
  cursor: not-allowed;
  border: 0 !important;
  /*TODO: Decommentare quando presenti @include ellipsis(1);*/
}

// accordion
.accordion::ng-deep {
  @media #{$bkp_mobile} {
    width: auto;
    margin-left: auto;
    margin-right: auto;
  }
  @media #{$bkp_tablet} {
    width: auto;
    margin-left: auto;
    margin-right: auto;
  }
  @media #{$bkp_desktop} {
    width: auto;
    margin-left: auto;
    margin-right: auto;
  }

  .accordion {
    width: 100%;
  }

  .layout-input {
    width: 100%;
  }
  .accordion-4 {
    .DocumentationButton {
      @media #{$bkp_mobile} {
        width: 85vw;
      }
      @media #{$bkp_tablet} {
        max-width: calc(
          90vw - (32px * 2)
        ); //32px è il padding sull'asse delle x dell'accordion 4 su tablet
      }
      @media #{$bkp_desktop} {
        max-width: calc(
          75vw - (36px * 2)
        ); //36px è il padding sull'asse delle x dell'accordion 4 su desktop
      }
    }
  }
}

.ribbon7-layout {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: $ribbon-color;
  border-radius: 16px;
  padding: 6px 8px 4px;
  margin-top: 8px;
}

// cursor
.cursor-pointer {
  cursor: pointer;
}

// space

// margine tra label e input (autocomplete, dropdown e textInput)
.mrg-label-input {
  margin-bottom: $mrg-btm-label-input-pu;
}

// background
.background-Property {
  background-color: $property-color;
}
.background-Mobility {
  background-color: $mobility-color;
}

.background-Secondary {
  background-color: $main_color;
}

// text align

.text-align-Default,
.text-align-default {
  text-align: left;
}

.text-align-Center,
.text-align-center {
  text-align: center;
}

.text-aling-Left,
.text-align-left {
  text-align: left;
}

.text-align-Right,
.text-align-right {
  text-align: right;
}

.icon-Lock {
  width: 20px;
  height: 20px;
}

.icon-Lock::before {
  content: "";
  position: absolute;
  background: url("/TpdPortalCommons/build/assets/icon-lock.svg");
  background-size: cover;
  width: 18px;
  height: 18px;
  padding-right: 1rem;
}

.icon-Auto2-negative::before {
  font-family: "unipol-icon" !important;
  content: "\e956";
  color: white;
  font-size: 36px;
  // line-height: 0.55;
}

//Visibility
.visible-mobile {
  @media #{bkp_mobile} {
    display: block;
  }

  @media #{$bkp_tablet} {
    display: none;
  }

  @media #{$bkp_desktop} {
    display: none;
  }
}

.visible-tablet {
  display: none;

  @media #{$bkp_tablet} {
    display: block;
  }

  @media #{$bkp_desktop} {
    display: none;
  }
}

.visible-desktop {
  display: none;

  @media #{$bkp_desktop} {
    display: block;
  }
}

.testo-non-valido {
  height: 14px;
  font-family: "Unipol Medium";
  font-size: 14px;
  font-stretch: normal;
  font-style: normal;
  line-height: 0.88;
  letter-spacing: normal;
  color: #ff001f;
}

.Heading-2-popupWidth {
  font-weight: bold;
  font-family: $font-family-medium;
  line-height: 1.5;
  color: $main_color;
  width: 446px;
  text-align: center;

  @media #{$bkp_mobile} {
    font-size: $font-heading2-mobile-size;
  }

  @media #{$bkp_tablet} {
    font-size: $font-heading2-tablet-size;
  }

  @media #{$bkp_desktop} {
    font-size: $font-heading2-desktop-size;
  }
}

.SmallScrollBar {
  &::-webkit-scrollbar {
    width: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: $scrollbar-gray-color;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: $scrollbar-gray-color;
  }
}

.SSRLoader{
  display: block;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;

  &::after{
    content: "";
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    z-index: 1000;

    background-color: white;

    background-image: url("/NextAssets/icons/loading_unipol.gif");
    background-size: 150px;
    background-position: center 150px;
    background-repeat: no-repeat;

    image-rendering: optimizeQuality;
    background-blend-mode: multiply;

    filter: invert(1);

    opacity: 0.5;
  }
}
