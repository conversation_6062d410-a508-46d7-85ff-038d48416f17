import { ChangeDetector<PERSON><PERSON>, Component, Inject, <PERSON><PERSON><PERSON>, OnInit, ViewChild } from "@angular/core";
import { BehaviorSubject, Subscription } from "rxjs";
import { AppProps } from "../app.props";
import { ConfigInterpreteDxAPI, ProductType } from "../models/config-interprete";
import { AssignmentService } from "../services/assignment.service";
import { CartService, IActiveCart } from "../services/cart.service";
import {
  AmbitoDiBisognoCode,
  CommonFunctions,
  LocalStorageLayer,
  ModaleErroreDataType,
  ModaleErroreProdottoUnicoComponent,
  TipoModaleErrore,
  TPDErrorsService
} from "@tpd-web-angular-libs/angular-library";
import { InterpreteService, PEGA_API } from "../services/bffinterprete.service";
import { InputSuccess, UtilsService } from "../services/utils.service";
import { StatusService } from "../services/status.service";
import { MessagesService } from "../services/messages.service";
import { GenericDataType, IActionLoadRequest } from "../models/bff.interprete.model";
import { IStoragePaymentData } from "../utils/payment-utils";
import { AngularMFE, Helpers } from "@tpd-web-common-libs/nodejs-library";

import { ModuloAnalyticsDx } from "../services/analytics.interprete.service";
import { ModalDxApi } from "../components/modal/modal-dx-api";
import MapsService from "../services/maps.service";

import { PaymentInterpreteService } from "../services/payment.interprete.service";
import UserDataService from "../services/user-data.service";
import { IView } from "../models/models.commons";
import RestoreCaseService from "../services/restore-case.service";
import ChiamateMockService from "../services/servizi-gestione-chiamate/chiamate-mock.service";
import { EventsMethod } from "../utils/eventsMethod";
import { AccordionComponent } from "../components/accordion/accordion.component";

export interface TpdInterpreteDxApiContex{
  retrieveRequest?: (api: PEGA_API, casoRichiesta?: string, ignoraDatiRicevuti?: boolean) => Promise<boolean>,
  createRequest?: (api: PEGA_API, loadRequest: IActionLoadRequest, casoRichiesta?: string, ignoraDatiRicevuti?: boolean) => Promise<boolean>,
  setInitData?: (initData: GenericDataType) => void,
  setEnv?: (env: string) => void,
  setProductType?: (productType: string) => void,

  chiamaRichiestaDiContatto?: (datiAggiuntivi: GenericDataType) => void,
  chiamaCustomCreate?: (productType?: string, gestioneProcesso?: GenericDataType) => void,
  chiamaVisualizzazioneCarrelloVuoto?: (statoVisualizzazione?: boolean) => void,
  chiamaPaginaErroreInterna?: (gestioneProcesso?: GenericDataType, motivoFallimento?: string, actionID?: string) => void
}

const AngularMicrofrontendClass: AngularMFE.Class.ClassType =
  AngularMFE.Class.AngularMicrofrontendFactory({ BehaviorSubject, AppProps });
@Component({
  host: { class: 'widget-global-style' },
  selector: 'tpd-interprete-angular-dx-api-pu',
  templateUrl: './tpd-interprete-angular-dx-api-pu.html',
  styleUrls: ['./tpd-interprete-angular-dx-api-pu.scss'],
})
export class TpdInterpreteDxApi extends AngularMicrofrontendClass implements OnInit {
  public static TpdInterpreteDxApiContext: TpdInterpreteDxApiContex = {}

  // Data Variables:
  private callCompleted = true; // serve per effetturare una sola chiamata alla volta e nel caso inserire in coda le successive chiamate
  private numberModifyPopupAssurance = 0;
  private config: ConfigInterpreteDxAPI;
  private _initData: GenericDataType;
  private _carrello: IActiveCart[];
  private formValue: GenericDataType;
  private actionID = '';
  private modalActionID = '';
  private actionIDTemp = '';
  private processManagement: GenericDataType;
  private gestioneProcesso: GenericDataType;
  private refreshFor = '';
  private target = '';
  private callBackAfterRetrieve: (() => void) | "closeModalAndRefreshPage" | "closeModalAndReloadPage";
  private paymentData: IStoragePaymentData;
  private esitoPagamento: string;
  private codErrorPayment: string;
  private messageErrorPayment: string;
  private dxModule: ModuloAnalyticsDx = {};
  private productModule: GenericDataType = {};
  private codaAzioni = [];
  private retrieveType: string;
  private VediEModifica: GenericDataType;

  private requestMapper = {
    [PEGA_API.loadPage]: (actionOnLoad: IActionLoadRequest) => {
      return {
        action: actionOnLoad,
        productType: this.config?.productType,
        assignmentId: this.statusService.assignmentId,
        retrieveType: this.retrieveType,
        referencesToUpdate: this.directLogin
          ? {
              ...this._initData,
              ...this.processManagement,
              ...this.gestioneProcesso,
              ...this.VediEModifica,
            }
          : this._initData,
        caseId: this.statusService.caseID,
        idOfferta: this.idOfferta,
        versione: this.versione,
        env: this.env,
      };
    },
    [PEGA_API.nextPage]: () => {
      return {
        actionId: this.statusService.hasReplaceActionId() ? this.statusService.useReplaceActionId() : this.actionID,
        assignmentId: this.statusService.assignmentId,
        captchaToken: this.statusService.captchaToken,
        retrieveType: this.retrieveType,
        referencesToUpdate: {
          ...this.formValue,
          ...this.processManagement,
          ...this.gestioneProcesso,
          ...this.VediEModifica,
        },
        abTesting: this.abTesting,
        productType: this.config?.productType,
      };
    },
    [PEGA_API.updatePage]: () => {
      return {
        assignmentId: this.statusService.assignmentId,
        actionId: this.statusService.hasReplaceActionId() ? this.statusService.useReplaceActionId() : this.actionID,
        referencesToUpdate: {
          ...this.formValue,
          ...this.processManagement,
          ...this.gestioneProcesso,
        },
        refreshFor: this.refreshFor,
        productType: this.config?.productType,
      };
    },
  };

  // View Variables:
  public modalView: IView;
  public tooltipID: string;
  public view: IView;
  public caseID: string;

  public overlayTitle = '';
  public isLoadingWithOverlay = false;
  public completaCaricamentoCustom = false;
  public hasError = false;
  public errorId = 'interprete-dx-api-error';
  public errorIdNotVisible = 'interprete-dx-api-error-hidden';
  public reloadVisible = false;
  public showTYP = false;
  public showErrorePagamento = false;
  public showCarrelloVuoto = false;

  //region Gestione pagina di fallimento
  public showPaginaFallimento = false;
  public motivoFallimento: string;
  //endregion

  //region Gestione quotazioni fuori flusso pega
  public ambitoDiBisognoCode = AmbitoDiBisognoCode;
  public showQuotazione = false;
  public ambitoDiBisognoCodeQuotazione: AmbitoDiBisognoCode;
  public listaQuotazioniSupportate = [
    AmbitoDiBisognoCode.VIAGGI,
    AmbitoDiBisognoCode.SALUTE,
    AmbitoDiBisognoCode.INFORTUNI,
  ];
  //endregion

  //region Gestione modale di errore
  public visualizzaModaleErrore = false;
  public tipoModaleErrore: TipoModaleErrore = TipoModaleErrore.ModaleErroreGenerica;
  public datiModaleErrore: ModaleErroreDataType;
  //endregion

  public configInputSuccess: InputSuccess;
  public directLogin = false;
  public idOfferta: string;
  public versione: string;
  public env: string;
  public errorMessage: string;
  public testingKey: string;
  public abTesting = '';
  public erroreRecuperaPreventivo = false;
  @ViewChild('assurancePopup') assurancePopup: ModalDxApi;

  // Subscriptions:
  private actionsSubscription: Subscription;
  private loaderSubscription: Subscription;
  private errorSubscription: Subscription;
  public errorSubscriptionReload: Subscription;
  private successPaymentRicorrente: Subscription;
  private errorPaymentRicorrente: Subscription;
  private closedTooltipSubscription: Subscription;
  private mockSubscription!: Subscription;

  public isProdEnv!: boolean; // TO DO: REMOVE, TEST ENVIRONMENT
  public isServerSideRendering = false;

  constructor(
    @Inject(AppProps) private appProps: AppProps,
    private commonFunctions: CommonFunctions,
    private paymentService: PaymentInterpreteService,
    private messageService: MessagesService,
    private statusService: StatusService,
    private interpreteService: InterpreteService,
    private TPDErrorsService: TPDErrorsService,
    private utilsService: UtilsService,
    // private analyticsInterprete: AnalyticsInterprete
    private assignmentService: AssignmentService,
    private _chiamateMockService: ChiamateMockService,
    private _cartService: CartService,
    private _mapService: MapsService,
    private _userDataService: UserDataService,
    private _localStorageLayer: LocalStorageLayer,
    private _restoreCaseService: RestoreCaseService,
    private _cdr: ChangeDetectorRef,
    private _ngZone: NgZone
  ) {
    super(appProps);
    this.isServerSideRendering = Helpers.EnvironmentHelper.isServerSide();
  }

  public async ngOnInit() {
    const storageData = await this.retrieveDataFromStorage();

    this.config = {
      ...this.config,
      ...storageData
    };

    if (this.config) {
      this.env = this.config.env;
      this._initData = {
        ...this._initData || {},
        ...this.config.initData || {}
      };

      //TODO: Controllare decommentazione carrello
      //this._carrello = this.config.carrello;
      this.statusService.configInterprete = this.config;
      // this.dxModule.proposal_dx_category = this._config.dxCategory;
      this.testingKey = this.config?.abTesting;
      this.propsBehaviorSubject.subscribe((val) => {
        this.statusService.setProps(val);
      });
      if (!this.config.productType) {
        //RouterHelper.goTo('/disambiguazione'); //TODO: commentare per mock o esecuzione DEV
      }

      await this.statusService.inizializzaStatoCarrelloAttivo(); //Inizializziamo la quantità di item all'interno del carrello attivo
      if(!this.config.showCarrello && !this.config.showRichiestaDiContatto && !this.config.mantieniDatiUtente)
        await this._userDataService.clearStoredData();
      else await this._userDataService.resetStoredUserData();
      await this.init();
    }
  }

  //region InizializzazioneInterprete

  private _initObservables(){
    this.loaderSubscription = this.messageService.loading.subscribe(load => {
      //debugger;
      if(load === false){
        if(this.customLoading){
          this.completaCaricamentoCustom = true;
          window.setTimeout(() => {this.isLoadingWithOverlay = load; this.messageService.customLoadingFlag = false;}, 1000);
        }else {
          this.isLoadingWithOverlay = load
        }
      }else {
        this.completaCaricamentoCustom = false;
        this.isLoadingWithOverlay = load;
      };
    });
    this.errorSubscription = this.messageService.error.subscribe(error => this.hasError = error);
    this.TPDErrorsService.getErrorObservable().subscribe(error => error === this.errorId && this._chiamataDiReloadInterprete());
    this.closedTooltipSubscription = this.utilsService.closeToolTip.subscribe(close => close && this.closePopup());
    this.successPaymentRicorrente = this.paymentService.callbackRicorrente.subscribe(() => this.sendPaymentOutcome());
    this.errorPaymentRicorrente = this.paymentService.callbackError.subscribe(() => this.sendPaymentOutcome(true));

    this.actionsSubscription = this.messageService.getActionMessage().subscribe(async action => {
      if (action) {
        if (this.callCompleted) {
          this.callCompleted = false;
          this.actionManagement(action);
        }
        else this.codaAzioni.push(action);
      }
    });
  }

  private _initContext(){
    TpdInterpreteDxApi.TpdInterpreteDxApiContext.createRequest = (api, loadRequest, casoRichiesta, ignoraDatiRicevuti) => this._ngZone.run(() => this.retrieveData(api, this.requestMapper[api](loadRequest), casoRichiesta, ignoraDatiRicevuti));
    TpdInterpreteDxApi.TpdInterpreteDxApiContext.retrieveRequest = (api, casoRichiesta, ignoraDatiRicevuti) => this._ngZone.run(() => this.retrieveData(api, this.requestMapper[api](undefined), casoRichiesta, ignoraDatiRicevuti));
    TpdInterpreteDxApi.TpdInterpreteDxApiContext.setEnv = (env) => this._ngZone.run(() => this.env = env);
    TpdInterpreteDxApi.TpdInterpreteDxApiContext.setProductType = (productType) => this._ngZone.run(() => this.config.productType = productType as any);
    TpdInterpreteDxApi.TpdInterpreteDxApiContext.setInitData = (initData) => this._ngZone.run(() => this._initData = initData);
    TpdInterpreteDxApi.TpdInterpreteDxApiContext.chiamaRichiestaDiContatto = datiAggiuntivi => this._ngZone.run(() => this.chiamaRichiestaDiContatto(datiAggiuntivi));
    TpdInterpreteDxApi.TpdInterpreteDxApiContext.chiamaCustomCreate = (productType, gestioneProcesso) => this._ngZone.run(() => this._customCreateInterprete(productType, gestioneProcesso));
    TpdInterpreteDxApi.TpdInterpreteDxApiContext.chiamaVisualizzazioneCarrelloVuoto = (statoVisualizzazione?: boolean) => this._ngZone.run(() => this.showCarrelloVuoto = statoVisualizzazione);
    TpdInterpreteDxApi.TpdInterpreteDxApiContext.chiamaPaginaErroreInterna = (gestioneProcesso, motivoFallimento, actionID) => this._ngZone.run(() => this._chiamaPaginaErroreInterna(gestioneProcesso, motivoFallimento, actionID));

    if(Helpers.EnvironmentHelper.isClientSide()){
      //Settiamo come elemento globale il context
      window['TpdInterpreteDxApiContext'] = TpdInterpreteDxApi.TpdInterpreteDxApiContext;
    }
  }

  private async _initMock(): Promise<boolean>{
    let richiestaInizializzata = false;
    if(Helpers.EnvironmentHelper.isClientSide() && window.location.href.includes('?mock')){
      const urlParams = new URLSearchParams(window.location.search);
      const mockType = urlParams.get('mock');

      if(mockType !== 'true'){
        //Recupera il case di test
        const restoreData = this._restoreCaseService.restoreData;
        if(restoreData){
          this.statusService.assignmentId = restoreData.assignmentId;
          this.statusService.caseID = restoreData.caseId;
          this.env = restoreData.env;
          this.config.productType = restoreData.productType as ProductType;
          await this.retrieveData(
            PEGA_API.loadPage,
            this.requestMapper[PEGA_API.loadPage](IActionLoadRequest.retrieve),
            "Recupero stato salvato interprete"
          );
          richiestaInizializzata = true;
        }
      }

      if(!richiestaInizializzata && mockType === 'true'){
        this.mockSubscription = this.assignmentService
          .getMockExampleData()
          .subscribe((val) => {
            const metaBodyResponse = val?.metaBodyResponse;
            const pegaBodyResponse = val.pegaBodyResponse;
            this.actionID = metaBodyResponse.actionId;
            this.statusService.assignmentId = metaBodyResponse.assignmentId;
            this.statusService.caseID = pegaBodyResponse.caseID;
            this.view = pegaBodyResponse.view as unknown as IView;
          });
        richiestaInizializzata = true;
      }

      if(!richiestaInizializzata)
        richiestaInizializzata = await this._chiamateMockService.effettuaChiamataMock(mockType);

      if(richiestaInizializzata)
        console.warn("Inizializzazione caso Mock", mockType);
    }

    return richiestaInizializzata;
  }

  private async _initPagamento(){
    console.warn("Inizializzazione caso pagamento");
    const responsePagamentoUrl = this.paymentService.getPaymentResponseUrlData();
    this.esitoPagamento = responsePagamentoUrl.esito;
    this.codErrorPayment = responsePagamentoUrl.codiceEsito;
    this.messageErrorPayment = responsePagamentoUrl.messaggio;

    this.statusService.assignmentId = this.paymentData.assignmentId;
    if (this.paymentData.returnFromAutorizza) {
      const xpayNonce = responsePagamentoUrl.xpayNonce;
      const { importo, aliasToken, nonceRequestId } = this.paymentData;
      const datiPagamento = {
        importo,
        mezzoPagamentoRicorrente: {
          aliasToken,
          nonceRequestId,
          xpayNonce,
        },
        paymentService: this.config.paymentService
      };

      await this.paymentService.acquista(
        datiPagamento,
        this.paymentData.versione,
        this.paymentData.idPreventivo,
        this.paymentData.codiceFiscale
      );
    } else await this.sendPaymentOutcome();
  }

  private async _initRedirectLogin(){
    let richiestaInizializzata = false;

    try {
      const redirect_login = JSON.parse(await Helpers.SessionHelper.getData('redirect_login'));
      if(redirect_login){
        console.warn("Inizializzazione atterraggio post login");
        await Helpers.SessionHelper.deleteData('redirect_login');
        this.statusService.assignmentId = redirect_login.assignmentId;
        this.statusService.caseID = redirect_login.caseId;

        //recupero analytics
        this.productModule = this.config?.analyticsProductModule;
        const analyticsAfterLogin = JSON.parse(await Helpers.SessionHelper.getData('ANALYTICS_AFTER_LOGIN'));
        if (analyticsAfterLogin) {
          localStorage.removeItem('ANALYTICS_AFTER_LOGIN');
          this.dxModule = {
            proposal_dx_flow: analyticsAfterLogin?.proposal_dx_flow,
            proposal_dx_source: analyticsAfterLogin?.proposal_dx_source
          };
        }

        //setto proprietà directLogin nel processManagement
        this.directLogin = true;
        this.processManagement = {
          ...this.processManagement,
          'ProcessManagement.DirectLogin': this.directLogin,
        };
        this.gestioneProcesso = {
          ...this.gestioneProcesso,
          'GestioneProcesso.DirectLogin': this.directLogin,
        };

        richiestaInizializzata = true;
        await this.retrieveData(
          PEGA_API.loadPage,
          this.requestMapper[PEGA_API.loadPage](IActionLoadRequest.retrieve),
          "Atterraggio dopo login"
        );
      }
    } catch (errore) {
      console.error(`Errore recupero informazioni di redirect`, errore);
    }

    return richiestaInizializzata;
  }

  private async _initWALoggato(){
    if(Helpers.LoginHelper.userLogged){
      try{
        const data1 = await this._localStorageLayer.getData1();
        const name = data1.registry.name;
        const surname = data1.registry.surname;
        const taxCode = data1.registry.taxCodeVAT;

        this._initData = {
          ...this._initData,
          "Contraente.Nome": name,
          "Contraente.Cognome": surname,
          "Contraente.CodiceFiscale": taxCode
        }
      }catch(e){
        console.error("Non é stato possibile recuperare le informazioni dell'utente", e);
      }
    }
  }

  protected async init() {
    this._initObservables();
    this._initContext();

    this.statusService.resetReferencesContraente();
    this.isProdEnv = !Helpers.EnvironmentHelper.isLocalDevelopmentEnvironment(); // TODO: REMOVE, TEST ENVIRONMENT

    await this._initWALoggato();
    if(!(await this._initMock())) {
      this.paymentData = await this.paymentService.getPaymentData();
      if (this.paymentData) {
        await this._initPagamento();
      } else {
        if(!(await this._initRedirectLogin())) {
          if (this.config.showCarrello) {
            await this._chiamaRecuperoCarrello();
          } else if(this.config.showRichiestaDiContatto){
            await this.chiamaRichiestaDiContatto();
          } else if (this.listaQuotazioniSupportate.includes(this.config?.productType as AmbitoDiBisognoCode)) {
            console.warn("Inizializzazione caso quotazione fuori pega");
            this.showQuotazione = true;
            this.ambitoDiBisognoCodeQuotazione = this.config.productType as AmbitoDiBisognoCode;
          } else if(this.config.doRecupero){
              await this.retrieveData(
                PEGA_API.loadPage,
                this.requestMapper[PEGA_API.loadPage](IActionLoadRequest.create),
                "Creazione nuovo case recupero preventivo"
              );
          }else {
            console.warn("Inizializzazione nuovo case pega");
            this.dxModule.proposal_dx_flow = 'richiesta';
            this.dxModule.proposal_dx_source = 'web';

            if(
              this.config.doRetrieve &&
              this.config.doRetrieveAssignmentId &&
              this.config.doRetrieveCaseId
            ){
              this.statusService.assignmentId = this.config.doRetrieveAssignmentId;
              this.env = "UNICO"
              this.statusService.caseID = this.config.doRetrieveCaseId;
              await this.retrieveData(
                PEGA_API.loadPage,
                this.requestMapper[PEGA_API.loadPage](IActionLoadRequest.retrieve),
                "Creazione nuovo case con retrieve"
              );
            }else{
              await this.retrieveData(
                PEGA_API.loadPage,
                this.requestMapper[PEGA_API.loadPage](IActionLoadRequest.create),
                "Creazione nuovo case"
              );
            }

            this._initData = undefined;
          }
        }
      }
    }
  }

  //endregion

  public async actionManagement(action: GenericDataType) {
    //region Salvataggio e recupero dati
    const formData = {
      ...this.statusService.getFormGroupValue(),
      ...this.statusService.getControlsValueOfFormArray()
    };

    await this._userDataService.storeUserData(formData);
    this.formValue = {
      ...formData,
      ...await this._userDataService.getStoredUserData()
    }

    if(this.statusService.getAssignmentOrigineRoc())
      this.formValue['assignmentOrigine'] = this.statusService.getAssignmentOrigineRoc();

    //endregion
    this.tooltipID = action.tooltipID;
    this.refreshFor = undefined;
    this.callBackAfterRetrieve = action.callBackAfterRetrieve;
    this.retrieveType = action?.retrieveType;
    this.VediEModifica = action?.VediEModifica ? action.VediEModifica[0] : undefined;

    this.gestioneProcesso = action?.gestioneProcesso;
    if (this.target !== 'modalDialog') {
      // se sono in un modalDialog riuso il ProcessManagement precedente
      this.processManagement = action?.processManagement;
    }

    switch (action.actionName) {
      case 'takeAction':
        this.actionID = action?.action?.actionProcess?.actionName;
        await this.retrieveData(
          PEGA_API.nextPage,
          this.requestMapper[PEGA_API.nextPage](),
          "Gestione azione takeAction",
          false
        );
        break;
      case 'refresh':
        //Filtriamo le reference nel caso si singolo reference
        this.formValue = this.statusService.filtraReferenceSingoli(this.formValue);
        this.refreshFor = action?.action?.refreshFor;
        await this.retrieveData(
          PEGA_API.updatePage,
          this.requestMapper[PEGA_API.updatePage](),
          "Gestione azione refresh",
          false
        );
        break;
      case 'localAction':
        this.actionIDTemp = this.actionID;
        this.actionID = action?.action?.actionProcess?.localAction;
        this.target = action?.action?.actionProcess?.target;
        await this.retrieveData(
          PEGA_API.updatePage,
          this.requestMapper[PEGA_API.updatePage](),
          "Gestione azione localAction",
          false
        );
        break;
      case 'finishAssignment':
        EventsMethod.pulisciStoredGestioneProcessoStoredValues();
        AccordionComponent.clearOpenAccordionStatus();

        this.formValue = this.statusService.applicaSubmitCallback(this.formValue);
        this.formValue = this.statusService.filtraReferenceIndirizzo(this.formValue);
        this.actionID = null;
        await this.retrieveData(
          PEGA_API.nextPage,
          this.requestMapper[PEGA_API.nextPage](),
          "Gestione azione finishAssignment",
          false
        );
        break;
      case 'closeContainer':
        if(this.callBackAfterRetrieve && this.callBackAfterRetrieve === 'closeModalAndReloadPage') {
          this.callBackAfterRetrieve = undefined;
          await this.closeModal({ refreshPage: true, target: "replaceCurrent" });
        }
        break;
    }

    if(this.codaAzioni.length === 0) {
      this.callCompleted = true;
      this.messageService.setLoading(false);
    }else {
      await this.actionManagement(this.codaAzioni.shift());
    }
  }

  public async retrieveData(api: PEGA_API, request: GenericDataType, casoRichiesta = "", ignoraDatiRicevuti = false): Promise<boolean> {
    let erroreAniaPresente = false;
    let erroreGenericoPresente = false;

    //Chiudiamo subito la modale
    if(this.callBackAfterRetrieve && this.callBackAfterRetrieve === 'closeModalAndRefreshPage')
      this.modalView = undefined;

    if(casoRichiesta.length !== 0)
      console.warn("Retrieve dati:", casoRichiesta);

    this.reloadVisible = api !== PEGA_API.loadPage;

    this.messageService.setLoading(this.target !== 'overlay');

    try{
      let retrievedData = {
        metaBodyResponse: {actionId: '', assignmentId: ''},
        pegaBodyResponse: {caseID: '', view:{groups: []}},
        pegaErrorMessages: undefined,
        tpdAction: undefined
      };

      let pegaResponse = undefined;
      if(Helpers.EnvironmentHelper.isClientSide()){
        if(window.location.href.includes('?mock=true')){
          console.warn("MockCall: API (", api, ") REQUEST ->", request);
          pegaResponse = await this.assignmentService.nextCallData();
        }else pegaResponse = await (await this.interpreteService.mappaChiamateApi[api](request)).json();
      }

      if(pegaResponse.pegaErrorMessages || pegaResponse?.pegaBodyResponse?.view?.viewID === 'pyActionDisplayError'){
        this.visualizzaModaleErrore = true;
        if(pegaResponse.pegaErrorMessages){
          erroreAniaPresente = true;
          const datiParsati = ModaleErroreProdottoUnicoComponent.ParsaInformazioniModale(
            pegaResponse.pegaErrorMessages,
            undefined,
            undefined,
            () => {
              this.chiamaRichiestaDiContatto();
              this.visualizzaModaleErrore = false;
            }
          )
          if(datiParsati){
            this.tipoModaleErrore = datiParsati.tipoModale;
            this.datiModaleErrore = datiParsati.datiModale;
          }
        }else{
          erroreGenericoPresente = true;
          ignoraDatiRicevuti = true;
          this.tipoModaleErrore = TipoModaleErrore.ModaleErroreGenerica;
          this.datiModaleErrore = {
            messaggioHTML: `<span>Errore durante la richiesta</span>`
          };
        }
      }else{
        retrievedData = pegaResponse;
      }

      if(!ignoraDatiRicevuti){
        const metaBodyResponse = retrievedData.metaBodyResponse;
        const pegaBodyResponse = retrievedData.pegaBodyResponse;
        //const analyticsBodyResponse = retrievedData.analyticsBodyResponse;
        const tpdAction = retrievedData.tpdAction;

        this.actionID = metaBodyResponse.actionId;
        this.statusService.assignmentId = metaBodyResponse.assignmentId;
        this.statusService.caseID = pegaBodyResponse.caseID;
        this._restoreCaseService.restoreData = {
          assignmentId: this.statusService.assignmentId,
          caseId: this.statusService.caseID,
          env: this.env,
          productType: this.config.productType
        };

        switch (tpdAction){
          case 'calculateQuote':
            Helpers.RouterHelper.goTo('/disambiguazione');
            break;
          case 'emptyCart':
            this.showCarrelloVuoto = true;
            break;
          case 'reloadBackPage':
            this._chiamaUltimoCasePreROC();
            break;
        }

        switch (this.target) {
          case 'modalDialog':
            this.modalActionID = metaBodyResponse.actionId;
            if (metaBodyResponse?.actionId === this.modalActionID) {
              this.assurancePopup.refreshOnClosePopupAssurance = this.numberModifyPopupAssurance > 0;
              this.numberModifyPopupAssurance++;
            }

            if (Helpers.EnvironmentHelper.isClientSide()){
              const rootElement = document.documentElement;
              rootElement.style.overflow = 'hidden';
            }

            this.modalView = pegaBodyResponse?.view;
            //this.analyticsInterprete.sendAnalyticsPopup(pegaBodyResponse);
            this.statusService.resetTooltipElement();
            break;
          case 'overlay':
            this.target = undefined;
            this.messageService.sendOverlayDataSubject({
              id: this.tooltipID,
              data: pegaBodyResponse.view?.groups,
            });
            this.statusService.addTooltipElement(
              this.tooltipID,
              pegaBodyResponse.view?.groups
            );
            break;
          default:
            this.view = pegaBodyResponse?.view;
            this.statusService.resetTooltipElement();
            break;
        }

        if(this.callBackAfterRetrieve){
          //L'annullamento della callbackAfterRetrieve non deve essere fatto in maniera centralizzata
          if(typeof this.callBackAfterRetrieve === 'function') {
            this.callBackAfterRetrieve();
            this.callBackAfterRetrieve = undefined;
          }else {
            this.callBackAfterRetrieve = undefined;
            await this.closeModal({ refreshPage: true, target: "replaceCurrent" });
          }
        }

        if (api === PEGA_API.nextPage) {
          if (!this.utilsService.scrollOnTop) {
            this.utilsService.scrollOnTop = true;
            Helpers.EnvironmentHelper.isClientSide() && window.scrollTo(0, 0);
          } else {
            Helpers.EnvironmentHelper.isClientSide() && window.scrollTo(0, 0);
          }
        }

        /*
        this.analyticsInterprete.createBodyAnalytics(
          pegaBodyResponse,
          analyticsBodyResponse,
          this.dxModule,
          this.productModule
        ); //aggiungere analytics modulo DX*/
      }

      if(this.codaAzioni.length === 0)
        this.messageService.setLoading(false);
    }catch(e){
      erroreGenericoPresente = true;
      let error = e;
      try {
        error = await e;
      } catch (e) {
        console.error("Non é stato possibile recuperare gli errori", String(e));
      }

      console.error("Errore retrieve dei dati PEGA", error);
      if (error?.status === 401) {
        this.erroreRecuperaPreventivo = true;
        this.errorMessage = 'Accesso non consentito. Preventivo momentaneamente in carico al CS.';
      }

      this.closeModal({ refreshPage: false, target: "replaceCurrent" });
      this.closePopup();
      this.messageService.setLoading(false);
      this.messageService.setError(true);
    }

    return erroreAniaPresente || erroreGenericoPresente;
  }

  public async closeModal($event) {
    this.target = $event?.target;
    this.modalView = undefined;

    if (Helpers.EnvironmentHelper.isClientSide()) {
      const rootElement = document.documentElement;
      rootElement.style.overflow = 'auto';
    }
    this.numberModifyPopupAssurance = 0;
    this.assurancePopup.refreshOnClosePopupAssurance = false;
    this.actionID = this.actionIDTemp;
    this.actionIDTemp = undefined;
    this.statusService.resetTimerOTP();
    this.statusService.resetNumberFail();

    if ($event && $event.refreshPage) {
      await this.retrieveData(
        PEGA_API.loadPage,
        this.requestMapper[PEGA_API.loadPage](IActionLoadRequest.reload),
        "Chiusura modale con evento refreshPage"
      );
    }

    $event.closeModalCompleteCallback && $event.closeModalCompleteCallback();
  }

  public closePopup() {
    this.actionID = this.actionIDTemp;
    this.actionIDTemp = undefined;
    this.utilsService.resetCloseToolTip();
  }

  public async sendPaymentOutcome(errorePagamentoRicorrente = false) {
    this.messageService.setLoading(true);
    this.configInputSuccess = await this.utilsService.getInputSuccess();
    //let isPYOk = true;

    if (errorePagamentoRicorrente) {
      //isPYOk = false;
      this.reloadVisible = true;
      this.messageService.setError(true);
    } else {
      if (this.esitoPagamento === 'OK') {
        this.showTYP = !!this.configInputSuccess;
      } else {
        //isPYOk = false;
        this.showErrorePagamento = true;
      }
    }
    /*
    this.analyticsInterprete.sendTYPAnalytics(
      isPYOk,
      this.codErrorPayment,
      this.messageErrorPayment
    );*/
    this.messageService.setLoading(false);
  }

  public async retrieveDataFromStorage() {
    let esito;

    try{
      esito = await Helpers.SessionHelper.getData('pu_data_props');
      await Helpers.SessionHelper.deleteData('pu_data_props');
    }catch(error){
      console.error("Non é stato possibile recuperare le informazioni dallo storage", String(error));
    }

    return esito;
  };

  //region Richieste specifiche interprete

  private _resetStatoChiamatePega(productType?: string, initData?: {[key: string]: string}, resetAssignmentID = true, resetCaseID = true){
    this.codaAzioni = [];
    this.config.productType = productType as AmbitoDiBisognoCode | ProductType;
    if(resetAssignmentID)
      this.statusService.assignmentId = undefined;
    if(resetCaseID)
      this.statusService.caseID = undefined;

    this.idOfferta = undefined;
    this.versione = undefined;
    this.env = 'UNICO';
    this.retrieveType = undefined;
    this.formValue = undefined;
    this.processManagement = undefined
    this.gestioneProcesso = initData || {};
    this.VediEModifica = undefined;
    this.abTesting = undefined;
    this._initData = initData || {};
  }

  private async _chiamaRecuperoCarrello(){
    this.showCarrelloVuoto = false;
    const codiceFiscale = await this._userDataService.storedCodiceFiscale();
    if (codiceFiscale) {
      try {
        const carrelloAttivo = await this._cartService.recuperaCarrelloAttivo(codiceFiscale);
        this._carrello = carrelloAttivo.CarrelloAttivo;
        this.showCarrelloVuoto = this._carrello?.length === 0 || !this._carrello;
        if (
          !this.showCarrelloVuoto &&
          this._carrello[0].AssignmentID &&
          this._carrello[0].CaseID
        ) {
          this.statusService.assignmentId = this._carrello[0].AssignmentID;
          this.statusService.caseID = this._carrello[0].CaseID;
          this.retrieveType = 'Recupero Carrello';
          this.env = 'UNICO';
          this.config.productType = '' as AmbitoDiBisognoCode | ProductType;
          await this.retrieveData(
            PEGA_API.loadPage,
            this.requestMapper[PEGA_API.loadPage](IActionLoadRequest.retrieve),
            "Recupero del carrello attivo"
          );
        }
      } catch (error) {
        this.showCarrelloVuoto = true;
        console.error('Errore nel recupero del carrllo vuoto: ', error);
      }
    }else{
      this.showCarrelloVuoto = true;
    }
  }

  private _chiamataDiReloadInterprete(){
    this.statusService.assignmentId = undefined;

    this.retrieveData(
      PEGA_API.loadPage,
      this.requestMapper[PEGA_API.loadPage](IActionLoadRequest.reload),
      "Azione di reload a seguito di errore"
    );
    this.messageService.setError(false);
  }

  private _customCreateInterprete(productType?: string, gestioneProcesso?: GenericDataType){
    this._resetStatoChiamatePega(productType, {...gestioneProcesso});
    this.retrieveData(
      PEGA_API.loadPage,
      this.requestMapper[PEGA_API.loadPage](IActionLoadRequest.create),
      "Chiamata alla custom create"
    );
  }

  public async quotazioneProseguiHandler(data: { [key: string]: string }) {
    this._initData = {
      ...data
    };

    const esito = await this.retrieveData(
      PEGA_API.loadPage,
      this.requestMapper[PEGA_API.loadPage](IActionLoadRequest.create),
      "Caso quotazione prosegui"
    );

    if(!esito){
      this._initData = undefined;
      this.showQuotazione = false;
    }
  }

  public chiamaRichiestaDiContatto(datiAggiuntivi?: GenericDataType) {
    this.statusService.assignmentId && this.statusService.setAssignmentOrigineRoc(this.statusService.assignmentId);

    this._initData = datiAggiuntivi;
    this.showQuotazione = false;
    this.config.productType = ProductType.UNICO;
    this.env = 'UNICO';
    this.retrieveType = 'Richiesta Contatto';
    this._initData = {
      ...(datiAggiuntivi || {}),
      ...(this.statusService.assignmentId ? {'AssignmentOrigine': this.statusService.assignmentId} : {'GestioneProcesso.PreVenditaRoC': true})
    }
    this.retrieveData(
      PEGA_API.loadPage,
      this.requestMapper[PEGA_API.loadPage](IActionLoadRequest.create),
      "Caso richiesta di contatto"
    );
  }

  private async _chiamaUltimoCasePreROC(){
    this.statusService.assignmentId = undefined;
    this.statusService.caseID = this.statusService.getCaseIdOrigineRoc();

    const chiamataInErrore = await this.retrieveData(
      PEGA_API.loadPage,
      this.requestMapper[PEGA_API.loadPage](IActionLoadRequest.reload),
      "Azione di reload dopo ultimo back da flusso ROC"
    );

    if(!chiamataInErrore)
      this.statusService.resetAssignmentOrigineRoc();

    this.messageService.setError(false);
  }

  private async _chiamaPaginaErroreInterna(gestioneProcesso?: GenericDataType, motivoFallimento?: string, actionID?: string){
    if(motivoFallimento)
      this.motivoFallimento = motivoFallimento;
    if(gestioneProcesso){
      //Facciamo una chiamata silente a pega per chiudere il case
      this._resetStatoChiamatePega(this.statusService.configInterprete.productType, gestioneProcesso, false, false);
      if(actionID)
        this.actionID = actionID;
      try{
        const requestPega = this.requestMapper[PEGA_API.nextPage]();

        //Campo utilizzato da BFF per le logiche di visualizzazione della failure page
        requestPega['action'] = 'GO_TO_FAILURE_PAGE';

        const response = await this.interpreteService.mappaChiamateApi[PEGA_API.nextPage](requestPega);
        const responseJson = await response.json();
        if(responseJson.tpdAction === 'failurePage')
          this.showPaginaFallimento = true;
      }catch (e){
        console.error("Errore nella chiamata silente", e);
      }
      console.warn("Chiamata Silente post click button");
    }

    this.closeModal({refreshPage: false, target: 'replaceCurrent'});
  }

  //endregion

  public ngOnDestroy() {
    this.actionsSubscription && this.actionsSubscription.unsubscribe();
    this.mockSubscription && this.mockSubscription.unsubscribe();
    this.loaderSubscription && this.loaderSubscription.unsubscribe();
    this.errorSubscription && this.errorSubscription.unsubscribe();
    this.errorSubscriptionReload && this.errorSubscriptionReload.unsubscribe();
    this.successPaymentRicorrente && this.successPaymentRicorrente.unsubscribe();
    this.errorPaymentRicorrente && this.errorPaymentRicorrente.unsubscribe();
    this.closedTooltipSubscription && this.closedTooltipSubscription.unsubscribe();
  }

  //region beam

  public get customLoading(): boolean{
    return this.messageService.customLoadingFlag;
  }

  public get customLoadingType(): 'showPrice' | 'warranty'{
    return this.messageService.customLoadingType;
  }

  public get paeseDesinazione(): string {
    let esito = '';

    if (this.config && this.config.initData)
      esito = this.config.initData['PaeseDestinazione'] ?? '';

    return esito;
  }

  public get dataNascita(): string {
    let esito = '';

    if (this.config && this.config.initData)
      esito = this.config.initData['DataDiNascita'] ?? '';

    return esito;
  }

  //endregion
}
