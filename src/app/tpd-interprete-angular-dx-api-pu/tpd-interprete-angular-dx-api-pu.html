<ng-container *ngIf="!this.isServerSideRendering; else serverSideRenderingInterprete">
  <div
    class="wrapper-container"
    [hidden]="hasError || showTYP || showErrorePagamento || showCarrelloVuoto || showPaginaFallimento">
    <top-level-view [view]="view"></top-level-view>
    <div *ngIf="modalView" [ngClass]="{'backgroundModal': modalView }"></div>
  </div>

  <modal-dx-api
    #assurancePopup
    [data]="modalView"
    [hidden]="!modalView"
    (closeModalEmitter)="closeModal($event)">
  </modal-dx-api>
  <!--region Gestione errori e caricamento-->
  <tpd-loading
    *ngIf="!this.customLoading"
    [isLoading]="isLoadingWithOverlay"
    [overlayedSpinner]="true"
    [overlayTitle]="overlayTitle">
  </tpd-loading>
  <custom-loading-interprete
    *ngIf="this.customLoading"
    [tipoLoading]="this.customLoadingType"
    [completaLoading]="this.completaCaricamentoCustom"
    [mostraCaricamento]="this.isLoadingWithOverlay">
  </custom-loading-interprete>
  <tpd-errors
    data-chain="true"
    data-reload="true"
    data-text_reload="Riprova"
    [errorId]="errorId"
    [hidden]="!hasError"
    [blueVersion]="true"
    *ngIf="reloadVisible"
  ></tpd-errors>
  <tpd-errors
    data-chain="true"
    [errorId]="errorIdNotVisible"
    [hidden]="!hasError"
    [blueVersion]="true"
    *ngIf="!reloadVisible && !errorMessage"
  ></tpd-errors>
  <tpd-errors
    data-chain="true"
    [customMessageRecuperaPreventivo]="erroreRecuperaPreventivo"
    [showButtonBack]="true"
    [messageToDisplay]="errorMessage"
    [errorId]="errorIdNotVisible"
    [hidden]="!hasError"
    [blueVersion]="true"
    *ngIf="!reloadVisible && errorMessage"
  ></tpd-errors>
  <modale-errore-prodotto-unico
    *ngIf="this.visualizzaModaleErrore"
    [tipoModaleErrore]="this.tipoModaleErrore"
    [modaleErroreData]="this.datiModaleErrore"
    (onCloseModal)="this.visualizzaModaleErrore = false">
  </modale-errore-prodotto-unico>
  <!--endregion-->
  <!--region Pagine di gestione flusso-->
  <thank-you-page *ngIf="this.showTYP" [infoAcquisto]="this.configInputSuccess"></thank-you-page>
  <fallimento-pagamento *ngIf="this.showErrorePagamento" [nome]="this.configInputSuccess.nomeContraente"></fallimento-pagamento>
  <pagina-fallimento *ngIf="this.showPaginaFallimento" [motivoFallimento]="this.motivoFallimento"></pagina-fallimento>
  <carrello-vuoto *ngIf="this.showCarrelloVuoto"></carrello-vuoto>
  <!--endregion-->
  <!--region Pagine di quotazione fuori pega-->
  <ng-container *ngIf="this.showQuotazione && !this.hasError">
    <ng-container [ngSwitch]="this.ambitoDiBisognoCodeQuotazione">
      <quotazione-viaggi
        *ngSwitchCase="this.ambitoDiBisognoCode.VIAGGI"
        [paeseDiDestinazione]="paeseDesinazione"
        [proseguiClick]="this.quotazioneProseguiHandler.bind(this)"
        [richiediContattoClick]="this.chiamaRichiestaDiContatto.bind(this)"
      ></quotazione-viaggi>
      <quotazione-malattia
        *ngSwitchCase="this.ambitoDiBisognoCode.SALUTE"
        [dataNascita]="dataNascita"
        [proseguiClick]="this.quotazioneProseguiHandler.bind(this)"
      ></quotazione-malattia>
      <quotazione-infortuni
        *ngSwitchCase="this.ambitoDiBisognoCode.INFORTUNI"
        [dataNascita]="dataNascita"
        [proseguiClick]="this.quotazioneProseguiHandler.bind(this)"
      ></quotazione-infortuni>
    </ng-container>
  </ng-container>
  <!--endregion-->
</ng-container>

<ng-template #serverSideRenderingInterprete>
  <div class="SSRLoader"></div>
</ng-template>
