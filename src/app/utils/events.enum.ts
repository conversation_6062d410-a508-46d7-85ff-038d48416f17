/**
 * @description Enum di tutti gli eventi sui field gestiti.
 * N.B: Non tutti i componenti field gestiscono tutti gli eventi
 *  (esempio, su un radio button non ha senso parlare di blur e focus )
 */
export enum EventsManaged {
  click = 'click',
  hover = 'hover',
  blur = 'blur',
  focus = 'focus',
  change = 'change',
}

export const SupportedActions = [
  'finishAssignment',
  'localAction',
  'refresh',
  'takeAction',
  'runDataTransform',
  'closeContainer'
];
