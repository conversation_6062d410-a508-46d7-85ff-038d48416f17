import { AbstractControl } from '@angular/forms';
import { RegExps } from '@tpd-web-angular-libs/angular-library';

export const validation = {
  number: RegExps.numeric,
  houseNumber: RegExps.civico,
  email: RegExps.email,
  mobilephone: RegExps.phoneNumberafuv16,
  taxCode: RegExps.codFiscale,
  microchip: RegExps.microchip,
};

// this function removes single error
export function removeError(control: AbstractControl, error: string) {
  const err = control.errors; // get control errors
  if (err) {
    delete err[error]; // delete your own error
    if (!Object.keys(err).length) {
      // if no errors left
      control.setErrors(null); // set control errors to null making it VALID
    } else {
      control.setErrors(err); // controls got other errors so set them back
    }
  }
}

// this function adds a single error
export function addError(control: AbstractControl, error: string) {
  const errorToSet = { [error]: true };
  control.setErrors({ ...control.errors, ...errorToSet });
}

//regExp layout
export const regexpRowSpace =
  /^Row(?<AvaiableSpace>( (L|R)){0,4})(?<ColWidth>( (\d+ \d+)){0,4})$/gim;
export const regexpRow =
  /^Row(?<Alignment>( (L|C|R|SA|SB|SE)(T|C|B)){1,4})(?<Gap>( \d+){0,4})(?<MWidth> MW\s*\d+)?$/gim;
export const regexpCol =
  /^Col(?<Alignment>( (T|C|B)(L|C|R|SA|SB|SE)){1,4})(?<Gap>( \d+){0,4})(?<MWidth> MW\s*\d+)?$/gim;
export const regexpCard =
  /^Card (?<bgColor>[a-z,1-9]+)(?<borderRadius>( \d+){1,2})(?:\s(?<border>[\d+]{1,3}\s[a-z,1-9]+))?(?:(?<padding>( \d+){1,4}))?$/gim;
export const regexpBox =
  /^Box (?<colorBox>[a-z,1-9]+)(?:\s(?<border>[\d+]{1,3}\s[A-Z]{2}))?(?:(?<paddingBox>( \d+){1,4}))?$/gim;
