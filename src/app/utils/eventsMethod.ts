import {
  IActionSet,
  IField,
  IGestioneProcesso,
  IProcessManagement, IStoredGestioneProcesso,
  IStoredGestioneProcessoChecker
} from "../models/models.commons";
import { AnalyticsInterprete } from "../services/analytics.interprete.service";
import { MessagesService } from "../services/messages.service";
import { EventsManaged } from "./events.enum";
import { StatusService } from "../services/status.service";

interface EventsInterface {
    onClick(...args: unknown[]): unknown;
    onHover(...args: unknown[]): unknown;
    onBlur(...args: unknown[]): unknown;
    onFocus(...args: unknown[]): unknown;
    onChange(...args: unknown[]): unknown;
  }

  export class EventsMethod implements EventsInterface {
    private static _storedGestioneProcessoStoredValues: IStoredGestioneProcesso = {};
    public static pulisciStoredGestioneProcessoStoredValues = () => EventsMethod._storedGestioneProcessoStoredValues = {};

    public eventsManaged = EventsManaged;
    public actionsSet: IActionSet[] = [];
    public processManagement!: IProcessManagement;
    public gestioneProcesso?: IGestioneProcesso;
    public field?: IField;

    //Rappresenta gli id di gestione processo che devono essere memorizzati
    private _storedGestioneProcessoChecker: IStoredGestioneProcessoChecker = {
      'GestioneProcesso.IdPosizioneDaCancellare': true,
      'GestioneProcesso.CodProdottoDaCancellare': true,
      'GestioneProcesso.DescBeneWPT': true,
      'GestioneProcesso.TempSezValInt': true,
      'GestioneProcesso.TempGarValInt': true
    }

    constructor(
      public messageService: MessagesService,
      public analytiicsService: AnalyticsInterprete
    ) {}

    public checkSendAnalytics() {
      if (
        this.field &&
        this.field.customAttributes &&
        this.field.customAttributes.analytics_track_enabled
      ) {
        const analyticsActionPayload = {};
        const cAttributes = this.field.customAttributes;

        Object.keys(cAttributes).forEach((k) => {
          if (k.includes('analytics_')) {
            if (k !== 'analytics_track_enabled' && !k.includes('analytics_app')) {
              // per escludere le proprietà per app

              let analyticsK: string;

              if (k.includes('analytics_web_')) {
                //Analytics properties solo per web
                analyticsK = k.replace('analytics_web_', '');
              } else {
                //Analytics properties generiche
                analyticsK = k.replace('analytics_', '');
              }

              (analyticsActionPayload as unknown)[analyticsK] = cAttributes[k];
            }
          }
        });

        //console.log(analyticsActionPayload, 'AnalyticsForAction')//to do rimuovere
        this.analytiicsService.sendAnalyticsAction(analyticsActionPayload);
      }
    }

    //region VI processManagement

    public resetProcessManagement() {
      this.processManagement = {
        'ProcessManagement.NextStep': undefined,
        'ProcessManagement.IndiceGaranziaSelezionata': undefined,
        'ProcessManagement.IndiceSezioneSelezionata': undefined,
        'ProcessManagement.IdGaranzia': undefined,
        'ProcessManagement.ValoreGaranzia': undefined,
      };
    }

    public setValueProcessManagement() {
      this.resetProcessManagement();
      if (this.field) {
        const customAttributes = this.field.customAttributes;
        const nextStep =
          customAttributes && customAttributes['ProcessManagement.NextStep'];
        if (
          this.processManagement &&
          this.processManagement['ProcessManagement.NextStep']
        ) {
          this.processManagement['ProcessManagement.NextStep'] = nextStep;
        }

        const idGaranzia =
          customAttributes && customAttributes['ProcessManagement.IdGaranzia'];
        if (
          this.processManagement &&
          this.processManagement['ProcessManagement.IdGaranzia']
        ) {
          this.processManagement['ProcessManagement.IdGaranzia'] = idGaranzia;
        }

        const valoreGaranzia =
          customAttributes &&
          customAttributes['ProcessManagement.ValoreGaranzia'];
        if (
          this.processManagement &&
          this.processManagement['ProcessManagement.ValoreGaranzia']
        ) {
          this.processManagement['ProcessManagement.ValoreGaranzia'] =
            valoreGaranzia;
        }

        let isActionOpenPopupAssurance = false;
        for (const a of this.actionsSet) {
          if (a.actions) {
            isActionOpenPopupAssurance = a.actions.some(
              (act) =>
                act?.actionProcess?.localAction === 'OpenPopupAssurance' ||
                act?.actionProcess?.localAction === 'OpenPopupInterdip'
            );
          }
        }
        if (isActionOpenPopupAssurance) {
          // se ci sono azioni di openPopupAssurance
          // devo passare il processManagement con gli indici di sezione e garanzia
          const regex = /[()]/;
          const referenceWords = this.field?.reference?.split(regex);
          if (
            this.processManagement &&
            this.processManagement['ProcessManagement.IndiceSezioneSelezionata']
          ) {
            this.processManagement['ProcessManagement.IndiceSezioneSelezionata'] =
              referenceWords ? referenceWords[1] : undefined;
          }
          if (
            this.processManagement &&
            this.processManagement['ProcessManagement.IndiceGaranziaSelezionata']
          ) {
            this.processManagement[
              'ProcessManagement.IndiceGaranziaSelezionata'
            ] = referenceWords ? referenceWords[3] : undefined;
          }
        }
      }
    }

    private _resetGestioneProcesso() {
      this.gestioneProcesso = {};
    }

    //endregion

    //region PU gestioneProcesso

    private _controllaEsistenzaAzioniLocali(...azioniLocali: string[]): boolean{
      let azioneLocaleEsiste = false;
      for (const a of this.actionsSet) {
        if (a.actions) {
          azioneLocaleEsiste = a.actions.some(
            azione => {
              let condizione = false;
              for(const azioneDaControllare of azioniLocali)
                condizione = condizione || azione?.actionProcess?.localAction === azioneDaControllare;
              return condizione
            }
          );
        }
      }
      return azioneLocaleEsiste;
    }

    private _recuperaIndiceDaReference(reference: string, nomeIndice: string): string{
      let esito = undefined;

      if(reference && nomeIndice){
        const regex = new RegExp(`${nomeIndice}\\((?<valore>\\S+?)\\)`);
        const execution = regex.exec(reference);
        esito = execution?.groups?.valore;
      }


      return esito;
    }

    private _controlloIndiciAperturaPopup(){
      if(this._controllaEsistenzaAzioniLocali(
        'OpenPopupAssurance',
        'OpenPopupInterdip',
        'PopupInterdipendenze',
        'PopupDettagliGaranzie',
        'PopupInfoGaranzia',
        'PopupInfoProtExtra'
      )){
        if (this._recuperaIndiceDaReference(this.field.reference, 'Pacchetti'))
          this.gestioneProcesso['GestioneProcesso.IndiceOffertaSelezionata'] = this._recuperaIndiceDaReference(this.field.reference, 'Pacchetti');
        if (this._recuperaIndiceDaReference(this.field.reference, 'Sezioni'))
          this.gestioneProcesso['GestioneProcesso.IndiceSezioneSelezionata'] = this._recuperaIndiceDaReference(this.field.reference, 'Sezioni');
        if (this._recuperaIndiceDaReference(this.field.reference, 'Garanzie'))
          this.gestioneProcesso['GestioneProcesso.IndiceGaranziaSelezionata'] = this._recuperaIndiceDaReference(this.field.reference, 'Garanzie');
        if (this._recuperaIndiceDaReference(this.field.reference, 'ListaAttributi'))
          this.gestioneProcesso['GestioneProcesso.IndiceAttributoGaranziaSelezionata'] = this._recuperaIndiceDaReference(this.field.reference, 'ListaAttributi');
        if (this._recuperaIndiceDaReference(this.field.reference, 'ProtezioniExtra'))
          this.gestioneProcesso['GestioneProcesso.IndiceProtezioneExtraSelezionata'] = this._recuperaIndiceDaReference(this.field.reference, 'ProtezioniExtra');
      }
    }

    private _impostaValoreGestioneProcesso(customAttributes: {[key: string]: string}, nomeGestioneProcesso: string, soloValoriStored = false){
      if(customAttributes){
        const key = `GestioneProcesso.${nomeGestioneProcesso}`
        if(this._storedGestioneProcessoChecker[key] && customAttributes[key]){
          const gestioneProcessoValue = customAttributes[key];
          if(gestioneProcessoValue !== 'true'){
            EventsMethod._storedGestioneProcessoStoredValues[key] = gestioneProcessoValue;
          }
        }else if(!soloValoriStored){
          const gestioneProcessoValue = customAttributes[key];
          if(gestioneProcessoValue && !this.gestioneProcesso[key])
            this.gestioneProcesso[key] = gestioneProcessoValue;
        }
      }
    }

    public settaValoriGestioneProcessoDaStorage(pulisciValoreDopoUso = true){
      const customAttributes = this.field.customAttributes || {};
      for(const key of Object.keys(customAttributes)){
        if(
          key.startsWith("GestioneProcesso") &&
          customAttributes[key] === 'true' &&
          EventsMethod._storedGestioneProcessoStoredValues[key]
        ){
          this.gestioneProcesso[key] = EventsMethod._storedGestioneProcessoStoredValues[key];
          if(pulisciValoreDopoUso)
            delete EventsMethod._storedGestioneProcessoStoredValues[key];
        }
      }
    }

    public settaValoriGestioneProcesso(soloValoriStored = false) {
      !soloValoriStored && this._resetGestioneProcesso();
      if (this.field) {
        const customAttributes = this.field.customAttributes;
        !soloValoriStored && this._controlloIndiciAperturaPopup();
        this._impostaValoreGestioneProcesso(customAttributes, 'StepSuccessivo', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'InviaTramitePosta', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'FlagSalvaPerDopo', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'VaiAModificaDati', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'IdGaranzia', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'ValoreGaranzia', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'AggiungiConvenzione', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'IndiceScontoSelezionato', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'IdPosizioneDaCancellare', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'CodProdottoDaCancellare', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'DescBeneWPT', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'IndiceOffertaSelezionata', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'IndiceSezioneSelezionata', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'IndiceGaranziaSelezionata', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'RimuoviConvenzione', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'MostraBoxResidenza', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'MostraBoxDomicilio', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'ModificaIndirizzo', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'IndiceComunicazioneInCasoDiSinistro', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'TempSezValInt', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'TempGarValInt', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'MostraCardViaggiatore', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'StepSuccessivoViaggiatori', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'IndiceBeneficiario', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'IndiceBeneAggiuntaBeneficiario', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'IndiceBeneReferenteTerzo', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'ModificaBeneficiario', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'ModificaRefTerzo', soloValoriStored);
        this._impostaValoreGestioneProcesso(customAttributes, 'IndiceBeneAggiuntaComproprietario', soloValoriStored);
      }
    }

    //endregion

    //region eventiGenerici
    public onClick(...args: unknown[]) {
      this.messageService.onEventExecuted(
        this.actionsSet,
        EventsManaged.click,
        this.processManagement,
        this.gestioneProcesso
      );
    }

    public onHover(...args: unknown[]) {
      this.messageService.onEventExecuted(
        this.actionsSet,
        EventsManaged.hover,
        this.processManagement,
        this.gestioneProcesso
      );
    }

    public onBlur(...args: unknown[]) {
      this.messageService.onEventExecuted(
        this.actionsSet,
        EventsManaged.blur,
        this.processManagement,
        this.gestioneProcesso
      );
    }

    public onFocus(...args: unknown[]) {
      this.messageService.onEventExecuted(
        this.actionsSet,
        EventsManaged.focus,
        this.processManagement,
        this.gestioneProcesso
      );
    }

    public onChange(...args: unknown[]) {
      if(StatusService.GlobalStatusService){
        const customAttributes = this.field.customAttributes;
        const statusService = StatusService.GlobalStatusService;
        if(customAttributes?.singleReference){
          statusService.referenceSingolo = this.field.reference;
        }
      }

      this.messageService.onEventExecuted(
        this.actionsSet,
        EventsManaged.change,
        this.processManagement,
        this.gestioneProcesso
      );
    }

    //endregion
  }
