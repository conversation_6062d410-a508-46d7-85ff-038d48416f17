// apple pay-gpay constant merchant ID

export const MERCHANT_ID_VALUES = {
  coll: {
    venditaIbridaViaggi: '00009454',
    venditaIbridaPet: '00009454',
    venditaIbridaAll: '00009454',
    venditaIbridaInfortuni: '00009454',
    rinnovi: '00009454',
    insoluti: '00009454',
    preventivi: '00009454',
  },
  inte: {
    venditaIbridaViaggi: '00013590',
    venditaIbridaPet: '00013590',
    venditaIbridaAll: '00013590',
    venditaIbridaInfortuni: '00013590',
    rinnovi: '00013590',
    insoluti: '00013590',
    preventivi: '00013590',
  },
  prod: {
    venditaIbridaViaggi: 'payment_2330788',
    venditaIbridaPet: 'payment_2330788',
    venditaIbridaAll: 'payment_2330788',
    venditaIbridaInfortuni: 'payment_2330788',
    rinnovi: 'payment_870163_cassa',
    insoluti: 'payment_870163_cassa',
    preventivi: 'payment_870163_cassa',
  },
};

export const googlePayMerchantId = 'BCR2DN4TZCJ6PFRJ';

export const appleMerchantID = {
  inte: 'merchant.it.unipolsai.hub.coll',
  coll: 'merchant.it.unipolsai.hub.coll',
  prod: 'merchant.it.unipolsai.hub',
};

// export class GPayRequest implements google.payments.api.PaymentDataRequest {
//   merchantInfo: google.payments.api.MerchantInfo;
//   apiVersion: number;
//   apiVersionMinor: number;
//   emailRequired?: boolean;
//   shippingAddressRequired?: boolean;
//   shippingAddressParameters?: google.payments.api.ShippingAddressParameters;
//   allowedPaymentMethods: google.payments.api.PaymentMethodSpecification[];
//   transactionInfo: google.payments.api.TransactionInfo;
//   offerInfo?: google.payments.api.OfferInfo;
//   shippingOptionRequired?: boolean;
//   shippingOptionParameters?: google.payments.api.ShippingOptionParameters;
//   callbackIntents?: google.payments.api.CallbackIntent[];

//   constructor() {
//     /* DONOTHING */
//   }
// }

/***
 * @param assignmentId assignmentId corrente per richiamre PEGA dopo la redirect nexi
 * @param returnFromAutorizza flag per capire se riatterro dopo un autorizza
 */
export interface IStoragePaymentData {
  assignmentId: string;
  returnFromAutorizza: boolean;
  idPreventivo?: string;
  versione?: string;
  codiceFiscale?: string;
  importo?: string;
  aliasToken?: string;
  nonceRequestId?: string;
}
