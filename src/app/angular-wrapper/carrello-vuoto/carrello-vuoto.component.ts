import { Component, ElementRef, Inject, Input, ViewChild } from '@angular/core';
import { AngularMFE, CommonMFE } from '@tpd-web-common-libs/nodejs-library';
import { AppProps } from 'src/app/app.props';
import { BehaviorSubject } from 'rxjs';

const AngularMicroFrontendClass = AngularMFE.Class.AngularMicrofrontendFactory({
  BehaviorSubject,
  AppProps,
});

@Component({
  templateUrl: './carrello-vuoto.component.html',
  selector: 'carrello-vuoto',
})
export default class CarrelloVuotoComponent extends AngularMicroFrontendClass {
  @ViewChild('Container', { read: ElementRef }) public container: ElementRef;
  @Input() public data: { [key: string]: any };

  get remoteDependencies(): CommonMFE.Types.TRemoteDependency[] {
    return [
      {
        elementRef: this.container.nativeElement,
        props: {
          ...this.appProps,
          data: this.data,
          id: `${this.appProps.id}-carrello-vuoto`,
          __mfeName__: 'tpdCarrelloVuoto',
        },
        updateProps: (newProps) => {
          return {
            ...newProps,
            id: `${this.appProps.id}-carrello-vuoto`,
            data: this.data,
            __mfeName__: 'tpdCarrelloVuoto',
          };
        },
      },
    ];
  }

  constructor(@Inject(AppProps) private appProps: AppProps) {
    super(appProps);
  }
}
