import { Component, ElementRef, Input, Output, ViewChild, EventEmitter, Inject } from "@angular/core";
import { AngularMFE, CommonMFE } from "@tpd-web-common-libs/nodejs-library";
import { BehaviorSubject } from "rxjs";
import { AppProps } from "../../app.props";

export interface AgencyModel{
  [key: string]: any
}

export interface InstallerModel{
  [key: string]: any
}

const AngularMicroFrontendClass = AngularMFE.Class.AngularMicrofrontendFactory({
  BehaviorSubject,
  AppProps,
});

@Component({
  selector: 'locator-wrapper',
  templateUrl: './locator-wrapper.component.html'
})
export default class LocatorWrapperComponent extends AngularMicroFrontendClass{
  @ViewChild('locatorWrapperContainer', { read: ElementRef }) public container: ElementRef;

  @Input() stepCorrente: 'scelta_agenzia' | 'scelta_installatore';

  @Output() onAgencySelected = new EventEmitter<AgencyModel>();
  @Output() onInstallerSelected = new EventEmitter<InstallerModel>();
  @Output() onComponentReady = new EventEmitter<boolean>();

  private _staticData = {
    isConfiguratore: true,
    showFilters: false,
    selectedAddress: '',
    showInfoWindow: true,
    isProdottoUnico: true,
    onAgencySelected: (agency) => this.onAgencySelected.emit(agency),
    onInstallerSelected: (installer) => this.onInstallerSelected.emit(installer),
    onLoadData: () => this.onComponentReady.emit(true)
  }

  public get remoteDependencies(): CommonMFE.Types.TRemoteDependency[] {
    return [
      {
        elementRef: this.container.nativeElement,
        props: {
          ...this.appProps,
          id: `${this.appProps.id}-locator`,
          __mfeName__: 'tpdLocatorWidget',
          currentStep: this.stepCorrente,
          ...this._staticData
        },
        updateProps: (newProps) => {
          return {
            ...newProps,
            id: `${this.appProps.id}-carrello-vuoto`,
            __mfeName__: 'tpdLocatorWidget',
            currentStep: this.stepCorrente,
            ...this._staticData
          };
        },
      },
    ];
  }

  constructor(@Inject(AppProps) private appProps: AppProps) {
    super(appProps);
  }
}
