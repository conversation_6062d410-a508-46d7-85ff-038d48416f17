import { Component, ElementRef, Inject, Input, ViewChild } from '@angular/core';
import { AngularMFE, CommonMFE } from '@tpd-web-common-libs/nodejs-library';
import { AppProps } from 'src/app/app.props';
import { BehaviorSubject } from 'rxjs';
import { InputSuccess } from "../../services/utils.service";

const AngularMicroFrontendClass = AngularMFE.Class.AngularMicrofrontendFactory({
  BehaviorSubject,
  AppProps,
});

@Component({
  templateUrl: './thank-you-page.component.html',
  selector: 'thank-you-page',
})
export default class ThankYouPageComponent extends AngularMicroFrontendClass {
  @ViewChild('Container', { read: ElementRef }) public container: ElementRef;
  @ViewChild('navigationHeader', { read: ElementRef }) public navigationHeader: ElementRef;

  public fieldStepper = {
    "customAttributes": {
      "currentStep": "3",
      "labelFormatDeselected": "TEXT APP GRL13 WEB GRL14 GRL14 GRL14",
      "labelFormatPreviousStep": "TEXT APP BDL13 WEB BDL14 BDL14 BDL14",
      "style": "StepperPU",
      "labelFormatCurrentStep": "TEXT APP BDM13 WEB BDM14 BDM14 BDM14",
      "labelsStepper": "Preventivo|Carrello|Acquisto"
    }
  }

  @Input() public infoAcquisto: InputSuccess;

  get remoteDependencies(): CommonMFE.Types.TRemoteDependency[] {
    return [
      {
        elementRef: this.container.nativeElement,
        props: {
          ...this.appProps,
          id: `${this.appProps.id}-thank-you-page`,
          __mfeName__: 'tpdThankYouPage',
          infoAcquisto: this.infoAcquisto,
          showHeader: false
        },
        updateProps: (newProps) => {
          return {
            ...newProps,
            id: `${this.appProps.id}-thank-you-page`,
            __mfeName__: 'tpdThankYouPage',
            infoAcquisto: this.infoAcquisto,
            showHeader: false
          };
        },
      },
      {
        elementRef: this.navigationHeader.nativeElement,
        props: {
          ...this.appProps,
          __mfeName__: 'tpdNavigationHeader',
          title: 'Il tuo acquisto',
          hideBackBtn: true,
          hideCloseBtn: true,
        },
        updateProps: (newProps) => {
          return {
            ...newProps,
            __mfeName__: 'tpdNavigationHeader',
            title: 'Il tuo acquisto',
            hideBackBtn: true,
            hideCloseBtn: true,
          };
        },
      },
    ];
  }

  constructor(@Inject(AppProps) private appProps: AppProps) {
    super(appProps);
  }
}
