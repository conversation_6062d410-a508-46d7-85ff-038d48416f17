import { Component, ElementRef, Inject, Input, ViewChild } from '@angular/core';
import { AngularMFE, CommonMFE, Helpers } from '@tpd-web-common-libs/nodejs-library';
import { AppProps } from '../../app.props';
import { BehaviorSubject } from 'rxjs';

const AngularMicroFrontendClass = AngularMFE.Class.AngularMicrofrontendFactory({
  BehaviorSubject,
  AppProps,
});

@Component({
  templateUrl: './quotazione-infortuni.component.html',
  selector: 'quotazione-infortuni',
})
export default class QuotazioneInfortuniComponent extends AngularMicroFrontendClass {
  @ViewChild('Container', { read: ElementRef }) public container: ElementRef;
  @ViewChild('navigationHeader', { read: ElementRef }) public navigationHeader: ElementRef;

  @Input() public dataNascita: string | undefined;
  @Input() public proseguiClick: (data: {
    [key: string]: string;
  }) => void | undefined;

  public fieldStepper = {
    "customAttributes": {
      "currentStep": "0",
      "labelFormatDeselected": "TEXT APP GRL13 WEB GRL14 GRL14 GRL14",
      "labelFormatPreviousStep": "TEXT APP BDL13 WEB BDL14 BDL14 BDL14",
      "style": "StepperPU",
      "labelFormatCurrentStep": "TEXT APP BDM13 WEB BDM14 BDM14 BDM14",
      "labelsStepper": "Preventivo|Carrello|Acquisto"
    }
  }

  get remoteDependencies(): CommonMFE.Types.TRemoteDependency[] {
    return [
      {
        elementRef: this.container.nativeElement,
        props: {
          ...this.appProps,
          id: `${this.appProps.id}-quotazione-infortuni-widget`,
          __mfeName__: 'tpdQuotazioneInfortuniWidget',
          dataNascita: this.dataNascita,
          proseguiClick: this.proseguiClick
        },
        updateProps: (newProps) => {
          return {
            ...newProps,
            id: `${this.appProps.id}-quotazione-infortuni-widget`,
            __mfeName__: 'tpdQuotazioneInfortuniWidget',
            dataNascita: this.dataNascita,
            proseguiClick: this.proseguiClick
          };
        },
      },
      {
        elementRef: this.navigationHeader.nativeElement,
        props: {
          ...this.appProps,
          __mfeName__: 'tpdNavigationHeader',
          title: 'Il tuo preventivo',
          hideBackBtn: false,
          hideCloseBtn: false,
          onBackBtnClicked: () => Helpers.RouterHelper.goTo('/disambiguazione'),
          onCloseBtnClicked: () => Helpers.RouterHelper.goTo('/disambiguazione')
        },
        updateProps: (newProps) => {
          return {
            ...newProps,
            __mfeName__: 'tpdNavigationHeader',
            title: 'Il tuo preventivo',
            hideBackBtn: false,
            hideCloseBtn: false,
            onBackBtnClicked: () => Helpers.RouterHelper.goTo('/disambiguazione'),
            onCloseBtnClicked: () => Helpers.RouterHelper.goTo('/disambiguazione')
          };
        },
      },
    ];
  }

  constructor(@Inject(AppProps) private appProps: AppProps) {
    super(appProps);
  }
}
