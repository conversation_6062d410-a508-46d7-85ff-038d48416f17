export interface IPage {
  pageID?: string;
  name?: string;
  appliesTo?: string;
  caseTypeID?: string;
  groups?: ITopPageGroup[];
  validationMessages?: string; // Può essere restituito dall'API '.../refresh' se le modifiche dell'utente hanno causato un errore non specifico del campo
}

export interface ITopPageGroup {
  layout?: ILayout;
}

export interface ILayout {
  titleFormat?: string; //Styles are Heading 1, Heading 2, Heading 3, Heading 4, Heading 5, and Heading 6.
  containerFormat?: string;
  groupFormat?: string; // SCREENLAYOUT can be header, header_footer, footer, content_only, header_left_right, header_left. SIMPLELAYOUT can be Default, Stacked, Inline, Inline grid double, Inline grid triple.
  layoutFormat?: string; // Layouts in the topmost group of a Page use SCREENLAYOUT. All other layouts can be SIMPLELAYOUT, TABLELAYOUT, or REPEATINGLAYOUT.
  title?: string; // This field is encoded to prevent cross-site scripting
  visible?: boolean;
  view?: IView;
  groups?: IGroup[];
  reference?: string; // Set when the layoutFormat is TABLELAYOUT or REPEATINGLAYOUT.
  sourceType?: string; // Whether this layout is sourced from a property or data page; set when the layoutFormat is TABLELAYOUT or REPEATINGLAYOUT.,
  fieldListID?: string;
  referenceType?: string; // List or Group; set when the layoutFormat is TABLELAYOUT or REPEATINGLAYOUT.,
  header?: IHeader;
  newRow?: INewRow;
  rows?: IRow[];
  readOnly?: boolean; // For pagelist/pagegroup to show as readonly,
  repeatRowOperations?: IRepeatRow;
  repeatLayoutFormat?: string;
  repeatContainerFormat?: string;
  customAttributes?: Record<string, string>;
}

export interface IView {
  reference?: string;
  validationMessages?: string; // This may be returned by the '.../refresh' apis, if the user's edits caused a non-field-specific error,
  viewID?: string;
  name?: string;
  appliesTo?: string;
  visible?: boolean;
  groups?: ITopViewGroup[];
}

export interface ITopViewGroup {
  layout?: ILayout;
  view?: IView;
}

export interface IGroup extends ITopViewGroup {
  field?: IField;
  paragraph?: IParagraph;
  caption?: ICaption;
  newRow?: INewRow;
  layout?: ILayout;
}

export interface IIndentifiedElement {
  elementUniqueId?: string;
}

export interface IField extends IIndentifiedElement{
  reference?: string; // the path in the case hierarchy to this field,
  validationMessages?: string; // In the .../refresh APIs, field level errors are returned,
  validateAs?: string; // Name of the Edit-Validate rule configured on property rule of this field,
  fieldID?: string;
  name?: string; // this comes from the property rule of the field,
  readOnly?: boolean; // whether to show this read-only or not,
  visible?: boolean; // whether field should be shown currently or not,
  labelReserveSpace?: boolean; // whether to reserve space for a missing label,
  label?: string; // This is the label on the field element in the view. It is encoded to prevent cross-site scripting.,
  showLabel?: boolean; // only available for pxButton, pxIcon, and pxLink,
  required?: boolean;
  labelFormat?: string; //
  disabled?: boolean;
  value?: string; // This field is encoded to prevent cross-site scripting,
  maxLength?: number; // maximum length for the value of this field,
  expectedLength?: number; // expected length for the value of this field, useful for identifying how wide the text box should be,
  type?: string; // Text, Decimal, etc.,
  control?: IControl;
  testID?: string; // unique ID for testing
  controlName?: string;
  customAttributes?: any;
}

export interface IControl {
  type?: string; // the type of control will determine the fields that appear in modes,
  modes?: IMode[];
  actionSets?: IActionSet[];
  label?: string; // label for the button or link. This field is encoded to prevent cross-site scripting.
  format?: string;
}

export interface IMode {
  modeType?: string;
  controlFormat?: string;
  textAlign?: string;
  tooltip?: string; // This is encoded to prevent cross-site scripting,
  maxChars?: string;
  minChars?: string;
  formatType?: string;
  specifySize?: string;
  showReadOnlyValidation?: boolean;
  listSource?: string; // used with controls pxAutoComplete/pxDropDown/pxRadioButtons; list can be sourced from promptlist, locallist, or datapage,
  options?: IOption[]; // used with controls pxAutoComplete/pxDropDown/pxRadioButtons; when listSource is set to promptlist or locallist, contains the dropdown contents,
  dataPageID?: string; // used with controls pxAutoComplete/pxDropDown/pxRadioButtons; when listSource is set to datapage,
  dataPageValue?: string; // used with controls pxAutoComplete/pxDropDown/pxRadioButtons; when listSource is set to datapage, field in the data page response to use for the value,
  dataPagePrompt?: string; // used with controls pxAutoComplete/pxDropDown/pxRadioButtons; when listSource is set to datapage, field in the data page response to use for the prompt,
  dataPageTooltip?: string;
  enableGrouping?: boolean;
  groupBy?: string;
  groupOrder?: string;
  maxResults?: string;
  minResult?: string;
  minSearch?: string;
  clipboardPageID?: string;
  clipboardPageValue?: string;
  clipboardPagePrompt?: string;
  clipboardPageTooltip?: string;
  obfuscated?: boolean;
  dateFormat?: string;
  dateTimeFormat?: string;
  customDateFormat?: string;
  showAs24HourFormat?: boolean;
  linkType?: string; // used with control pxLink,
  linkData?: string; // used with control pxLink,
  linkImageSource?: string; // used with control pxLink,
  linkImagePosition?: string; // used with control pxLink,
  linkImage?: string;
  linkProperty?: string;
  linkStyle?: string; // used with control pxLink,
  iconSource?: string; // used with control pxIcon to determine its image source,
  iconStandard?: string; // used with control pxIcon, when iconSource is standardicon,
  iconImage?: string; // used with control pxIcon, when iconSource is image,
  iconUrl?: string; // used with control pxIcon, when iconSource is exturl,
  iconProperty?: string; // used with control pxIcon, when iconSource is property,
  iconStyle?: string; // used with control pxIcon, when iconSource is styleclass,
  captionPosition?: string;
  placeholder?: string; // placeholder text, typically used with a dropdown where a choice hasn't been made yet,
  orientation?: string; // used with control pxRadioButtons, either 'vertical' or 'horizontal',
  wrapAfter?: number; // used with control pxRadioButtons, the number of radio buttons after which it wraps, so if the value is 3 and there are 5 buttons the first row would have 3, the second 2,
  lightWeightAutoComplete?: boolean;
  displayAsComboBox?: boolean;
  displayFullScreen?: boolean;
  allowFreeFormInput?: boolean;
  dateTime?: string;
  displayMode?: string;
  displayLongFormat?: boolean;
  ignoreLocaleSettings?: boolean;
  showReadOnlyFormatting?: boolean;
  calendarNavigation?: string;
  allowTextEntry?: boolean; //if true allow text entry of date, if false use the calendar icon
}

export interface IOption {
  value?: string; // Will be the same as the key for a locallist, but different for a promptlist,
  key?: string;
  tooltip?: string;
}

export interface IActionSet {
  actions?: IClientAction[];
  events?: IEvent[];
}

export interface IClientAction {
  action?: string;
  refreshFor?: string; // either name of control or unique ID,
  actionProcess?: IActionProcess;
}

export interface IActionProcess {
  setValuePairs?: IValuePairs[];
  functionParameters?: IFunctionParams[];
  actionName?: string;
  urlBase?: string;
  windowName?: string;
  windowOptions?: string;
  alternateDomain?: IAltDomain;
  queryParams?: IQueryParams[];
  localAction?: string;
  target?: string;
}

export interface IValuePairs {
  value?: string;
  name?: string;
  valueReference?: IValueReference;
}

export interface IValueReference {
  reference?: string;
  lastSavedValue?: string; // The last saved value is encoded to protect against cross-site scripting
}

export interface IFunctionParams {
  value?: string;
  name?: string;
  valueReference?: IValueReference;
}

export interface IAltDomain {
  url?: string;
  urlReference?: IURLReference;
}

export interface IURLReference {
  reference?: string;
  lastSavedValue?: string; // The last saved value is encoded to protect against cross-site scripting
}

export interface IQueryParams {
  value?: string;
  name?: string;
  valueReference?: IValueReference;
}

export interface IEvent {
  event?: string;
}

export interface IParagraph extends IIndentifiedElement{
  visible?: boolean;
  appliesTo?: string;
  paragraphID?: string;
  readOnly?: boolean;
  value?: string; //This is typically html content. It is encoded to prevent against cross-site scripting.
}

export interface ICaption  extends IIndentifiedElement {
  columnImportance?: string; // Used for the column header layout, a primary column remains when the browser width is shrunk, a secondary column moves into the primary, and other means the column can be lost,
  captionFor?: string; // while fields usually have a label as part of them, a caption element may be bound to a field but not part of it,
  control?: IControl;
  value?: string; // This field is encoded to prevent cross-site scripting,
  format?: string;
  testID?: string;
  visible?: boolean;
}

export interface INewRow {
  groups?: IGroup[];
  testID?: string; // A unique number that can be used for test referencing, for row it will be appended with -R1, -R2, etc and for RDL it will be appended with -1, -2, etc
}

export interface IHeader {
  groups?: IGroup[];
}

export interface IRow {
  groups?: IGroup[];
  testID?: string; // A unique number that can be used for test referencing, for row it will be appended with -R1, -R2, etc and for RDL it will be appended with -1, -2, etc,
  groupIndex?: string; // For a grid populated by a group, where each row has a text-based key
}

export interface IRepeatRow {
  rowEditing?: string; // How to edit rows in a repeating layoutNOTE: if readOnly, then all fields will besent as readOnly,
  editingInlineType?: string; // Available when rowEditing is 'row'; row means click to edit, readWrite means inline editable
}

export interface IStage {
  ID?: string;
  name?: string;
  pxObjClass?: string;
}

export interface ISLA {
  stageSLAGoal?: Date | string;
  stageSLADeadline?: Date | string;
  overallSLAGoal?: Date | string;
  overallSLADeadline?: Date | string;
  pxObjClass?: Date | string;
}

export interface IStartingProcess {
  ID?: string;
  name?: string;
  requiresFieldsToCreate?: boolean;
  pxObjClass?: string;
}

export interface IAction {
  ID?: string;
  name?: string;
  pxObjClass?: string;
}

//export interface IContent { }

export interface IPageInstruction {
  instruction?: string; // DELETE, REPLACE, UPDATE are common amongst embedded pages, pagelists & pagegroups. Pagelists also support APPEND, INSERT, & MOVE. Pagegroups have ADD,
  target?: string; // Reference to the embedded page, pagelist, or pagegroup. Doesn't need to start with a leading dot,
  listIndex?: number; // Use this to specify a pagelist index for the DELETE, REPLACE, UPDATE, INSERT, and MOVE instructions,
  groupIndex?: string; // Use this to specify a pagegroup subscript for the ADD, DELETE, REPLACE, and UPDATE instructions,
  listMoveToIndex?: number; // Use this to specify a pagelist index for the MOVE instruction to indicate the new position,
  content?: any; // Content is expected for all instructions except DELETE and MOVE. REPLACE, ADD, APPEND, and INSERT merge in the pyDefault data transform to the new page
}

export interface IAttachments {
  type?: string; // Determines the type of attachment,
  attachmentFieldName?: string; // Reference to the attachment field property. Should not start with a leading dot,
  category?: string; // The category of attachment,
  name?: string; // Name of the attachment. No need to add this field in request, if name matches uploaded file name,
  ID?: string; // ID received upon uploading a file using POST /attachments/upload,
  delete?: boolean; // This should be Include only while submitting form, an already attachment field needs to be cleared. If delete = true, type has to be File along with a valid attachment field name. Not other field should be included in request payload for this attachment.
}
export interface IProcessManagement {
  'ProcessManagement.NextStep'?: string;
  'ProcessManagement.IndiceSezioneSelezionata'?: string;
  'ProcessManagement.IndiceGaranziaSelezionata'?: string;
  'ProcessManagement.IdGaranzia'?: string;
  'ProcessManagement.ValoreGaranzia'?: string;
}

export interface IGestioneProcesso {
  'GestioneProcesso.StepSuccessivo'?: string;
  'GestioneProcesso.MostraBoxResidenza'?: string;
  'GestioneProcesso.MostraBoxDomicilio'?: string;
  'GestioneProcesso.IndiceSezioneSelezionata'?: string;
  'GestioneProcesso.IndiceGaranziaSelezionata'?: string;
  'GestioneProcesso.IndiceAttributoGaranziaSelezionata'?: string;
  'GestioneProcesso.IndiceOffertaSelezionata'?: string;
  'GestioneProcesso.IdGaranzia'?: string;
  'GestioneProcesso.ValoreGaranzia'?: string;
  'GestioneProcesso.FlagSalvaPerDopo'?: string;
  'GestioneProcesso.AggiungiConvenzione'?: boolean;
  'GestioneProcesso.VaiAModificaDati'?: string;
  'GestioneProcesso.IndiceScontoSelezionato'?: string;
  'GestioneProcesso.IdPosizioneDaCancellare'?: string;
  'GestioneProcesso.RimozioneConvenzione'?: string;
}

export interface IStoredGestioneProcessoChecker {
  'GestioneProcesso.IdPosizioneDaCancellare': true;
  'GestioneProcesso.CodProdottoDaCancellare': true;
  'GestioneProcesso.DescBeneWPT': true;
  'GestioneProcesso.TempSezValInt': true;
  'GestioneProcesso.TempGarValInt': true;
}

export interface IStoredGestioneProcesso {
  'GestioneProcesso.IdPosizioneDaCancellare'?: string;
}
