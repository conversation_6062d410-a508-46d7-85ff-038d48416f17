// documents
export interface IDocumentsResponse {
  pxObjClass: string;
  createdByName: string;
  lastUpdatedBy: string;
  createdBy: string;
  createTime: string;
  ID: string;
  title: string;
  lastUpdatedByName: string;
  lastUpdateTime: string;
}

export interface IDocumentDetailsResponse {
  pxObjClass: string;
  createdByName: string;
  lastUpdatedBy: string;
  createdBy: string;
  createTime: string;
  documentAccessibleTo: string;
  documentContent: string;
  ID: string;
  title: string;
  lastUpdatedByName: string;
  lastUpdateTime: string;
}
// messages
export interface IListOfMessages {
  entryCount: number;
  pageFirstEntry: string;
  pageLastEntry: string;
  messages: IMessagesResponse[];
}

export interface IMessagesResponse {
  context: string;
  contextDescription: string;
  contextID: string;
  contextType: string;
  currentContext: string;
  ID: string;
  message: string;
  postedBy: string;
  postedTime: string;
  updatedTime: string;
  replies: IMessageReplies[];
}

export interface IMessageReplies {
  context: string;
  currentContext: string;
  ID: string;
  message: string;
  postedBy: string;
  postedTime: string;
}

export interface ICreateMessageResponse {
  ID: string;
}

// spaces
export interface ISpacesResponse {
  spaces: ISpacesGroups[];
}

export interface ISpacesGroups {
  owner: ISpacesOwner[];
  accessType: string;
  memberCount: string;
  name: string;
  description: string;
  ID: string;
}

export interface ISpacesOwner {
  name: string;
  ID: string;
}

export interface ISpaceSpaceHierarchy {
  entryIndex: string;
  entryName: string;
  entryID: string;
}

export interface ISpacesMembers {
  name: string;
  ID: string;
  position: string;
  type: string;
  key: string;
  status: string;
}

export interface ISpaceDetailsResponse {
  owner: ISpacesOwner;
  createdByName: string;
  lastUpdatedBy: string;
  accessType: string;
  memberCount: string;
  description: string;
  spacehierarchy: ISpaceSpaceHierarchy[];
  createdBy: string;
  createTime: string;
  members: ISpacesMembers[];
  name: string;
  ID: string;
  lastUpdatedByName: string;
  lastUpdateTime: string;
}

export interface ILeaveOrJoinSpaceResponse {
  success: string;
}

export interface ISpacePinsResponse {
  entryCount: string;
  pins: ISpacesPins[];
}

export interface ISpacesPins {
  pinType: string;
  pinnedItem: string;
  pinTypeID: string;
  createdBy: string;
  createTime: string;
  name: string;
  ID: string;
}
