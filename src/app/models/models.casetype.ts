import { IPage } from './models.commons';

export interface ICaseTypesResponse {
  pxObjClass?: string;
  caseTypes?: ICaseType[];
}

export interface ICaseType {
  ID?: string;
  name?: string;
  CanCreate?: boolean;
  pxObjClass?: string;
  startingProcesses?: IStartingProcess[];
}

export interface IStartingProcess {
  ID?: string;
  name?: string;
  requiresFieldsToCreate?: boolean;
  pxObjClass?: string;
}

//Dettagli Caso - GET /casetypes/{ID}
export interface ICaseTypeDetailResponse {
  caseTypeID?: string;
  creation_page?: IPage;
}
