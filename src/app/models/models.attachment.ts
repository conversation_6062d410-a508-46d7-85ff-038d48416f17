export interface IAttachmentsUpdate {
  ID?: string;
}
export interface IAttachmentErrorResponse {
  errorClassification: errorClassification;
  localizedValue: string;
  errorDetails: IAttachmentError[];
}
export interface IAttachmentError {
  message: string;
  localizedValue: string;
  errorCode: string;
  errorCodeLabel: string;
  errorClassification: string;
  erroneousInputOutputFieldInPage: string;
  erroneousInputOutputIdentifier: string;
  messageParameters: string[];
  messageDetails: string;
}

export enum errorClassification {
  'Invalid input',
  'Insufficient security',
  'Resource not found',
  'Internal server error',
}
export interface IAttachmentListItem {
  extension: string;
  fileName: string;
  createdBy: string;
  createTime: string;
  links: IAttachmentLinks;
  ID: string;
  category: string;
  type: string;
}
export interface IAttachmentListResponse {
  attachments: IAttachmentListItem[];
}
export interface IAttachmentCategory {
  name: string;
  ID: string;
  canView: boolean;
  canDeleteOwn: boolean;
  canEdit: boolean;
  canCreate: boolean;
  canDeleteAll: boolean;
}
export interface IAttachmentCategories {
  attachment_categories: IAttachmentCategory[];
}
export interface IAttachmentLinks {
  'download/delete': IAttachmentLink;
}
export interface IAttachmentLink {
  rel: string;
  href: string;
  title: string;
  type: 'GET' | 'DELETE';
}
