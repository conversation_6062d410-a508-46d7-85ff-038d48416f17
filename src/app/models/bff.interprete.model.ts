import { IActionResponse } from './models.assignment';

export type GenericDataType = {
  [key: string]: any
}

export interface ILoadPageRequest {
  action: IActionLoadRequest;
  assignmentId?: string; // Required only if action == retrieve || reload
  caseId?: string; // Required only if action == duplicate
  productType?: string;
  referencesToUpdate?: any;
  env?: string;
}

export enum IActionLoadRequest {
  create = 'CREATE',
  retrieve = 'retrieve',
  duplicate = 'duplicate',
  reload = 'reload',
}

export interface ICommandResponse {
  metaBodyResponse: IMetaBodyResponse;
  pegaBodyResponse: IActionResponse;
  analyticsBodyResponse?: any;
}

export interface IMetaBodyResponse {
  actionId: string;
  assignmentId: string;
}
export interface IGoNextPageRequest {
  actionId?: string; // If null retrieves first action from assignment
  assignmentId: string;
  captchaToken?: string;
  referencesToUpdate?: any;
}

export interface IUpdatePageRequest {
  actionId?: string; //If null retrieves first action from assignment
  assignmentId: string;
  referencesToUpdate?: any;
  refreshFor?: string;
}
