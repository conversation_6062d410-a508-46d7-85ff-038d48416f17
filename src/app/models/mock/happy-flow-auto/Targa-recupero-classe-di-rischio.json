{"view": {"reference": "", "validationMessages": "", "viewID": "RecuperoClasseRischioPG", "visible": true, "fua": true, "titleFormat": "", "name": "RecuperoClasseRischioPG", "appliesTo": "UG-Ins-PU-Work-Quotazione", "containerFormat": "", "groups": [{"view": {"reference": "", "validationMessages": "", "viewID": "Intestazione", "visible": true, "titleFormat": "", "name": "Intestazione", "appliesTo": "UG-Ins-PU-Work-Quotazione", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"modeType": "ignore", "tooltip": ""}, {"modeType": "readOnly", "autoPrepend": "", "tooltip": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "isMasked": false, "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202212131640340513751", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "Header", "background": "Secondary"}}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "isEncrypted": false, "testID": "202301131218420210633", "control": {"format": "Standard", "testID": "202301131218420210633"}, "isMasked": false, "value": "Classe di rischio"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"modeType": "ignore", "iconImage": "webwb/pymenuleftarrow.png", "tooltip": "", "iconSource": "image"}, {"modeType": "readOnly", "iconImage": "webwb/pymenuleftarrow.png", "autoPrepend": "", "controlFormat": "Standard", "tooltip": "", "showReadOnlyValidation": "false", "autoAppend": "", "formatType": "text", "iconSource": "image"}], "actionSets": [{"actions": [{"actionProcess": {"actionName": "Indietro"}, "action": "takeAction"}], "events": [{"event": "click"}]}], "type": "pxIcon"}, "label": "Image", "type": "Text", "isMasked": false, "required": false, "showLabel": false, "validateAs": "", "reference": "pyTemplateGeneric", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202212141106470503509", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"componentID": "_back", "size": "S", "resource": "arrowLeft", "type": "icon"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "readOnly": false, "title": ""}}, {"view": {"reference": "", "validationMessages": "", "viewID": "BarraDiAvanzamento", "visible": false, "titleFormat": "", "name": "BarraDiAvanzamento", "appliesTo": "UG-Ins-PU-Work-Quotazione", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"modeType": "ignore", "tooltip": ""}, {"modeType": "readOnly", "autoPrepend": "", "tooltip": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "isMasked": false, "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202212131640340513751", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "Stepper", "progress": "17", "style": "StepperPU"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "readOnly": false, "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "isEncrypted": false, "testID": "202301241613290618250", "control": {"format": "Text responsive", "testID": "202301241613290618250"}, "isMasked": false, "value": "Indica la targa per recuperare la classe di rischio da un altro attestato di rischio. Ricorda che il proprietario deve essere il medesimo."}}], "groupFormat": "Row LC CC", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"view": {"reference": "Ambito", "validationMessages": "", "viewID": "RecuperoClasseRischioPG", "visible": true, "titleFormat": "", "name": "RecuperoClasseRischioPG", "appliesTo": "UG-Ins-PU-Data-Ambito", "containerFormat": "NOHEADER", "groups": [{"view": {"reference": "Ambito.Bene", "validationMessages": "", "viewID": "RecuperoClasseRischioPG", "visible": true, "titleFormat": "", "name": "RecuperoClasseRischioPG", "appliesTo": "UG-Ins-PU-Data-Bene", "containerFormat": "NOHEADER", "groups": [{"view": {"reference": "Ambito.Bene.Auto", "validationMessages": "", "viewID": "RecuperoClasseRischioPG", "visible": true, "titleFormat": "", "name": "RecuperoClasseRischioPG", "appliesTo": "UG-Ins-PU-Data-Auto", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "isEncrypted": false, "testID": "202301241611550617302", "control": {"format": "Text medium", "testID": "202301241611550617302"}, "isMasked": false, "value": "Targa"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"modeType": "ignore", "iconImage": "dsmimages/pzInformation.png", "tooltip": "", "iconSource": "image"}, {"modeType": "readOnly", "iconImage": "dsmimages/pzInformation.png", "autoPrepend": "", "controlFormat": "Standard", "tooltip": "", "showReadOnlyValidation": "false", "autoAppend": "", "formatType": "text", "iconSource": "image"}], "actionSets": [], "type": "pxIcon"}, "label": "Image", "type": "Text", "isMasked": false, "required": false, "showLabel": false, "validateAs": "", "reference": "Ambito.Bene.Auto.pyTemplateGeneric", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202301241611550618309", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"tooltipDirection": "TooltipLU", "TooltipID": "tooltip1", "resource": "info", "type": "icon"}}}], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "accessCondition": "", "readOnly": false, "control": {"modes": [{"modeType": "editable", "controlFormat": "Standard", "textAlign": "Left", "tooltip": "", "maxChars": "", "formatType": "none", "specifySize": "auto", "minChars": ""}, {"modeType": "readOnly", "tooltip": "", "showReadOnlyValidation": "false", "formatType": "none"}], "actionSets": [], "type": "pxTextInput"}, "label": "", "type": "Text", "isMasked": false, "required": false, "validateAs": "", "reference": "Ambito.Bene.Auto.TargaAltroVeicolo", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202301241611550618319", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "TargaAltroVeicolo", "customAttributes": {"validation": "licensePlate"}}}], "groupFormat": "Col CC 8 4", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "readOnly": false, "title": ""}}], "readOnly": false, "title": ""}}], "readOnly": false, "title": ""}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"imageSource": "none", "modeType": "ignore", "tooltip": ""}, {"modeType": "readOnly", "imageSource": "none", "autoPrepend": "", "controlFormat": "Full Positive Button", "tooltip": "", "showReadOnlyValidation": "false", "autoAppend": "", "formatType": "text"}], "actionSets": [{"actions": [{"action": "finishAssignment"}], "events": [{"event": "click"}]}], "label": "PROSEGUI", "type": "px<PERSON><PERSON><PERSON>"}, "label": "<PERSON><PERSON>", "type": "Text", "isMasked": false, "required": false, "showLabel": false, "validateAs": "", "reference": "pyTemplateButton", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202301241736580367520", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton"}}], "groupFormat": "DefaultTPD", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"view": {"reference": "", "validationMessages": "", "viewID": "BoxRichiestaDiContatto", "visible": true, "titleFormat": "", "name": "BoxRichiestaDiContatto", "appliesTo": "UG-Ins-PU-Work-Quotazione", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"paragraph": {"visible": true, "appliesTo": "UG-Ins-PU-Work-Quotazione", "paragraphID": "BoxRichiestaDiContattoWEB", "testID": "202212201135080193565", "readOnly": true, "value": "<p data-pega-style=\"Text responsive S Center\">Parla con noi: un nostro consulente ti aiuter&agrave; a trovare la soluzione migliore. C&#39;&egrave; sempre un&#39;<strong>Agente UnipolSai</strong> vicino a te.</p>"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"modeType": "ignore", "tooltip": ""}, {"modeType": "readOnly", "autoPrepend": "", "tooltip": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "isMasked": false, "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202212291442330058645", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "verticalSpace", "spaceSize": "S"}}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"imageSource": "none", "modeType": "ignore", "tooltip": ""}, {"modeType": "readOnly", "imageSource": "none", "autoPrepend": "", "controlFormat": "Highlight", "tooltip": "", "showReadOnlyValidation": "false", "autoAppend": "", "formatType": "text"}], "actionSets": [], "label": "Ti contattiamo noi", "type": "px<PERSON><PERSON><PERSON>"}, "label": "<PERSON><PERSON>", "type": "Text", "isMasked": false, "required": false, "showLabel": false, "validateAs": "", "reference": "pyTemplateButton", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202212201135080194164", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton"}}], "groupFormat": "InlineTPD middle", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "StackedTPD", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "BoxHighlight", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"modeType": "ignore", "iconStandard": "pxIcon", "tooltip": "", "iconSource": "standardIcon"}, {"modeType": "readOnly", "iconStandard": "pxIcon", "autoPrepend": "", "controlFormat": "Standard", "tooltip": "", "showReadOnlyValidation": "false", "autoAppend": "", "formatType": "text", "iconSource": "standardIcon"}], "actionSets": [], "type": "pxIcon"}, "label": "Image", "type": "Text", "isMasked": false, "required": false, "showLabel": false, "validateAs": "", "reference": "pyTemplateGeneric", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202212201148470288169", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"size": "M", "resource": "assistenza_clienti", "type": "icon"}}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "isEncrypted": false, "testID": "202212201148470289699", "control": {"format": "Heading 3", "testID": "202212201148470289699"}, "isMasked": false, "value": "TI DIAMO SUPPORTO"}}], "groupFormat": "Row LC", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"paragraph": {"visible": true, "appliesTo": "UG-Ins-PU-Work-Quotazione", "paragraphID": "BoxRichiestaDiContattoAPP", "testID": "202212201135080193565", "readOnly": true, "value": "<p data-pega-style=\"Text\">Parla con noi: un nostro consulente ti aiuterà a trovare la soluzione migliore. C'è sempre un'Agenzia UnipolSai vicino a te.</p>"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"imageSource": "none", "modeType": "ignore", "tooltip": ""}, {"modeType": "readOnly", "imageSource": "none", "autoPrepend": "", "controlFormat": "Cancel button", "tooltip": "", "showReadOnlyValidation": "false", "autoAppend": "", "formatType": "text"}], "actionSets": [], "label": "TI CONTATTIAMO NOI", "type": "px<PERSON><PERSON><PERSON>"}, "label": "<PERSON><PERSON>", "type": "Text", "isMasked": false, "required": false, "showLabel": false, "validateAs": "", "reference": "pyTemplateButton", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202212201148470291629", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton"}}], "groupFormat": "Col TL 16 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 20 32", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "<PERSON> 24 0 1 Grey1 20 32", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "readOnly": false, "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"imageSource": "none", "modeType": "ignore", "tooltip": ""}, {"modeType": "readOnly", "imageSource": "none", "autoPrepend": "", "controlFormat": "Full Positive Button", "tooltip": "", "showReadOnlyValidation": "false", "autoAppend": "", "formatType": "text"}], "actionSets": [{"actions": [{"action": "finishAssignment"}], "events": [{"event": "click"}]}], "label": "PROSEGUI", "type": "px<PERSON><PERSON><PERSON>"}, "label": "<PERSON><PERSON>", "type": "Text", "isMasked": false, "required": false, "showLabel": false, "validateAs": "", "reference": "pyTemplateButton", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202301241736580367520", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton"}}], "groupFormat": "DefaultTPD", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TL 32 16 24 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 20 24 D 0 T 0 M 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}, "caseID": "UG-INS-PU-WORK-QUOTAZIONE Q-12019", "name": "RecuperoClasseRischioPG", "actionID": "RecuperoClasseRischioPG"}