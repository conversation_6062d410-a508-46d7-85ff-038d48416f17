{"view": {"reference": "", "validationMessages": "", "viewID": "RiepilogoDati", "visible": true, "fua": true, "titleFormat": "", "name": "RiepilogoDati", "appliesTo": "UG-Ins-PU-Work-Quotazione", "containerFormat": "", "groups": [{"view": {"reference": "", "validationMessages": "", "viewID": "AnalyticsHidden", "visible": true, "titleFormat": "", "name": "AnalyticsHidden", "appliesTo": "UG-Ins-PU-Work-Quotazione", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"modeType": "ignore", "tooltip": ""}, {"modeType": "readOnly", "autoPrepend": "", "tooltip": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "isMasked": false, "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202212131640340513751", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"page_id": "unico_preventivo_3", "customType": "analyticsPageFields", "funnel_name": "ConfigurazioneOfferta", "page_name": "unico:riepilogo", "funnel_step": ""}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "readOnly": false, "title": ""}}, {"view": {"reference": "", "validationMessages": "", "viewID": "Intestazione", "visible": true, "titleFormat": "", "name": "Intestazione", "appliesTo": "UG-Ins-PU-Work-Quotazione", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"modeType": "ignore", "tooltip": ""}, {"modeType": "readOnly", "autoPrepend": "", "tooltip": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "isMasked": false, "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202212131640340513751", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "Header", "background": "Secondary"}}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "isEncrypted": false, "testID": "202301131218420210633", "control": {"format": "Standard", "testID": "202301131218420210633"}, "isMasked": false, "value": "Riepilogo"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"modeType": "ignore", "iconImage": "webwb/pymenuleftarrow.png", "tooltip": "", "iconSource": "image"}, {"modeType": "readOnly", "iconImage": "webwb/pymenuleftarrow.png", "autoPrepend": "", "controlFormat": "Standard", "tooltip": "", "showReadOnlyValidation": "false", "autoAppend": "", "formatType": "text", "iconSource": "image"}], "actionSets": [{"actions": [{"actionProcess": {"actionName": "Indietro"}, "action": "takeAction"}], "events": [{"event": "click"}]}], "type": "pxIcon"}, "label": "Image", "type": "Text", "isMasked": false, "required": false, "showLabel": false, "validateAs": "", "reference": "pyTemplateGeneric", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202212141106470503509", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"componentID": "_back", "size": "S", "resource": "arrowLeft", "type": "icon"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "readOnly": false, "title": ""}}, {"view": {"reference": "", "validationMessages": "", "viewID": "BarraDiAvanzamento", "visible": true, "titleFormat": "", "name": "BarraDiAvanzamento", "appliesTo": "UG-Ins-PU-Work-Quotazione", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"modeType": "ignore", "tooltip": ""}, {"modeType": "readOnly", "autoPrepend": "", "tooltip": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "isMasked": false, "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202212131640340513751", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "Stepper", "progress": "60", "style": "StepperPU"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "readOnly": false, "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "isEncrypted": false, "testID": "202212151724460768590", "control": {"format": "TEXT APP BDL16 WEB BDL18C BDL18C BDL16C", "testID": "202212151724460768590"}, "isMasked": false, "value": "Ecco la tua offerta. Fai un controllo e aggiungi al carrello"}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "isEncrypted": false, "testID": "202303081434360318993", "control": {"format": "TEXT APP GDB16 WEB BDB20 BDB20 BDB16", "testID": "202303081434360318993"}, "isMasked": false, "value": "Dati di polizza"}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "isEncrypted": false, "testID": "20230308143436032164", "control": {"format": "Text APP GDL16 WEB BDL16 BDL16 BDL16", "testID": "20230308143436032164"}, "isMasked": false, "value": "Ecco l'inizio e la fine della polizza che stai acquistando"}}], "groupFormat": "Col TL 8 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"view": {"reference": "Ambito", "validationMessages": "", "viewID": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible": true, "titleFormat": "", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "appliesTo": "UG-Ins-PU-Data-Ambito", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "accessCondition": "", "readOnly": false, "control": {"modes": [{"dateTime": "auto", "showReadOnlyFormatting": false, "allowTextEntry": true, "useFutureDateRange": true, "textAlign": "Left", "tooltip": "", "usePastDateRange": true, "ignoreLocaleSettings": false, "futureDateRange": "10", "displayLongFormat": false, "displayMode": "calendar", "calendarNavigation": "true", "minChars": "", "modeType": "editable", "controlFormat": "", "maxChars": "", "formatType": "text", "specifySize": "auto", "obfuscated": false, "pastDateRange": "10"}, {"modeType": "readOnly", "dateTimeFormat": "DateTime-Short", "tooltip": "", "showReadOnlyValidation": "false", "formatType": "datetime", "obfuscated": false, "showAs24HourFormat": false}], "actionSets": [{"actions": [{"action": "refresh", "refreshFor": "RHVyYXRhUG9saXp6YVVHLUlucy1QVS1EYXRhLUFtYml0by5EYXRhRGVjb3JyZW56YTIwMjMwMzA4MTQ1MjAxMDMxMTM3MQ%3D%3D"}], "events": [{"event": "change"}]}], "type": "pxDateTime"}, "label": "Data inizio", "type": "Date", "isMasked": false, "required": false, "validateAs": "", "reference": "Ambito.DataDecorrenza", "labelFormat": "TEXT APP GDB16 WEB BAB16 BAB16 BAB16", "isEncrypted": false, "disabled": false, "testID": "202303081452010311371", "valueFormatting": "", "value": "20230226", "maxLength": 0, "expectedLength": "", "fieldID": "DataDecorrenza", "customAttributes": {"dateFormat": "gg/mm/aaaa"}}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "accessCondition": "", "readOnly": false, "control": {"modes": [{"dateTime": "auto", "showReadOnlyFormatting": false, "allowTextEntry": true, "useFutureDateRange": true, "textAlign": "Left", "tooltip": "", "usePastDateRange": true, "ignoreLocaleSettings": false, "futureDateRange": "10", "displayLongFormat": false, "displayMode": "calendar", "calendarNavigation": "true", "minChars": "", "modeType": "editable", "controlFormat": "", "maxChars": "", "formatType": "text", "specifySize": "auto", "obfuscated": false, "pastDateRange": "10"}, {"modeType": "readOnly", "dateTimeFormat": "DateTime-Short", "tooltip": "", "showReadOnlyValidation": "false", "formatType": "datetime", "obfuscated": false, "showAs24HourFormat": false}], "actionSets": [], "type": "pxDateTime"}, "label": "Orario inizio", "type": "TimeOfDay", "isMasked": false, "required": false, "validateAs": "", "reference": "Ambito.OraDecorrenza", "labelFormat": "TEXT APP GDB16 WEB BAB16 BAB16 BAB16", "isEncrypted": false, "disabled": false, "testID": "202303081452010311651", "valueFormatting": "", "value": "230000", "maxLength": 0, "expectedLength": "", "fieldID": "OraDecorrenza", "customAttributes": {"dateFormat": "hh/mm"}}}], "groupFormat": "Row Responsive2colL", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "accessCondition": "", "readOnly": false, "control": {"modes": [{"modeType": "editable", "controlFormat": "Standard", "listSource": "locallist", "textAlign": "Left", "options": [{"value": "1 anno", "key": "1 anno"}, {"value": "2 anni", "key": "2 anni"}, {"value": "3 anni", "key": "3 anni"}, {"value": "4 anni", "key": "4 anni"}, {"value": "5 anni", "key": "5 anni"}], "tooltip": "", "maxChars": "", "formatType": "none", "specifySize": "auto", "minChars": ""}, {"modeType": "readOnly", "tooltip": "", "showReadOnlyValidation": "false", "formatType": "none"}], "actionSets": [], "type": "pxDropdown"}, "label": "<PERSON>rata polizza", "type": "Text", "isMasked": false, "required": false, "validateAs": "", "reference": "Ambito.Durata", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202302211128270137321", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "<PERSON><PERSON>"}}], "groupFormat": "StackedTPD", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "readOnly": false, "title": ""}}], "groupFormat": "Col TL 20 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 0 D 20 24 20 28 T 20 24 20 28 M 16 24 16 28", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Card WH 24 0 1 GN 20 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"paragraph": {"visible": false, "appliesTo": "UG-Ins-PU-Work-Quotazione", "paragraphID": "DataDecorrenzaTra60ggE1Anno", "testID": "202303091525280669483", "readOnly": true}}, {"paragraph": {"visible": false, "appliesTo": "UG-Ins-PU-Work-Quotazione", "paragraphID": "DataDecorrenzaMaggioreDi1Anno", "testID": "202303091525280670340", "readOnly": true}}], "groupFormat": "TooltipCard", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "isEncrypted": false, "testID": "202302201431270990331", "control": {"format": "Text APP GDB16 WEB BDB20 BDB20 BDB16", "testID": "202302201431270990331"}, "isMasked": false, "value": "Ottieni uno sconto aggiuntivo"}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "isEncrypted": false, "testID": "202302201431270994174", "control": {"format": "Text APP BDB16 WEB BDB16 BDB16 BDB16", "testID": "202302201431270994174"}, "isMasked": false, "value": ""}}], "groupFormat": "Card Ribbon", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Row SB", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 20 24 0 0 D 20 24 0 0 T 20 24 0 0 M 16 24 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "isEncrypted": false, "testID": "20230221112847056690", "control": {"format": "Text APP GDL16 WEB BDL16 BDL16 BDL16", "testID": "20230221112847056690"}, "isMasked": false, "value": "Se decidi di acquistare una polizza pluriennale avrai un ulteriore sconto del 5%"}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "accessCondition": "", "readOnly": false, "control": {"modes": [{"dateTime": "auto", "showReadOnlyFormatting": false, "allowTextEntry": true, "useFutureDateRange": true, "textAlign": "Left", "tooltip": "", "usePastDateRange": true, "ignoreLocaleSettings": false, "futureDateRange": "10", "displayLongFormat": false, "displayMode": "calendar", "calendarNavigation": "true", "minChars": "", "modeType": "editable", "controlFormat": "", "maxChars": "", "formatType": "text", "specifySize": "auto", "obfuscated": false, "pastDateRange": "10"}, {"modeType": "readOnly", "dateTimeFormat": "DateTime-Short", "tooltip": "", "showReadOnlyValidation": "false", "formatType": "datetime", "obfuscated": false, "showAs24HourFormat": false}], "actionSets": [], "type": "pxDateTime"}, "label": "DataDecorrenza", "type": "Date", "isMasked": false, "required": false, "validateAs": "", "reference": "Ambito.DataDecorrenza", "labelFormat": "Standard", "isEncrypted": false, "disabled": true, "testID": "202302211128470572253", "valueFormatting": "", "value": "20230226", "maxLength": 0, "expectedLength": "", "fieldID": "DataDecorrenza"}}, {"view": {"reference": "Ambito", "validationMessages": "", "viewID": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible": true, "titleFormat": "", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "appliesTo": "UG-Ins-PU-Data-Ambito", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "accessCondition": "", "readOnly": false, "control": {"modes": [{"dateTime": "auto", "showReadOnlyFormatting": false, "allowTextEntry": true, "useFutureDateRange": true, "textAlign": "Left", "tooltip": "", "usePastDateRange": true, "ignoreLocaleSettings": false, "futureDateRange": "10", "displayLongFormat": false, "displayMode": "calendar", "calendarNavigation": "true", "minChars": "", "modeType": "editable", "controlFormat": "", "maxChars": "", "formatType": "text", "specifySize": "auto", "obfuscated": false, "pastDateRange": "10"}, {"modeType": "readOnly", "dateTimeFormat": "DateTime-Short", "tooltip": "", "showReadOnlyValidation": "false", "formatType": "datetime", "obfuscated": false, "showAs24HourFormat": false}], "actionSets": [{"actions": [{"action": "refresh", "refreshFor": "RHVyYXRhUG9saXp6YVVHLUlucy1QVS1EYXRhLUFtYml0by5EYXRhRGVjb3JyZW56YTIwMjMwMzA4MTQ1MjAxMDMxMTM3MQ%3D%3D"}], "events": [{"event": "change"}]}], "type": "pxDateTime"}, "label": "Data inizio", "type": "Date", "isMasked": false, "required": false, "validateAs": "", "reference": "Ambito.DataDecorrenza", "labelFormat": "TEXT APP GDB16 WEB BAB16 BAB16 BAB16", "isEncrypted": false, "disabled": false, "testID": "202303081452010311371", "valueFormatting": "", "value": "20230226", "maxLength": 0, "expectedLength": "", "fieldID": "DataDecorrenza", "customAttributes": {"dateFormat": "gg/mm/aaaa"}}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "accessCondition": "", "readOnly": false, "control": {"modes": [{"dateTime": "auto", "showReadOnlyFormatting": false, "allowTextEntry": true, "useFutureDateRange": true, "textAlign": "Left", "tooltip": "", "usePastDateRange": true, "ignoreLocaleSettings": false, "futureDateRange": "10", "displayLongFormat": false, "displayMode": "calendar", "calendarNavigation": "true", "minChars": "", "modeType": "editable", "controlFormat": "", "maxChars": "", "formatType": "text", "specifySize": "auto", "obfuscated": false, "pastDateRange": "10"}, {"modeType": "readOnly", "dateTimeFormat": "DateTime-Short", "tooltip": "", "showReadOnlyValidation": "false", "formatType": "datetime", "obfuscated": false, "showAs24HourFormat": false}], "actionSets": [], "type": "pxDateTime"}, "label": "Orario inizio", "type": "TimeOfDay", "isMasked": false, "required": false, "validateAs": "", "reference": "Ambito.OraDecorrenza", "labelFormat": "TEXT APP GDB16 WEB BAB16 BAB16 BAB16", "isEncrypted": false, "disabled": false, "testID": "202303081452010311651", "valueFormatting": "", "value": "230000", "maxLength": 0, "expectedLength": "", "fieldID": "OraDecorrenza", "customAttributes": {"dateFormat": "hh/mm"}}}], "groupFormat": "Row Responsive2colL", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "accessCondition": "", "readOnly": false, "control": {"modes": [{"modeType": "editable", "controlFormat": "Standard", "listSource": "locallist", "textAlign": "Left", "options": [{"value": "1 anno", "key": "1 anno"}, {"value": "2 anni", "key": "2 anni"}, {"value": "3 anni", "key": "3 anni"}, {"value": "4 anni", "key": "4 anni"}, {"value": "5 anni", "key": "5 anni"}], "tooltip": "", "maxChars": "", "formatType": "none", "specifySize": "auto", "minChars": ""}, {"modeType": "readOnly", "tooltip": "", "showReadOnlyValidation": "false", "formatType": "none"}], "actionSets": [], "type": "pxDropdown"}, "label": "<PERSON>rata polizza", "type": "Text", "isMasked": false, "required": false, "validateAs": "", "reference": "Ambito.Durata", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202302211128270137321", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "<PERSON><PERSON>"}}], "groupFormat": "StackedTPD", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "readOnly": false, "title": ""}}], "groupFormat": "Row Responsive2colL", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TL 20 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 20 0 20 24 D 20 0 20 28 T 20 0 20 28 M 16 0 16 28", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Box WebBorderGray1", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"modeType": "ignore", "iconStandard": "pxIcon", "tooltip": "", "iconSource": "standardIcon"}, {"modeType": "readOnly", "iconStandard": "pxIcon", "autoPrepend": "", "controlFormat": "Standard", "tooltip": "", "showReadOnlyValidation": "false", "autoAppend": "", "formatType": "text", "iconSource": "standardIcon"}], "actionSets": [], "type": "pxIcon"}, "label": "Image", "type": "Text", "isMasked": false, "required": false, "showLabel": false, "validateAs": "", "reference": "pyTemplateGeneric", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202212151724460778105", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"size": "S", "resource": "carNegative", "type": "icon"}}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "isEncrypted": false, "testID": "202212151802080210331", "control": {"format": "TEXT APP WHB16 WEB WHL16 WHL16 WHL16", "testID": "202212151802080210331"}, "isMasked": false, "value": "<PERSON><PERSON><PERSON><PERSON>"}}], "groupFormat": "Row CT 4 5", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "isEncrypted": false, "testID": "202212151802080216827", "control": {"format": "TEXT APP WHL13 WEB WHL20 WHL13 WHL13", "testID": "202212151802080216827"}, "isMasked": false, "value": "Prezzo al mese"}}], "groupFormat": "Row CT 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TC 8 16 8 8", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TC 24 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "isEncrypted": false, "testID": "202302171325330643497", "control": {"format": "TEXT APP BDB16 WEB BDB20 BDB14 BDB14", "testID": "202302171325330643497"}, "isMasked": false, "value": "Per<PERSON><PERSON><PERSON> sconto"}}], "groupFormat": "Accordion 7 Ribbon", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Accordi<PERSON><PERSON><PERSON><PERSON>", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"view": {"reference": "Ambito", "validationMessages": "", "viewID": "WrapperRiepilogo", "visible": true, "titleFormat": "", "name": "WrapperRiepilogo", "appliesTo": "UG-Ins-PU-Data-Ambito", "containerFormat": "NOHEADER", "groups": [{"layout": {"newRow": {"groups": []}, "visible": true, "repeatContainerFormat": "NOHEADER", "fieldListID": "<PERSON><PERSON>", "referenceType": "List", "readOnly": false, "title": "", "rows": [{"groups": [{"view": {"reference": "Ambito<PERSON>(1)", "validationMessages": "", "viewID": "Riepilogo", "visible": true, "titleFormat": "h2", "name": "Riepilogo", "appliesTo": "UG-Ins-PU-Data-Pacchetto", "containerFormat": "NOHEADER", "groups": [{"layout": {"newRow": {"groups": []}, "visible": true, "repeatContainerFormat": "NOHEADER", "fieldListID": ".Sezioni", "referenceType": "List", "readOnly": false, "title": "", "rows": [{"groups": [{"view": {"reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(1).Sezioni(1)", "validationMessages": "", "viewID": "DatiRiepilogo", "visible": true, "titleFormat": "h2", "name": "DatiRiepilogo", "appliesTo": "UG-Ins-PU-Data-Sezione", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "NomeSezione", "isEncrypted": false, "testID": "202303031447520050581", "control": {"format": "TEXT APP WHB13 WEB WHM16 WHM16 WHM16", "testID": "202303031447520050581"}, "isMasked": false, "value": "SEZIONE RCA"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "accessCondition": "", "readOnly": true, "control": {"modes": [{"displayWithReadOnlyFormat": "true", "modeType": "editable", "controlFormat": "", "textAlign": "Right", "tooltip": "", "maxChars": "", "formatType": "number", "specifySize": "auto", "minChars": ""}, {"currencyType": "local", "symbolPosition": "left", "hasSeparators": true, "textAlign": "Right", "tooltip": "", "showReadOnlyValidation": "false", "symbolValue": "", "numberScale": "none", "numberSymbol": "currency", "roundingMethod": "", "negativeFormat": "minusStyle", "modeType": "readOnly", "decimalPlaces": "2", "negativeFormatStyle": "NegativeNumber", "currencySymbolPosition": "default", "formatType": "number", "otherCurrencySymbol": "", "obfuscated": false, "displayCurrencyAs": "currencySymbol"}], "actionSets": [], "type": "pxC<PERSON>rency"}, "label": "", "type": "Text", "isMasked": false, "required": false, "validateAs": "", "reference": "Ambito.<PERSON><PERSON><PERSON>(1).Sezioni(1).PremioSezione", "labelFormat": "TEXT APP WHB14 WEB WHB16 WHB16 WHB16", "isEncrypted": false, "disabled": true, "testID": "202303081256230741537", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "PremioSezione"}}], "groupFormat": "Row SBT 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "N Col TL 4 8", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"newRow": {"groups": []}, "visible": true, "repeatContainerFormat": "NOHEADER", "fieldListID": "<PERSON>", "referenceType": "List", "readOnly": false, "title": "", "rows": [{"groups": [{"view": {"reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(1).Sezioni(1).G<PERSON><PERSON>(1)", "validationMessages": "", "viewID": "DatiRiepilogo", "visible": true, "titleFormat": "h2", "name": "DatiRiepilogo", "appliesTo": "UG-Ins-PU-Data-Garanzia", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "isEncrypted": false, "testID": "202303031447000190866", "control": {"format": "TEXT APP WHL13 WEB WHM13 WHM13 WHM13", "testID": "202303031447000190866"}, "isMasked": false, "value": "GARANZIA RCA"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "accessCondition": "", "readOnly": true, "control": {"modes": [{"displayWithReadOnlyFormat": "true", "modeType": "editable", "controlFormat": "", "textAlign": "Right", "tooltip": "", "maxChars": "", "formatType": "number", "specifySize": "auto", "minChars": ""}, {"currencyType": "local", "symbolPosition": "left", "hasSeparators": true, "textAlign": "Right", "tooltip": "", "showReadOnlyValidation": "false", "symbolValue": "", "numberScale": "none", "numberSymbol": "currency", "roundingMethod": "", "negativeFormat": "minusStyle", "modeType": "readOnly", "decimalPlaces": "2", "negativeFormatStyle": "NegativeNumber", "currencySymbolPosition": "default", "formatType": "number", "otherCurrencySymbol": "", "obfuscated": false, "displayCurrencyAs": "currencySymbol"}], "actionSets": [], "type": "pxC<PERSON>rency"}, "label": "", "type": "Decimal", "isMasked": false, "required": false, "validateAs": "", "reference": "Ambito.<PERSON><PERSON><PERSON>(1).Sezioni(1).Garanzie(1).PremioLordo", "labelFormat": "TEXT APP WHB14 WEB WHB16 WHB16 WHB16", "isEncrypted": false, "disabled": true, "testID": "202303031447000191254", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "PremioLordo"}}], "groupFormat": "N Row SBT 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "readOnly": false, "title": ""}}], "testID": "202303031447520050464-1"}], "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(1).Sezioni(1).Garanzie", "repeatLayoutFormat": "<PERSON><PERSON><PERSON>", "titleFormat": "h2", "sourceType": "Property", "containerFormat": "NOHEADER", "testID": "202303031447520050464", "groupFormat": "Dynamic", "layoutFormat": "REPEATINGLAYOUT"}}], "groupFormat": "Col TL 24 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "readOnly": false, "title": ""}}], "testID": "202303031448000731853-1"}], "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(1).Sezioni", "repeatLayoutFormat": "<PERSON><PERSON><PERSON>", "titleFormat": "h2", "sourceType": "Property", "containerFormat": "NOHEADER", "testID": "202303031448000731853", "groupFormat": "Dynamic", "layoutFormat": "REPEATINGLAYOUT"}}], "readOnly": false, "title": ""}}], "testID": "202303031448060206121-1"}], "reference": "Ambito<PERSON>", "repeatLayoutFormat": "<PERSON><PERSON><PERSON>", "titleFormat": "h2", "sourceType": "Property", "containerFormat": "NOHEADER", "testID": "202303031448060206121", "groupFormat": "Dynamic", "layoutFormat": "REPEATINGLAYOUT"}}], "readOnly": false, "title": ""}}], "groupFormat": "Accordion<PERSON>pened", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Accordion 7", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TC 24 16 24 16 MW528", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 20 24 20 32 D 0 32 T 0 24 M 16 24", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "isEncrypted": false, "testID": "202212191426330264956", "control": {"format": "TEXT APP GDB16 WEB BDM24C BDM16C BDM16C", "testID": "202212191426330264956"}, "isMasked": false, "value": "Scegli altre protezioni"}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"modeType": "ignore", "tooltip": ""}, {"modeType": "readOnly", "autoPrepend": "", "tooltip": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "isMasked": false, "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202212191426330268861", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "CarouselWeb", "paddingmobile": "0 28 0 40", "paddingDesktop": "0 40 0 40", "paddingTablet": "0 24 0 24", "paddingApp": "0 0 0 0"}}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"modeType": "ignore", "tooltip": ""}, {"modeType": "readOnly", "autoPrepend": "", "tooltip": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "isMasked": false, "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202212191426330268861", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "CardProtezione"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"view": {"reference": "", "validationMessages": "", "viewID": "BoxRichiestaDiContatto", "visible": true, "titleFormat": "", "name": "BoxRichiestaDiContatto", "appliesTo": "UG-Ins-PU-Work-Quotazione", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "isEncrypted": false, "testID": "202302271150130514421", "control": {"format": "TEXT APP BDM16 WEB BDM24 BDM24 BDM16", "testID": "202302271150130514421"}, "isMasked": false, "value": "Ti diamo supporto"}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"paragraph": {"visible": true, "appliesTo": "UG-Ins-PU-Work-Quotazione", "paragraphID": "BoxRichiestaDiContattoWEB", "testID": "202212201135080193565", "readOnly": true, "value": "<p data-pega-style=\"Text APP BDL16 WEB BDL16 BDL16 BDL16\">Parla con noi: un nostro consulente ti aiuter&agrave; a trovare la soluzione migliore. C&#39;&egrave; sempre un&#39; Agenzia UnipolSai vicino a te.</p>"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"imageSource": "none", "modeType": "ignore", "tooltip": ""}, {"modeType": "readOnly", "imageSource": "none", "autoPrepend": "", "controlFormat": "Highlight", "tooltip": "", "showReadOnlyValidation": "false", "autoAppend": "", "formatType": "text"}], "actionSets": [], "label": "Hai bisogno di aiuto?", "type": "px<PERSON><PERSON><PERSON>"}, "label": "", "type": "Text", "isMasked": false, "required": false, "showLabel": false, "validateAs": "", "reference": "pyTemplateButton", "labelFormat": "", "isEncrypted": false, "disabled": false, "testID": "202212201135080194164", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton"}}], "groupFormat": "Col TC 0 16 16 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Box CB 24", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TC 0 32 32 24", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"modeType": "ignore", "iconStandard": "pxIcon", "tooltip": "", "iconSource": "standardIcon"}, {"modeType": "readOnly", "iconStandard": "pxIcon", "autoPrepend": "", "controlFormat": "Standard", "tooltip": "", "showReadOnlyValidation": "false", "autoAppend": "", "formatType": "text", "iconSource": "standardIcon"}], "actionSets": [], "type": "pxIcon"}, "label": "Image", "type": "Text", "isMasked": false, "required": false, "showLabel": false, "validateAs": "", "reference": "pyTemplateGeneric", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202212201148470288169", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"size": "M", "resource": "assistenza_clienti", "type": "icon"}}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "isEncrypted": false, "testID": "202212201148470289699", "control": {"format": "TEXT APP BDM16 WEB BDM24 BDM24 BDM16", "testID": "202212201148470289699"}, "isMasked": false, "value": "TI DIAMO SUPPORTO"}}], "groupFormat": "Row LC 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"paragraph": {"visible": true, "appliesTo": "UG-Ins-PU-Work-Quotazione", "paragraphID": "BoxRichiestaDiContattoAPP", "testID": "202212201135080193565", "readOnly": true, "value": "<p data-pega-style=\"TEXT APP GDL16 WEB BDL16 BDL16 BDL16 \">Parla con noi: un nostro consulente ti aiuter&agrave; a trovare la soluzione migliore. C&#39;&egrave; sempre un&#39;Agenzia UnipolSai vicino a te.</p>"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"imageSource": "none", "modeType": "ignore", "tooltip": ""}, {"modeType": "readOnly", "imageSource": "none", "autoPrepend": "", "controlFormat": "Cancel button", "tooltip": "", "showReadOnlyValidation": "false", "autoAppend": "", "formatType": "text"}], "actionSets": [], "label": "HAI BISOGNO DI AIUTO?", "type": "px<PERSON><PERSON><PERSON>"}, "label": "", "type": "Text", "isMasked": false, "required": false, "showLabel": false, "validateAs": "", "reference": "pyTemplateButton", "labelFormat": "", "isEncrypted": false, "disabled": false, "testID": "202212201148470291629", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton"}}], "groupFormat": "Col TL 16 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 20 32 D 0 T 0 M 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Card WH 20 1 GL", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "readOnly": false, "title": ""}}], "groupFormat": "P 0 D 0 40 0 0 T 0 24 0 0 M 0 28 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"paragraph": {"visible": true, "appliesTo": "UG-Ins-PU-Work-Quotazione", "paragraphID": "InformativaDatiRiepilogo", "testID": "202212201205180259274", "readOnly": true, "value": "<p data-pega-style=\"BoxInfoText\">Esenzione imposte e causale esenzione.... continuare</p>"}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "accessCondition": "", "readOnly": false, "control": {"modes": [{"imageSource": "none", "modeType": "ignore", "tooltip": ""}, {"modeType": "readOnly", "imageSource": "none", "autoPrepend": "", "controlFormat": "Simple<PERSON>rey", "tooltip": "", "showReadOnlyValidation": "false", "autoAppend": "", "formatType": "text"}], "actionSets": [], "label": "Leggi di più", "type": "px<PERSON><PERSON><PERSON>"}, "label": "", "type": "Text", "isMasked": false, "required": false, "showLabel": true, "validateAs": "", "reference": "pyTemplateButton", "labelFormat": "TEXT WEB BDBN16R BDBN16R BDBN16R", "isEncrypted": false, "disabled": false, "testID": "202212131747220946511", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton"}}], "groupFormat": "RightWebLeftApp", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "BoxInfo", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 0 D 0 20 0 0 T 0 24 0 0 M 0 16 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "CardBgGreyApp", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"view": {"reference": "", "validationMessages": "", "viewID": "FondoPagina", "visible": true, "titleFormat": "", "name": "FondoPagina", "appliesTo": "UG-Ins-PU-Work-Quotazione", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"modeType": "ignore", "tooltip": ""}, {"modeType": "readOnly", "autoPrepend": "", "tooltip": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "isMasked": false, "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202301021843320704959", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "StickyFooterPU"}}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "isEncrypted": false, "testID": "202301021843320704118", "control": {"format": "TEXT APP WHM16 WEB BDL13 BDL13 BDL13", "testID": "202301021843320704118"}, "isMasked": false, "value": "TOTALE"}}, {"view": {"reference": "", "validationMessages": "", "viewID": "MostraPremioFondoPagina", "visible": true, "titleFormat": "", "name": "MostraPremioFondoPagina", "appliesTo": "UG-Ins-PU-Work-Quotazione", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "accessCondition": "", "readOnly": true, "control": {"modes": [{"displayWithReadOnlyFormat": "true", "modeType": "editable", "controlFormat": "", "textAlign": "Right", "tooltip": "", "maxChars": "", "formatType": "number", "specifySize": "auto", "minChars": ""}, {"currencyType": "local", "symbolPosition": "right", "hasSeparators": true, "textAlign": "Left", "tooltip": "", "showReadOnlyValidation": "false", "symbolValue": "u20AC", "numberScale": "none", "numberSymbol": "constant", "roundingMethod": "HALF_UP", "negativeFormat": "none", "modeType": "readOnly", "decimalPlaces": "2", "negativeFormatStyle": "NegativeNumber", "currencySymbolPosition": "left", "formatType": "number", "otherCurrencySymbol": "", "obfuscated": false, "displayCurrencyAs": "currencySymbol"}], "actionSets": [], "type": "pxC<PERSON>rency"}, "label": "", "type": "Text", "isMasked": false, "required": false, "validateAs": "", "reference": "Ambito.PremioPosizione", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202301031143470444207", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "PremioPosizione"}}], "groupFormat": "Mimic a sentence", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "readOnly": false, "title": ""}}], "groupFormat": "PriceContainer", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"imageSource": "none", "modeType": "ignore", "tooltip": ""}, {"modeType": "readOnly", "imageSource": "none", "autoPrepend": "", "controlFormat": "PositiveTPD Button", "tooltip": "", "showReadOnlyValidation": "false", "autoAppend": "", "formatType": "text"}], "actionSets": [{"actions": [{"action": "runDataTransform"}, {"action": "finishAssignment"}], "events": [{"event": "click"}]}], "label": "Aggiungi al carrello", "type": "px<PERSON><PERSON><PERSON>"}, "label": "<PERSON><PERSON>", "type": "Text", "isMasked": false, "required": false, "showLabel": false, "validateAs": "", "reference": "pyTemplateButton", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202301021843320707291", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"componentID": "footerPrimaryCTA", "GestioneProcesso.StepSuccessivo": "Aggiungi a Carrello"}}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"submitButton": "true", "componentID": "footerPrimaryCTA"}}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"submitButton": "true", "componentID": "footerPrimaryCTA"}}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "accessCondition": "", "readOnly": false, "control": {"modes": [{"imageSource": "none", "modeType": "ignore", "tooltip": ""}, {"modeType": "readOnly", "imageSource": "none", "autoPrepend": "", "controlFormat": "Simple NegativeApp", "tooltip": "", "showReadOnlyValidation": "false", "autoAppend": "", "formatType": "text"}], "actionSets": [{"actions": [{"action": "runDataTransform"}, {"action": "finishAssignment"}], "events": [{"event": "click"}]}], "label": "Salva per dopo", "type": "px<PERSON><PERSON><PERSON>"}, "label": "<PERSON><PERSON>", "type": "Text", "isMasked": false, "required": false, "showLabel": false, "validateAs": "", "reference": "pyTemplateButton", "labelFormat": "Standard", "isEncrypted": false, "disabled": false, "testID": "202301021843320707938", "valueFormatting": "", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"componentID": "footerSecondaryCTA", "FlagSalvaPerDopo": "true"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "readOnly": false, "title": ""}}], "title": ""}, "caseID": "UG-INS-PU-WORK-QUOTAZIONE Q-22144", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "actionID": "RiepilogoDati"}