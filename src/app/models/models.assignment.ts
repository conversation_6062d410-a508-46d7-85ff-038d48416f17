import { IView } from './models.commons';

export interface IAssignmentsResponse {
  pxObjClass: string;
  assignments: IAssignment[];
}

export interface IAssignment {
  caseID: string;
  executedDeadlineTime: string;
  executedGoalTime: string;
  ID: string;
  name: string;
  pxObjClass: string;
  routedTo: string;
  scheduledDeadlineTime: string;
  scheduledGoalTime: string;
  type: string;
  urgency: number;
  instructions: string;
}

export interface IErrorResponse {
  pxObjClass: string;
  errors: IError[];
}

export interface IError {
  ID: string;
  message: string;
  pxObjClass: string;
}

export interface IAssignmentResponse {
  ID: string;
  caseID: string;
  name: string;
  pxObjClass: string;
  type: string;
  routedTo: string;
  instructions: string;
  scheduledGoalTime: string;
  executedGoalTime: string;
  scheduledDeadlineTime: string;
  executedDeadlineTime: string;
  urgency: number;
  actions: IAssignmentAction[];
}

export interface IAssignmentAction {
  ID: string;
  name: string;
  type: string;
  pxObjClass: string;
}

export interface IAssignmentPostResponse {
  nextAssignmentID: string;
  nextPageID: string;
}

export interface IActionResponse {
  actionID: string;
  caseID: string;
  name: string;
  view: IView;
}
