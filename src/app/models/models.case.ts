import {
  IAction,
  IAttachments,
  //IContent,
  IPageInstruction,
  ISLA,
  IStage,
  IStartingProcess,
  IView,
} from './models.commons';

export interface ICasesResponse {
  pxObjClass?: string;
  cases?: ICase[];
}

export interface ICase {
  ID?: string;
  parentCaseID?: string;
  caseTypeID?: string;
  name?: string;
  pxObjClass?: string;
  stage?: string;
  status?: string;
  urgency?: string;
  createTime?: Date | string;
  createdBy?: string;
  lastUpdateTime?: Date | string;
  lastUpdatedBy?: string;
}

export interface ICreateCaseResponse {
  ID?: string;
  pxObjClass?: string;
  nextAssignmentID?: string;
  nextPageID?: string;
}

export interface ICaseResponse {
  status?: string;
  caseTypeID?: string;
  name?: string;
  ID?: string;
  parentCaseID?: string;
  pxObjClass?: string;
  stage?: string;
  urgency?: string;
  createTime?: Date | string;
  createdBy?: string;
  lastUpdateTime?: Date | string;
  lastUpdatedBy?: string;
  owner?: string;
  content?: any; //IContent
  stages?: IStage[];
  SLA?: ISLA;
  childCases?: IChildCase[];
  childCaseTypes?: ICaseType[];
  assignments?: ICaseAssignment[];
  actions?: IAction[];
}

export interface IChildCase {
  ID?: string;
  pxObjClass?: string;
}

export interface ICaseType {
  ID?: string;
  name?: string;
  CanCreate?: boolean;
  pxObjClass?: string;
  startingProcesses?: IStartingProcess[];
}

export interface ICaseAssignment {
  ID?: string;
  name?: string;
  type?: string;
  pxObjClass?: string;
  routedTo?: string;
  instructions?: string;
  scheduledGoalTime?: Date | string;
  executedGoalTime?: Date | string;
  scheduledDeadlineTime?: Date | string;
  executedDeadlineTime?: Date | string;
  urgency?: string;
  actions?: IAction[];
}

// request per POST
export interface ICreateCaseRequest {
  caseTypeID: string;
  processID?: string;
  parentCaseID?: string;
  content?: any; //IContent
  pageInstructions?: IPageInstruction[];
  attachments?: IAttachments;
}
