import { AmbitoDiBisognoCode } from '@tpd-web-angular-libs/angular-library';

export interface ConfigInterpreteDxAPI {
  env: string;
  productType: ProductType | AmbitoDiBisognoCode;

  doRecupero?: boolean;

  doRetrieve?: boolean;
  doRetrieveAssignmentId?: string;
  doRetrieveCaseId?: string;

  mantieniDatiUtente?: boolean;

  marketingCardDataContentId?: string;
  initData?: Record<string, any>;
  showCarrello?: boolean;
  showRichiestaDiContatto?: boolean;
  paymentService?: string;
  productBoxReasonWhy?: ProductTypeBoxReasonWhy;
  dxCategory?: DxCategory;
  isLoadingWithOverlay?: boolean;
  analyticsProductModule?: any;
  abTesting?: string;
  cf?: string;
}

export enum ProductType {
  AUTO = 'AUTO',
  CASA = 'CASA',
  VEICOLO = 'VEICOLO',
  PET = 'PET',
  VIAGGI = 'VIAGGI',
  MOBILITA = 'MOBILITA',
  UNICO = 'UNICO',
}

export enum ProductTypeBoxReasonWhy {
  Casa = 'Casa',
  Viaggi = 'Viaggi',
  CaneGatto = 'CaneGatto',
}

export enum DxCategory {
  CASA = 'abitazione',
  PET = 'pet',
  VIAGGI = 'viaggi',
}

export enum TestingKey {
  ABC = 'ab_vendita_ibrida',
}
