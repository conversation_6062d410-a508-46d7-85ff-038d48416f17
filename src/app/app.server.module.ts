import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ServerModule } from '@angular/platform-server';
import { importsModule } from './imports-module';
import { providersModule } from './providers-module';
import { TpdInterpreteDxApi } from './tpd-interprete-angular-dx-api-pu/tpd-interprete-angular-dx-api-pu';
import { declarationsModule } from './declarations-module';

@NgModule({
  declarations: [...declarationsModule()],
  imports: [ServerModule, ...importsModule()],
  providers: [...providersModule()],
  bootstrap: [TpdInterpreteDxApi],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AppServerModule {}
