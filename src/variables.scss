@import "../../../../node_modules/@tpd-web-angular-libs/angular-library/styles/variables.scss";
@import "../../../../node_modules/@tpd-web-angular-libs/angular-library/styles/mixin.scss";

/* font-size */
//Tag garanzie
$font-tag-size: 12px;

// Text
$font-text-size: 16px;

//Text promo
$font-text-promo-size: 18px;

// Text-responsive
$font-text-responsive-mobile-size: 16px;
$font-text-responsive-tablet-size: 18px;
$font-text-responsive-desktop-size: 20px;

// Text-responsive S
$font-text-responsiveS-mobile-size: 15px;
$font-text-responsiveS-tablet-size: 16px;
$font-text-responsiveS-desktop-size: 16px;

// Text-responsive M
$font-text-responsiveM-mobile-size: 16px;
$font-text-responsiveM-tablet-size: 24px;
$font-text-responsiveM-desktop-size: 24px;

//Heading-white-bold
$font-heading-white-mobile-size: 28px;
$font-heading-white-tablet-size: 30px;
$font-heading-white-desktop-size: 30px;

// Heading-1
$font-heading1-mobile-size: 28px;
$font-heading1-tablet-size: 40px;
$font-heading1-desktop-size: 40px;

// Heading-2
$font-heading2-mobile-size: 22px;
$font-heading2-tablet-size: 24px;
$font-heading2-desktop-size: 24px;

// Heading-3
$font-heading3-mobile-size: 20px;
$font-heading3-tablet-size: 30px;
$font-heading3-desktop-size: 30px;

// Heading-4
$font-heading4-mobile-size: 16px;
$font-heading4-tablet-size: 24px;
$font-heading4-desktop-size: 24px;

// Heading-5
$font-heading5-size: 24px;

// Heading-8
$font-heading8-size: 28px;

// Text XS
$font-text-xs-mobile-size: 13px;
$font-text-xs-tablet-size: 14px;
$font-text-xs-desktop-size: 14px;

//text-radio-option vertical
$font-radio-vertical-mobile-size: 20px;
$font-radio-vertical-tablet-size: 20px;
$font-radio-vertical-desktop-size: 24px;
// text-radio-option horizontal
$font-radio-horizontal-mobile-size: 16px;
$font-radio-horizontal-tablet-size: 20px;
$font-radio-horizontal-desktop-size: 24px;

//roundedCard text
$font-rounded-card-mobile-size: 16px;
$font-rounded-card-tablet-size: 22px;
$font-rounded-card-desktop-size: 22px;

/* font-family */
$font-family-default: "Unipol";
$font-family-bold: "Unipol Bold";
$font-family-medium: "Unipol Medium";

// padding-container
$padding-container: 30px 20px;
$padding-container-input-mobile: 8px 0;
$padding-container-input-tablet: 12px 0;
$padding-container-input-desktop: 12px 0;

// color
$selected-radio: #1285bf;
$link-color: #23527c;
$middle-grey: #666666;
$middle-grey-pu: #5c5c5c;
$alert-color: #ff001f;
$property-light-color: #f39200;
$property-color: #e94e10;
$mobility-color: #0169b4;
$sub-button-color: #d0021b;
$secondary-darkest: #316789;
$secondary-lightest: #e2f0f9;
$secondary-opaque: #8ab5d1;
$color-darker: #0f3250;
$color-primary: #0f3250;
$background-card-disabled: #f0f0f0;
$border-card-disabled: #cccccc;
$scrollbar-gray-color: #d9d9d9;
$advice-color: #fdf5f2;
$blue-primary: #193a56;
$ribbon-color: #6bd1ff;
$unico-protezione-auto-color: #0169b4;
$unico-protezione-casa-color: #e94e10;
$unico-protezione-salute-color: #59a627;
$unico-protezione-viaggi-color: #e94e10;
$unico-protezione-mobilita-color: #0169b4;
$unico-protezione-pet-color: #e94e10;
$unico-protezione-infortuni-color: #59a627;
$unico-protezione-malattia-color: #59a627;
$carousel-navigator-pin-disabled: #cbdeea;
$assurance-package-container-color: #1f5b8e;
$assurance-package-deselected-container-color: #88a6bf;
$assurance-package-deselected-text: #88a6bf;
$assurance-package-icon-select: #8bc94d;
$assurance-package-deselected-text: #cee1f3;

$check-disabled-color: #cbdeea;
$check-green-color: #38c661;
$check-green-disabled-color: #c3e3a3;
$check-error-color: #f40053;

$text-card-blue: #5393bc;
// space variables
$mrg-btm-label-input: 10px;
$mrg-btm-label-input-pu: 4px;

// width container
$width_container_mobile: 100%;
$width_container_tablet: 90vw;
$width_container_desktop: 75vw;

//carosello carte
$slick-opacity-on-hover: 1;
$slick-dot-character: "\2022";
$slick-font-family: "Unipol";
$slick-dot-size: 40px;
$slick-dot-color: $medium-light-blue;
$slick-opacity-not-active: 0.25;
$slick-dot-color-active: $dark-light_blue;
$slick-opacity-default: 0.75;
