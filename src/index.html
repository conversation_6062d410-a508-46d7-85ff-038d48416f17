<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>tpdInterpreteNg15</title>
    <base href="/" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="favicon.ico" />

  </head>
  <body>
    <!-- Remotes importer -->
    <script src="http://localhost:2999/client/main.js"></script>
    <!-- Component -->
    <tpd-interprete-angular-dx-api-pu-container
      data-props='{
      "__mfeName__": "tpdInterpreteAngularDxApiPu",
      "contentId": "tpd_interprete_angular_dx_api_pu-9",
      "projectId": "13123sds23dasd",
      "config": "config",
    }'
    ></tpd-interprete-angular-dx-api-pu-container>
  </body>
</html>
