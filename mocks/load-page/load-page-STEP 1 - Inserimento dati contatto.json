{"pegaBodyResponse": {"view": {"reference": "", "validationMessages": "", "viewID": "RaccoltaDatiContatto", "visible": true, "titleFormat": "", "name": "RaccoltaDatiContatto", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"view": {"reference": "", "validationMessages": "", "viewID": "AnalyticsHidden", "visible": true, "titleFormat": "", "name": "AnalyticsHidden", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202212131640340513751", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "analyticsPageFields", "funnel_name": "Prodotto Unico", "page_name": "unico:dati contatto"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "20230526173946071719", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"id_folder": "e7ec49d2-6c76-48f4-a6ce-f88958a8a7d8", "id_contract_number": "353753"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"view": {"reference": "", "validationMessages": "", "viewID": "Intestazione", "visible": true, "titleFormat": "", "name": "Intestazione", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202212131640340513751", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "Header", "background": "Secondary"}}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "Standard", "testID": "202301131218420210633"}, "value": "I tuoi contatti", "testID": "202301131218420210633"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore", "iconSource": "image", "iconImage": "webwb/pymenuleftarrow.png"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Standard", "formatType": "text", "showReadOnlyValidation": "false", "iconSource": "image", "iconImage": "webwb/pymenuleftarrow.png"}], "actionSets": [{"actions": [{"action": "takeAction", "actionProcess": {"actionName": "Indietro"}}], "events": [{"event": "click"}]}], "type": "pxIcon"}, "label": "Image", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateGeneric", "labelFormat": "Standard", "disabled": false, "testID": "202212141106470503509", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"componentID": "_back", "size": "S", "resource": "arrowLeft", "type": "icon"}, "showLabel": false}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}, {"view": {"reference": "", "validationMessages": "", "viewID": "BarraDiAvanzamento", "visible": true, "titleFormat": "", "name": "BarraDiAvanzamento", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202212131640340513751", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"currentStep": "0", "labelFormatDeselected": "TEXT APP GRL13 WEB GRL14 GRL14 GRL14", "customType": "Stepper", "labelFormatPreviousStep": "TEXT APP BDL13 WEB BDL14 BDL14 BDL14", "progress": "20", "style": "StepperPU", "labelFormatCurrentStep": "TEXT APP BDM13 WEB BDM14 BDM14 BDM14", "labelsStepper": "Preventivo|Carrello|Acquisto"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"paragraph": {"visible": true, "appliesTo": "UG-Ins-PU-Work-Quotazione", "paragraphID": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "readOnly": true, "value": "<p data-pega-style=\"TEXT APP GDL16 WEB BDL16C BDL16C BDL16C\">Un ultimo step prima del preventivo. Inserisci i dati di contatto dove vuoi ricevere l’offerta</p>", "testID": "202212131813470286253"}}, {"view": {"reference": "<PERSON><PERSON><PERSON>", "validationMessages": "", "viewID": "MostraContatti", "visible": true, "titleFormat": "", "name": "MostraContatti", "appliesTo": "UG-Ins-PU-Data-Persona", "groups": [{"view": {"viewID": "MostraContattiCasa", "visible": false, "name": "MostraContattiCasa"}}, {"view": {"reference": "Contraente.Contatti", "validationMessages": "", "viewID": "MostraContattiNoCasa", "visible": true, "titleFormat": "h2", "name": "MostraContattiNoCasa", "appliesTo": "UG-Ins-PU-Data-Contatti", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": false, "control": {"modes": [{"textAlign": "Left", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "Standard", "specifySize": "auto", "formatType": "none", "placeholder": "es: ma<PERSON><PERSON><PERSON>@gmail.com", "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "formatType": "none", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "postValue"}], "events": [{"event": "change"}]}], "type": "pxTextInput"}, "label": "Email", "type": "Text", "required": false, "validateAs": "", "reference": "Contraente.Contatti.Email", "labelFormat": "TEXT APP GDM16 WEB BAB16 BAB16 BAB16", "disabled": false, "testID": "20221213165608043390", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "Email", "customAttributes": {"requiredIF": "true", "conditionReference": "Contraente.Contatti.Cellulare", "validation": "email", "memorizzaEmailContraente": "true"}}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": false, "control": {"modes": [{"textAlign": "Left", "tooltip": "", "minChars": "9", "modeType": "editable", "controlFormat": "Standard", "specifySize": "auto", "formatType": "none", "placeholder": "es: 123-4567890", "maxChars": "10"}, {"tooltip": "", "modeType": "readOnly", "formatType": "none", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "postValue"}], "events": [{"event": "change"}]}], "type": "pxTextInput"}, "label": "Cellulare", "type": "Text", "required": false, "validateAs": "", "reference": "Contraente.Contatti.Cellulare", "labelFormat": "TEXT APP GDM16 WEB BAB16 BAB16 BAB16", "disabled": false, "testID": "202212131656080433746", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "Cellulare", "customAttributes": {"memorizzaCellulareContraente": "true", "requiredIF": "true", "conditionReference": "Contraente.Contatti.Email", "validation": "mobilephone"}}}], "groupFormat": "Col TL 28 20 20 20", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}, {"view": {"viewID": "MostraContatti", "visible": false, "name": "MostraContatti"}}], "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202212221633290890448", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "Separator", "renderPlatform": "Web"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"view": {"reference": "", "validationMessages": "", "viewID": "BoxPrivacy", "visible": true, "titleFormat": "", "name": "BoxPrivacy", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "20240403125250066042", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "BoxPrivacy", "privacyLabelFormatAPP": "TEXT APP BDB16 WEB", "privacyLabelAPP": "Leggi l’informativa", "paddingApp": "0 0 0 32"}}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "BoxPrivacyTitle STYLE TEXT APP BDM16 WEB BDB16 BDB16 BDB16", "testID": "202404031252500660703"}, "value": "Informativa sul trattamento dei dati personali per finalità di marketing", "testID": "202404031252500660703"}}, {"paragraph": {"visible": true, "appliesTo": "UG-Ins-PU-Work-Quotazione", "paragraphID": "InformativaPrivacy", "readOnly": true, "value": "<p data-pega-style=\"TEXT APP BDL16I WEB BDL16I BDL16I BDL16\">Ai sensi dell&rsquo;art. 13 del Regolamento (UE) n.679/2016 e degli artt. 82 e 83 del Reg.to Ivass n. 40/2018 (artt. 183 e 191 D.Lgs. 209/05 &ndash; Codice Assicurazioni Private)</p>\n\n<p data-pega-style=\"TEXT APP BDL16 WEB BDL16 BDL16 BDL16\">Desideriamo informarLa che UnipolSai Assicurazioni S.p.A. (di seguito &ldquo;UnipolSai&rdquo;), intende utilizzare per finalit&agrave; commerciali alcuni dati personali, con esclusione di dati appartenenti a categorie particolari1, da Lei volontariamente rilasciati o comunque acquisiti nell&#39;ambito dei rapporti con la nostra Societ&agrave; e/o con le altre societ&agrave; del Gruppo Unipol 2. La preghiamo di leggere con attenzione le informazioni di seguito riportate e di indicarci se ci consente o meno di utilizzare i Suoi dati per la predetta finalit&agrave;, nel rispetto delle vigenti normative in materia di protezione dei dati personali e di commercializzazione a distanza di prodotti e servizi assicurativi. PERCH&Eacute; LE CHIEDIAMO I DATI Ove Lei acconsenta i Suoi dati potranno essere trattati per la&nbsp;finalit&agrave; di seguito specificata: ContattarLa, mediante tecniche di comunicazione a distanza comprensive di modalit&agrave; automatizzate di contatto (come SMS, MMS, fax, chiamate telefoniche automatizzate, posta elettronica, messaggi su applicazioni web) e tradizionali (come posta cartacea e chiamate telefoniche con operatore) per inviarLe comunicazioni commerciali relative a prodotti o servizi di UnipolSai, delle societ&agrave; facenti parte del Gruppo Unipol e di terzi (in particolare, newsletter e materiale promozionale e pubblicitario), ovvero che risultino generalmente in linea con le preferenze di soggetti appartenenti alla Sua fascia di et&agrave;, o per i quali, anche accedendo alle aree web cui potr&agrave; essere da noi invitato, ha manifestato un Suo interesse all&rsquo;interno delle stesse. Potremo altres&igrave; effettuare comunicazioni al fine di avvisarLa, in prossimit&agrave; della successiva scadenza annuale della polizza, della possibilit&agrave; di richiedere un nuovo preventivo personalizzato, recuperando alcuni dati relativi al preventivo in precedenza effettuato. In questi casi, potremo invitarLa ad esprimere le Sue preferenze circa il numero e la frequenza di detti richiami. Il rilascio del Suo consenso ci permetter&agrave;, inoltre, di effettuare attivit&agrave; di vendita diretta e ricerche di mercato, incluse indagini sulla qualit&agrave; e soddisfazione dei servizi Resta inoltre ferma la possibilit&agrave; per il suo Agente assicurativo di riferimento (nel seguito, l&rsquo;&rdquo;Agente&rdquo;) di trattare i Suoi dati anche per ulteriori, autonome finalit&agrave; commerciali e di eventuale profilazione in relazione a prodotti e servizi dell&rsquo;Agente e/o di terzi, finalit&agrave; per le quali ricever&agrave; dal medesimo Agente, quale titolare di tale distinto trattamento, una separata, apposita informativa e richiesta di consenso (a cui si rinvia per ogni dettaglio). QUALI DATI RACCOGLIAMO Per la predetta finalit&agrave; (punto 1), saranno raccolti e trattati i dati personali, con esclusione di quelli appartenenti a categorie particolari1, da Lei forniti nell&#39;ambito dei Suoi rapporti relativi ai servizi e prodotti sia di UnipolSai. Tra i predetti dati, oltre ai Suoi dati identificativi, potranno essere inclusi, a seconda degli strumenti e dei servizi messi a Sua disposizione da UnipolSai e/o con i quali Lei decider&agrave; eventualmente di interagire: i dati relativi al Suo indirizzo di residenza e/o di domicilio, i Suoi recapiti telefonici (fisso e mobile) e telematici (indirizzo di posta elettronica); i dati relativi ai rapporti contrattuali (assicurativi e non) e ai servizi da Lei richiesti o in Suo favore prestati (come, ad esempio, i dati acquisti anche in occasione della liquidazione di sinistri eventualmente occorsi, tra cui importi liquidati, coordinate bancarie, ecc.); i dati da Lei rilasciati od eventualmente acquisiti, nell&rsquo;ambito dei rapporti, anche da registri, banche dati e fonti pubbliche conoscibili o pubblicamente accessibili da chiunque (come, ad esempio, i dati relativi a veicoli e a immobili a Lei intestati); i dati riferiti ad aspetti della Sua situazione personale e familiare, nonch&eacute; professionale ed economica (quali, ad esempio, stato civile, composizione nucleo familiare, titolo di studio, posizione lavorativa, reddito, interessi, ecc. ) da Lei comunicatici, tra cui quelli forniti nell&rsquo;ambito di indagini di natura commerciale, di questionari commerciali o di valutazione dell&rsquo;adeguatezza di prodotti assicurativi e previdenziali, in occasione di consulenze personalizzate svolte da UnipolSai e da Sue agenzie, ecc.; i dati rilasciati in occasione dell&rsquo;effettuazione di preventivi online sui sito web UnipolSai.it o in occasione della Sua partecipazione a concorsi promossi da UnipolSai sul proprio sito web o su siti di aziende co-promotrici dei concorso; i dati relativi all&rsquo;eventuale registrazione a nostri servizi on line, anche tramite installazione di nostre App; i dati connessi al Suo profilo od account su social network (quali, ad es., Facebook, Twitter, ecc.), qualora Lei si sia registrato ai nostri servizi online mediante il Suo profilo od account &ldquo;social&rdquo; e abbia autorizzato il gestore del social network a condividere tali dati con terzi. Il conferimento dei Suoi dati e il Suo consenso per le finalit&agrave; sopra indicate sono facoltativi e il loro mancato rilascio non incide sui rapporti e servizi in essere. Tenga presente che tali dati ci permetteranno di migliorare la qualit&agrave; dei nostri servizi, di contattarLa pi&ugrave; agevolmente, nonch&eacute; di inviarle oltre a comunicazioni di servizio (relative, ad esempio, a eventuali sinistri o ad avvisi di scadenza di una polizza), aggiornamenti e promozioni riguardanti nuovi contratti o prodotti di Suo possibile interesse. Ribadiamo che, in caso di mancato rilascio del Suo consenso, i Suoi dati non saranno utilizzati per la finalit&agrave; di cui al punto 1. COME TRATTIAMO I SUOI DATI I Suoi dati non saranno da noi diffusi, saranno gestiti anche con strumenti elettronici, con apposite procedure informatizzate e logiche di elaborazione dei dati (per aree geografiche, tipologia di clienti, contratti o servizi, ecc.) e potranno essere conosciuti, all&#39;interno delle nostre strutture, da personale a ci&ograve; incaricato, nonch&eacute; dalla nostra rete di intermediari assicurativi, quali Responsabili del trattamento, e da incaricati di societ&agrave; di nostra fiducia che svolgono, per nostro conto, alcune operazioni tecniche ed organizzative strettamente necessarie per lo svolgimento delle suddette attivit&agrave;, nonch&eacute; da societ&agrave; specializzate in servizi di informazione e promozione commerciale, ricerche di mercato ed indagini sulla qualit&agrave; dei servizi e sulla soddisfazione dei clienti (societ&agrave; che opereranno in qualit&agrave; di Responsabili del trattamento). I Suoi dati saranno custoditi nel pieno rispetto delle misure di sicurezza previste dalla normativa privacy e saranno conservati per due (2) anni, termini decorrenti dalla cessazione dei rapporti con UnipolSai ovvero, in assenza di rapporti con la predetta Compagnia, dal rilascio del consenso; decorsi tali termini non saranno pi&ugrave; utilizzati per la predetta finalit&agrave;. QUALI SONO I SUOI DIRITTI La normativa privacy (artt. 15-22 del Regolamento) Le garantisce il diritto di richiedere i) l&#39;accesso ai dati che La riguardano, ii) la loro rettifica e/o l&#39;integrazione, se inesatti o incompleti, o iii) la loro cancellazione, ove trattati illecitamente, nonch&eacute;, nei casi previsti, iv) la limitazione o v) l&#39;opposizione al loro trattamento a fini di marketing diretto, ivi compresa la profilazione, e all&#39;invio di comunicazioni commerciali a distanza e con modalit&agrave; automatizzate. La normativa sulla privacy Le attribuisce altres&igrave; il diritto di richiedere vi) la portabilit&agrave; dei dati da Lei forniti, nei limiti di quanto indicato dall&#39;art. 20 del Regolamento. Titolare del trattamento dei Suoi dati &egrave; UnipolSai Assicurazioni S.p.A. (www.unipolsai.it) con sede in Via Stalingrado 45 - 40128 Bologna. Le ricordiamo che Lei ha altres&igrave; diritto di revocare in ogni momento il consenso eventualmente prestato; la revoca non pregiudica la liceit&agrave; del trattamento fino a quel momento svolto. Per revocare il Suo consenso, pu&ograve; <NAME_EMAIL>. Per ogni altro dubbio o chiarimento (e per conoscere le categorie di soggetti cui comunichiamo i dati ed i responsabili del trattamento), nonch&egrave; per l&#39;esercizio degli altri diritti (punti da i) a vi del presente paragrafo), pu&ograve; rivolgersi al &quot;Responsabile per la protezione dei dati&quot;: a tale scopo potr&agrave; contattarlo presso UnipolSai Assicurazioni S.p.A., <NAME_EMAIL>. Quanto al trattamento effettuato dal Suo Agente in qualit&agrave; di titolare autonomo (vedi punto 2) potr&agrave; esercitare i diritti riconosciuti dalla normativa privacy nei confronti dell&#39;Agente medesimo con le modalit&agrave; indicate nel separato modulo d&#39;informativa e consenso dallo stesso fornitoLe. Resta fermo il Suo diritto di rivolgersi al Garante della privacy, anche attraverso la presentazione di un reclamo, ove ritenuto necessario, per la tutela dei Suoi dati personali e dei Suoi diritti. 1Ai sensi dell&#39;art. 9 del Regolamento, per categorie particolari di dati si intendono i dati personali che rivelino l&#39;origine razziale o etnica, le opinioni politiche, le convinzioni religiose o filosofiche, o l&#39;appartenenza sindacale, nonch&eacute; trattare dati genetici, dati biometrici intesi a identificare in modo univoco una persona fisica, dati relativi alla salute o alla vita sessuale o all&#39;orientamento sessuale della persona. Ai sensi dell&#39;art 4 punto 1) del Regolamento dati personali (non appartenenti a categorie particolari) sono costituiti da qualunque informazione relativa a persona fisica, identificata o identificabile, anche indirettamente, mediante riferimento a un identificativo come il nome, un numero di identificazione, dati relativi all&#39;ubicazione o un identificativo online; 2Per l&#39;elenco completo e aggiornato di tutte le societ&agrave; facenti parte del Gruppo, rimandiamo al sito di Unipol Gruppo S.p.A. www.unipol.it</p>", "testID": "202404031252500661103"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore", "iconSource": "standardIcon", "iconStandard": "pxIcon"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Standard", "formatType": "text", "showReadOnlyValidation": "false", "iconSource": "standardIcon", "iconStandard": "pxIcon"}], "actionSets": [], "type": "pxIcon"}, "label": "Image", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateGeneric", "labelFormat": "Standard", "disabled": false, "testID": "202404031303050574619", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"onlyIcon": "true", "size": "S", "resource": "expand-privacy", "type": "icon"}, "showLabel": false}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore", "iconSource": "standardIcon", "iconStandard": "pxIcon"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Standard", "formatType": "text", "showReadOnlyValidation": "false", "iconSource": "standardIcon", "iconStandard": "pxIcon"}], "actionSets": [], "type": "pxIcon"}, "label": "Image", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateGeneric", "labelFormat": "Standard", "disabled": false, "testID": "202404031303050574404", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"onlyIcon": "true", "size": "S", "resource": "cross-privacy", "type": "icon"}, "showLabel": false}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}, {"view": {"reference": "<PERSON><PERSON><PERSON>", "validationMessages": "", "viewID": "ConsensoCommerciale", "visible": true, "titleFormat": "", "name": "ConsensoCommerciale", "appliesTo": "UG-Ins-PU-Data-Persona", "groups": [{"view": {"reference": "Contraente.<PERSON>sensi", "validationMessages": "", "viewID": "ConsensoCommerciale", "visible": true, "titleFormat": "", "name": "ConsensoCommerciale", "appliesTo": "UG-Ins-PU-Data-Consenso", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Contraente.Consensi.pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202404031311040395823", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "BoxPrivacy", "privacyLabelFormatAPP": "\"TEXT APP BDB16 WEB”", "privacyLabelAPP": "Leggi l'informativa", "paddingApp": "0 0 0 32"}}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "BoxPrivacyTitle STYLE TEXT APP BDM16 WEB BDB16 BDB16 BDB16", "testID": "202404031311040396447"}, "value": "Consenso per comunicazioni commerciali (facoltativo):", "testID": "202404031311040396447"}}, {"paragraph": {"visible": true, "appliesTo": "UG-Ins-PU-Data-Consenso", "paragraphID": "TestoConsensoCommerciale", "readOnly": true, "value": "<p data-pega-style=\"TEXT APP BDL16 WEB BDL16 BDL16 BDL16\">preso atto di quanto indicato nell&rsquo;informativa sopra riportata, dichiaro di acconsentire al trattamento dei miei dati personali con esclusione di quelli appartenenti a categorie particolari&nbsp;da parte di UnipolSai Assicurazioni S.p.A., per l&rsquo;effettuazione da parte della medesima societ&agrave; di comunicazioni commerciali relative a prodotti o servizi di UnipolSai stessa, delle societ&agrave; facenti parte del Gruppo Unipol&nbsp;e di societ&agrave; terze, in particolare, per invio di comunicazioni promozionali, newsletter e materiale pubblicitario, attivit&agrave; di vendita diretta e compimento di ricerche di mercato, incluse indagini sulla qualit&agrave; e soddisfazione dei servizi,&nbsp; mediante tecniche di comunicazione a distanza comprensive di modalit&agrave; automatizzate di contatto (come SMS, MMS, fax, chiamate telefoniche automatizzate, posta elettronica, messaggi su applicazioni web) e tradizionali (come posta cartacea e chiamate telefoniche con operatore).</p>", "testID": "202404031311040396681"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore", "iconSource": "standardIcon", "iconStandard": "pxIcon"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Standard", "formatType": "text", "showReadOnlyValidation": "false", "iconSource": "standardIcon", "iconStandard": "pxIcon"}], "actionSets": [], "type": "pxIcon"}, "label": "Image", "type": "Text", "required": false, "validateAs": "", "reference": "Contraente.Consensi.pyTemplateGeneric", "labelFormat": "Standard", "disabled": false, "testID": "202404031311040397967", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"onlyIcon": "true", "size": "S", "resource": "expand-privacy", "type": "icon"}, "showLabel": false}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore", "iconSource": "standardIcon", "iconStandard": "pxIcon"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Standard", "formatType": "text", "showReadOnlyValidation": "false", "iconSource": "standardIcon", "iconStandard": "pxIcon"}], "actionSets": [], "type": "pxIcon"}, "label": "Image", "type": "Text", "required": false, "validateAs": "", "reference": "Contraente.Consensi.pyTemplateGeneric", "labelFormat": "Standard", "disabled": false, "testID": "202404031311040397378", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"onlyIcon": "true", "size": "S", "resource": "cross-privacy", "type": "icon"}, "showLabel": false}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"textAlign": "Left", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "specifySize": "auto", "formatType": "text", "obfuscated": false, "captionPosition": "right", "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "formatType": "<PERSON><PERSON><PERSON>e", "obfuscated": false, "showReadOnlyValidation": "false", "trueLabel": "True", "falseLabel": "False", "trueImage": "", "falseImage": "", "showValueAs": "text"}], "actionSets": [{"actions": [{"action": "refresh", "refreshFor": "Q29uc2Vuc29Db21tZXJjaWFsZVVHLUlucy1QVS1EYXRhLUNvbnNlbnNvLkNvbXVuaWNhemlvbmlDb21tZXJjaWFsaTIwMjQwNDAzMTMxMTA0MDM5ODU4Nw%3D%3D"}], "events": [{"event": "change"}]}], "type": "pxCheckbox", "label": ""}, "label": "Checkbox", "type": "True-False", "required": false, "validateAs": "", "reference": "Contraente.Consensi.ComunicazioniCommerciali", "labelFormat": "Standard", "disabled": false, "testID": "202404031311040398587", "value": "false", "maxLength": 0, "expectedLength": "", "fieldID": "ComunicazioniCommerciali", "customAttributes": {"checkboxLabel": "<PERSON><PERSON><PERSON><PERSON>", "checkBoxFormat": "TEXT APP BDL16 WEB"}, "showLabel": false}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}], "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Full Positive Button", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "finishAssignment"}], "events": [{"event": "click"}]}], "type": "px<PERSON><PERSON><PERSON>", "label": "Prosegui"}, "label": "<PERSON><PERSON>", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateButton", "labelFormat": "Standard", "disabled": false, "testID": "202212131813470289404", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"submitButton": "true"}, "showLabel": false}}], "groupFormat": "COL TC 20 24 32", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TC 16 24 16 MW496", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 20 32 20 38 D 0 32 T 0 32 M 16 24", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "P 0 D 0 24 T 0 24 M 16 24", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"view": {"reference": "", "validationMessages": "", "viewID": "MarketingCard", "visible": true, "titleFormat": "", "name": "MarketingCard", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202304261759240176561", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "PUBoxReasonWhy", "productType": "AUTO"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 20 32 20 38 D 0 32 T 0 32 M 16 24", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TC 16 24 16 MW403", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}], "groupFormat": "Responsive 2col", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TC 0 0 MW1062", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}, "actionID": "RaccoltaDatiContatto", "caseID": "UG-INS-PU-WORK-QUOTAZIONE Q-75048", "name": "Raccolta Dati Contatto"}, "metaBodyResponse": {"assignmentId": "ASSIGN-WORKLIST UG-INS-PU-WORK-QUOTAZIONE Q-75048!RACCOLTADATIBENE", "actionId": "RaccoltaDatiContatto", "caseId": "UG-INS-PU-WORK-QUOTAZIONE Q-75048"}, "analyticsBodyResponse": {}}