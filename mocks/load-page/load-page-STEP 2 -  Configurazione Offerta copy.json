{"pegaBodyResponse": {"view": {"reference": "", "validationMessages": "", "viewID": "ConfigurazioneOfferta", "visible": true, "titleFormat": "", "name": "ConfigurazioneOfferta", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"view": {"reference": "", "validationMessages": "", "viewID": "AnalyticsHidden", "visible": true, "titleFormat": "", "name": "AnalyticsHidden", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202212131640340513751", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "analyticsPageFields", "funnel_name": "Prodotto Unico", "page_name": "unico:show price"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "20230526173946071777", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"vehicle_total": "^", "vehicle_convention": "", "vehicle_unibox_type": "", "vehicle_applicant_email": "<EMAIL>", "vehicle_plate": "GG810CV", "vehicle_private_area_id": "4a9d1617-edab-4ee5-9888-f1d51cd1707e", "vehicle_applicant_birthdate": "19560618", "vehicle_applicant_phone": "3485269605", "vehicle_type": "1", "vehicle_position_id": "0337c11a-128b-47c7-9fd0-2eb074579497", "vehicle_applicant_fiscal_code": "", "vehicle_need_area": "AUTO"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"view": {"reference": "", "validationMessages": "", "viewID": "Intestazione", "visible": true, "titleFormat": "", "name": "Intestazione", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202212131640340513751", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "Header", "background": "Secondary"}}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "Standard", "testID": "202301131218420210633"}, "value": "La tua offerta", "testID": "202301131218420210633"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore", "iconSource": "image", "iconImage": "webwb/pymenuleftarrow.png"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Standard", "formatType": "text", "showReadOnlyValidation": "false", "iconSource": "image", "iconImage": "webwb/pymenuleftarrow.png"}], "actionSets": [{"actions": [{"action": "takeAction", "actionProcess": {"actionName": "Indietro"}}], "events": [{"event": "click"}]}], "type": "pxIcon"}, "label": "Image", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateGeneric", "labelFormat": "Standard", "disabled": false, "testID": "202212141106470503509", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"componentID": "_back", "size": "S", "resource": "arrowLeft", "type": "icon"}, "showLabel": false}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"view": {"reference": "", "validationMessages": "", "viewID": "BarraDiAvanzamento", "visible": true, "titleFormat": "", "name": "BarraDiAvanzamento", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202212131640340513751", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"currentStep": "1", "labelFormatDeselected": "TEXT APP GRL13 WEB GRL14 GRL14 GRL14", "customType": "Stepper", "labelFormatPreviousStep": "TEXT APP BDL13 WEB BDL14 BDL14 BDL14", "progress": "40", "style": "StepperPU", "labelFormatCurrentStep": "TEXT APP BDM13 WEB BDM14 BDM14 BDM14", "labelsStepper": "Preventivo|Carrello|Acquisto"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP GDL16 WEB BDL18 BDL16 BDL16", "testID": "202303151302500321629"}, "value": "<PERSON><PERSON><PERSON> un'offerta da personalizzare.", "testID": "202303151302500321629"}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP GDL16 WEB BDL18 BDL16 BDL16", "testID": "202303311234290888976"}, "value": "L'abbiamo calcolata con", "testID": "202303311234290888976"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore", "linkType": "none", "linkData": "", "linkImageSource": "none", "linkImagePosition": "left"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "pzhc", "formatType": "text", "showReadOnlyValidation": "false", "linkType": "none", "linkData": "", "linkImageSource": "none", "linkImagePosition": "left"}], "actionSets": [{"actions": [{"action": "localAction", "actionProcess": {"localAction": "PopupVediDati", "localActionFormat": "Standard", "customTemplate": "pzModalTemplate", "target": "modalDialog"}}], "events": [{"event": "click"}]}], "type": "pxLink", "label": "questi dati"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateButton", "labelFormat": "TEXT APP BDLN16 WEB BDMN18 BDMN16 BDMN16", "disabled": false, "testID": "202301131812090219386", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "showLabel": true}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton"}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton"}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton"}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton"}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton"}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton"}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton"}}], "groupFormat": "Mimic a sentence center web", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"view": {"reference": "Ambito", "validationMessages": "", "viewID": "WrapperPackages", "visible": true, "titleFormat": "", "name": "WrapperPackages", "appliesTo": "UG-Ins-PU-Data-Ambito", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "DEFAULT", "groupFormat": "Dynamic", "layoutFormat": "REPEATINGLAYOUT", "title": "", "reference": "Ambito<PERSON>", "sourceType": "Property", "fieldListID": "<PERSON><PERSON>", "referenceType": "List", "newRow": {"groups": []}, "rows": [{"groups": [{"view": {"reference": "Ambito<PERSON>(1)", "validationMessages": "", "viewID": "MostraDettagliPackages", "visible": true, "titleFormat": "", "name": "MostraDettagliPackages", "appliesTo": "UG-Ins-PU-Data-Pacchetto", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "Repeating dynamic layout highlight", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito.Pacchetti(1).pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202301181230530973429", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "Assurance Package", "assurancePackageHeader": "MAGGIORE PROTEZIONE"}}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"textAlign": "Left", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "specifySize": "auto", "formatType": "text", "obfuscated": false, "captionPosition": "right", "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "formatType": "<PERSON><PERSON><PERSON>e", "obfuscated": false, "showReadOnlyValidation": "false", "trueLabel": "True", "falseLabel": "False", "trueImage": "webwb/pzCheckDone.svg", "falseImage": "", "showValueAs": "image"}], "actionSets": [{"actions": [{"action": "refresh", "refreshFor": "TW9zdHJhRGV0dGFnbGlQYWNrYWdlc1VHLUlucy1QVS1EYXRhLVBhY2NoZXR0by5TZWxlemlvbmF0bzIwMjMwMzMxMTQyMzI0MDQxNzgwNg%3D%3D_1"}], "events": [{"event": "click"}]}], "type": "pxCheckbox", "label": ""}, "label": "Checkbox", "type": "True-False", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(1).Selezionato", "labelFormat": "Standard", "disabled": false, "testID": "202303311423240417806", "value": "false", "maxLength": 0, "expectedLength": "", "fieldID": "Selezionato", "customAttributes": {"ComponentPurpose": "CheckPackage"}, "showLabel": false}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "Nome", "control": {"format": "PackageName", "testID": "20230118123053097391"}, "value": "Premium", "testID": "20230118123053097391"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": true, "control": {"modes": [{"textAlign": "Right", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "specifySize": "auto", "formatType": "number", "maxChars": ""}, {"textAlign": "Right", "tooltip": "", "modeType": "readOnly", "formatType": "number", "obfuscated": false, "showReadOnlyValidation": "false", "symbolValue": "€"}], "actionSets": [], "type": "pxC<PERSON>rency"}, "label": "", "type": "Decimal", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(1).RataPremioLordoScontato", "labelFormat": "PackageInstallmentsPrice", "disabled": false, "testID": "202303311423240418147", "value": "663.52", "maxLength": 0, "expectedLength": "", "fieldID": "RataPremioLordoScontato"}}, {"caption": {"columnImportance": "", "visible": false, "captionFor": "", "control": {"format": "PackageInstallmentsPeriod", "testID": "202303311423240419263"}, "value": "al mese", "testID": "202303311423240419263"}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": true, "control": {"modes": [{"textAlign": "Right", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "specifySize": "auto", "formatType": "number", "maxChars": ""}, {"textAlign": "Right", "tooltip": "", "modeType": "readOnly", "formatType": "number", "obfuscated": false, "showReadOnlyValidation": "false", "symbolValue": "€"}], "actionSets": [], "type": "pxC<PERSON>rency"}, "label": "", "type": "Decimal", "required": false, "validateAs": "", "reference": "Ambito.<PERSON><PERSON><PERSON>(1).PremioLordo", "labelFormat": "TEXT APP WHLS12 WEB WHBS16 WHBS13 WHBS13", "disabled": false, "testID": "20230405170355001226", "value": "962.62", "maxLength": -1, "expectedLength": "", "fieldID": "PremioLordo", "customAttributes": {"formatLabel": "PackagePaymentDisclaimer"}}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": true, "control": {"modes": [{"textAlign": "Right", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "specifySize": "auto", "formatType": "number", "maxChars": ""}, {"textAlign": "Right", "tooltip": "", "modeType": "readOnly", "formatType": "number", "obfuscated": false, "showReadOnlyValidation": "false", "symbolValue": "€"}], "actionSets": [], "type": "pxC<PERSON>rency"}, "label": "", "type": "Decimal", "required": false, "validateAs": "", "reference": "Ambito.<PERSON><PERSON>ti(1).PremioLordoScontato", "labelFormat": "TEXT APP WHB13 WEB WHB16 WHB13 WHB13", "disabled": false, "testID": "202304051703550010313", "value": "663.52", "maxLength": 0, "expectedLength": "", "fieldID": "PremioLordoScontato"}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP WHL13 WEB WHL16 WHL13 WHL13", "testID": "202303311452090090754"}, "value": "all'anno", "testID": "202303311452090090754"}}], "groupFormat": "Mimic a sentence", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP BDB14 WEB BDB18 BDB16 BDB14", "testID": "202404291219180271370"}, "value": "Sconto", "testID": "202404291219180271370"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": true, "control": {"modes": [{"textAlign": "Right", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "specifySize": "auto", "formatType": "number", "maxChars": ""}, {"textAlign": "Right", "tooltip": "", "modeType": "readOnly", "formatType": "number", "obfuscated": false, "showReadOnlyValidation": "false", "symbolValue": "%"}], "actionSets": [], "type": "pxInteger"}, "label": "", "type": "Integer", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(1).PercentualeSconto", "labelFormat": "TEXT APP BDB14 WEB BDB18 BDB16 BDB14", "disabled": false, "testID": "20230403153236045814", "value": "32", "maxLength": 0, "expectedLength": "", "fieldID": "PercentualeSconto"}}], "groupFormat": "AssurancePackageRibbon", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Row CT 24 24 16 14", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}]}, {"groups": [{"view": {"reference": "Ambito<PERSON>(2)", "validationMessages": "", "viewID": "MostraDettagliPackages", "visible": true, "titleFormat": "", "name": "MostraDettagliPackages", "appliesTo": "UG-Ins-PU-Data-Pacchetto", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "Repeating dynamic layout highlight", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202301181230530973429", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "Assurance Package", "assurancePackageHeader": ""}}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": true, "control": {"modes": [{"textAlign": "Left", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "specifySize": "auto", "formatType": "text", "obfuscated": false, "captionPosition": "right", "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "formatType": "<PERSON><PERSON><PERSON>e", "obfuscated": false, "showReadOnlyValidation": "false", "trueLabel": "True", "falseLabel": "False", "trueImage": "webwb/pzCheckDone.svg", "falseImage": "", "showValueAs": "image"}], "actionSets": [{"actions": [{"action": "refresh", "refreshFor": "TW9zdHJhRGV0dGFnbGlQYWNrYWdlc1VHLUlucy1QVS1EYXRhLVBhY2NoZXR0by5TZWxlemlvbmF0bzIwMjMwMzMxMTQyMzI0MDQxNzgwNg%3D%3D_2"}], "events": [{"event": "click"}]}], "type": "pxCheckbox", "label": ""}, "label": "Checkbox", "type": "True-False", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Selezionato", "labelFormat": "Standard", "disabled": false, "testID": "202303311423240417806", "value": "true", "maxLength": 0, "expectedLength": "", "fieldID": "Selezionato", "customAttributes": {"ComponentPurpose": "CheckPackage"}, "showLabel": false}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "Nome", "control": {"format": "PackageName", "testID": "20230118123053097391"}, "value": "Start", "testID": "20230118123053097391"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": true, "control": {"modes": [{"textAlign": "Right", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "specifySize": "auto", "formatType": "number", "maxChars": ""}, {"textAlign": "Right", "tooltip": "", "modeType": "readOnly", "formatType": "number", "obfuscated": false, "showReadOnlyValidation": "false", "symbolValue": "€"}], "actionSets": [], "type": "pxC<PERSON>rency"}, "label": "", "type": "Decimal", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).RataPremioLordoScontato", "labelFormat": "PackageInstallmentsPrice", "disabled": false, "testID": "202303311423240418147", "value": "601.69", "maxLength": 0, "expectedLength": "", "fieldID": "RataPremioLordoScontato"}}, {"caption": {"columnImportance": "", "visible": false, "captionFor": "", "control": {"format": "PackageInstallmentsPeriod", "testID": "202303311423240419263"}, "value": "al mese", "testID": "202303311423240419263"}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": true, "control": {"modes": [{"textAlign": "Right", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "specifySize": "auto", "formatType": "number", "maxChars": ""}, {"textAlign": "Right", "tooltip": "", "modeType": "readOnly", "formatType": "number", "obfuscated": false, "showReadOnlyValidation": "false", "symbolValue": "€"}], "actionSets": [], "type": "pxC<PERSON>rency"}, "label": "", "type": "Decimal", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).PremioLordo", "labelFormat": "TEXT APP WHLS12 WEB WHBS16 WHBS13 WHBS13", "disabled": false, "testID": "20230405170355001226", "value": "899.52", "maxLength": -1, "expectedLength": "", "fieldID": "PremioLordo", "customAttributes": {"formatLabel": "PackagePaymentDisclaimer"}}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": true, "control": {"modes": [{"textAlign": "Right", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "specifySize": "auto", "formatType": "number", "maxChars": ""}, {"textAlign": "Right", "tooltip": "", "modeType": "readOnly", "formatType": "number", "obfuscated": false, "showReadOnlyValidation": "false", "symbolValue": "€"}], "actionSets": [], "type": "pxC<PERSON>rency"}, "label": "", "type": "Decimal", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).PremioLordoScontato", "labelFormat": "TEXT APP WHB13 WEB WHB16 WHB13 WHB13", "disabled": false, "testID": "202304051703550010313", "value": "601.69", "maxLength": 0, "expectedLength": "", "fieldID": "PremioLordoScontato"}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP WHL13 WEB WHL16 WHL13 WHL13", "testID": "202303311452090090754"}, "value": "all'anno", "testID": "202303311452090090754"}}], "groupFormat": "Mimic a sentence", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP BDB14 WEB BDB18 BDB16 BDB14", "testID": "202404291219180271370"}, "value": "Sconto", "testID": "202404291219180271370"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": true, "control": {"modes": [{"textAlign": "Right", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "specifySize": "auto", "formatType": "number", "maxChars": ""}, {"textAlign": "Right", "tooltip": "", "modeType": "readOnly", "formatType": "number", "obfuscated": false, "showReadOnlyValidation": "false", "symbolValue": "%"}], "actionSets": [], "type": "pxInteger"}, "label": "", "type": "Integer", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).PercentualeSconto", "labelFormat": "TEXT APP BDB14 WEB BDB18 BDB16 BDB14", "disabled": false, "testID": "20230403153236045814", "value": "34", "maxLength": 0, "expectedLength": "", "fieldID": "PercentualeSconto"}}], "groupFormat": "AssurancePackageRibbon", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Row CT 24 24 16 14", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}]}], "repeatLayoutFormat": "Row CB 24 24 16 14", "repeatContainerFormat": "DEFAULT", "testID": "202301181231050302942"}}], "title": ""}}], "groupFormat": "Col TL 24 36 24 32", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 20 24 20 0 D 0 32 T 24 M 16 24 16 28", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP BDB16 WEB BDB24C BDB16C BDM16C", "testID": "202304201115020140965"}, "value": "Cosa include", "testID": "202304201115020140965"}}], "groupFormat": "Row CC CC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 0 0 0 16 D 0 28 T 0 24 M 0 24", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"view": {"reference": "Ambito", "validationMessages": "", "viewID": "PropostaOfferta", "visible": true, "titleFormat": "", "name": "PropostaOfferta", "appliesTo": "UG-Ins-PU-Data-Ambito", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groupFormat": "Dynamic", "layoutFormat": "REPEATINGLAYOUT", "title": "", "reference": "Ambito<PERSON>", "sourceType": "Property", "fieldListID": "<PERSON><PERSON>", "referenceType": "List", "newRow": {"groups": []}, "rows": [{"groups": [{"view": {"viewID": "WrapperSezioni", "visible": false, "name": "WrapperSezioni"}}]}, {"groups": [{"view": {"reference": "Ambito<PERSON>(2)", "validationMessages": "", "viewID": "WrapperSezioni", "visible": true, "titleFormat": "h2", "name": "WrapperSezioni", "appliesTo": "UG-Ins-PU-Data-Pacchetto", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groupFormat": "Dynamic", "layoutFormat": "REPEATINGLAYOUT", "title": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Sezioni", "sourceType": "Property", "fieldListID": ".Sezioni", "referenceType": "List", "newRow": {"groups": []}, "rows": [{"groups": [{"view": {"reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Sezioni(1)", "validationMessages": "", "viewID": "ListaSezioni", "visible": true, "titleFormat": "", "name": "ListaSezioni", "appliesTo": "UG-Ins-PU-Data-Sezione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "True-False", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Se<PERSON>ni(1).VediEModifica", "labelFormat": "Standard", "disabled": false, "testID": "202304171734180604289", "value": "false", "maxLength": 0, "expectedLength": "", "fieldID": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Sezioni(1).pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202302061752470181864", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "CardSezione", "selected": "true"}}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "ShowPriceGaranzie", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore", "iconSource": "standardIcon", "iconStandard": "pxIcon"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Standard", "formatType": "text", "showReadOnlyValidation": "false", "iconSource": "standardIcon", "iconStandard": "pxIcon"}], "actionSets": [], "type": "pxIcon"}, "label": "Image", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Sezioni(1).pyTemplateGeneric", "labelFormat": "Standard", "disabled": false, "testID": "202302011610530026778", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"size": "M", "resource": "sezioneRcAuto", "webResponsiveSize": "40L 48L 40C", "type": "icon"}, "showLabel": false}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "Descrizione", "control": {"format": "TEXT APP BDB16 WEB BDB20 BDB20 BDB16C", "testID": "202301181205410449672"}, "value": "RCA: PROTEGGI LA TUA GUIDA", "testID": "202301181205410449672"}}, {"paragraph": {"visible": true, "appliesTo": "UG-Ins-PU-Data-Sezione", "paragraphID": "TestoSezione", "readOnly": true, "value": "<p data-pega-style=\"TEXT APP GDL16C WEB BDL18 BDL18 BDL18\">Garanzia necessaria per circolare con il tuo veicolo.</p>", "testID": "202302061214250995217"}}], "groupFormat": "Col TC 8 4 4 8", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Sezioni(1).pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202212131640340513751", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "Separator", "style": "CardSezione", "paddingDesktop": "0 16 0 12", "paddingTablet": "0 16 0 12", "paddingApp": "0", "paddingMobile": "0 8 0 12"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "Row divider", "groupFormat": "Dynamic", "layoutFormat": "REPEATINGLAYOUT", "title": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Se<PERSON>ni(1).G<PERSON><PERSON>", "sourceType": "Property", "fieldListID": "<PERSON>", "referenceType": "List", "newRow": {"groups": []}, "rows": [{"groups": [{"view": {"reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Sezioni(1).G<PERSON><PERSON>(1)", "validationMessages": "", "viewID": "MostraGaranzia", "visible": true, "titleFormat": "h2", "name": "MostraGaranzia", "appliesTo": "UG-Ins-PU-Data-Garanzia", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP BDM16 WEB BDM20 BDM20 BDL16", "testID": "202302061214250997768"}, "value": "Responsabilità Civile Auto", "testID": "202302061214250997768"}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": true, "control": {"modes": [{"textAlign": "Right", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "specifySize": "auto", "formatType": "number", "maxChars": ""}, {"textAlign": "Left", "tooltip": "", "modeType": "readOnly", "formatType": "number", "obfuscated": false, "showReadOnlyValidation": "false", "symbolValue": "€"}], "actionSets": [], "type": "pxC<PERSON>rency"}, "label": "", "type": "Decimal", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Sezioni(1).<PERSON><PERSON><PERSON>(1).RataPremioLordoScontato", "labelFormat": "TEXT APP BDB18 WEB BDB18 BDB18 BDB18", "disabled": false, "testID": "202302061214250999346", "value": "601.69", "maxLength": 0, "expectedLength": "", "fieldID": "RataPremioLordoScontato"}}], "groupFormat": "ROW RT 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "ROW RT 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TL 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Row SBT SBT 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 0 24 0 12 D 0 12 0 0 T 0 12 0 0 M 0 12 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groupFormat": "Dynamic", "layoutFormat": "REPEATINGLAYOUT", "title": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Sezioni(1).G<PERSON><PERSON>(1).ListaAttributi", "sourceType": "Property", "fieldListID": ".ListaAttributi", "referenceType": "List", "newRow": {"groups": []}, "rows": [{"groups": [{"view": {"reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Sezioni(1).G<PERSON><PERSON>(1).ListaAttributi(1)", "validationMessages": "", "viewID": "MostraAttributi", "visible": true, "titleFormat": "h2", "name": "MostraAttributi", "appliesTo": "UG-Ins-PU-Data-AttributiGaranzia", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "DEFAULT", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "DescrizioneAttributo", "control": {"format": "TEXT APP BDL13 WEB BDL13 BDL13 BDL13", "testID": "20230206130825052352"}, "value": "<PERSON><PERSON><PERSON>", "testID": "20230206130825052352"}}], "groupFormat": "Mimic a sentence no space", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": true, "control": {"modes": [{"dataPagePrompt": "label", "groupOrder": "asc", "listSource": "datapage", "textAlign": "Left", "tooltip": "", "enableGrouping": false, "groupBy": "", "minChars": "", "dataPageParams": [{"valueReference": {"reference": "pyWorkPage.Ambito.CodiceProdottoWPT", "lastSavedValue": "PUAUTO"}, "name": "<PERSON><PERSON><PERSON>"}, {"valueReference": {"reference": ".CodiceSezione", "lastSavedValue": "PUSRCA"}, "name": "Sezione"}, {"valueReference": {"reference": ".Codice<PERSON>", "lastSavedValue": "PUPRCA"}, "name": "Garanzia"}, {"valueReference": {"reference": ".CodiceAttributo", "lastSavedValue": "MASSIMALE"}, "name": "Attributo"}, {"valueReference": {"reference": "pyWorkPage.Ambito.Bene.DescrizioneBeneWPT", "lastSavedValue": "1"}, "name": "<PERSON><PERSON>"}], "modeType": "editable", "dataPageValue": "Valore", "controlFormat": "TEXT APP BDB13 WEB BDB13 BDB13 BDB13", "dataPageTooltip": "", "loadMode": "auto", "options": [{"value": "7.750.000,00/ 6.450.000,00/  1.300.000,00", "key": "747"}, {"value": "12.000.000,00/ 10.000.000,00/  2.000.000,00", "key": "610"}, {"value": "25.000.000,00/ 20.000.000,00/  5.000.000,00", "key": "613"}, {"value": "50.000.000,00/ 40.000.000,00/ 10.000.000,00", "key": "616"}, {"value": "75.000.000,00/ 60.000.000,00/ 15.000.000,00", "key": "667"}, {"value": "77.500.000,00/ 62.000.000,00/ 15.500.000,00", "key": "675"}, {"value": "100.000.000,00/ 80.000.000,00/ 20.000.000,00", "key": "677"}], "dataPageID": "D_RecuperaMassimale", "specifySize": "auto", "formatType": "none", "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "formatType": "none", "showReadOnlyValidation": "false"}], "actionSets": [], "type": "pxDropdown"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Sezioni(1).G<PERSON><PERSON>(1).ListaAttributi(1).Valore", "labelFormat": "TEXT APP BDB13 WEB BDB13 BDB13 BDB13", "disabled": false, "testID": "202302061308250524733", "value": "747", "maxLength": 0, "expectedLength": "", "fieldID": "Valore", "customAttributes": {"showLikeLabel": "true"}}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "Valore"}}], "groupFormat": "Mimic a sentence", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 0 0 0 12 D 0 8 0 0 T 0 8 0 0 M 0 12 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}]}], "repeatLayoutFormat": "<PERSON><PERSON><PERSON>", "repeatContainerFormat": "NOHEADER", "testID": "202302061308320217988"}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP GDL13 WEB BDL16 BDL16 BDL13C", "testID": "202302061754220267644"}, "value": "Aggiungi una protezione extra", "testID": "202302061754220267644"}}], "groupFormat": "Mimic a sentence", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 0 0 0 24 D 0 T 0 M 0 0 0 40", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}]}], "repeatLayoutFormat": "<PERSON><PERSON><PERSON>", "repeatContainerFormat": "Row divider", "testID": "202302061309010856865"}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "SimpleTPD", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "runDataTransform"}, {"action": "runDataTransform"}, {"action": "finishAssignment"}], "events": [{"event": "click"}]}], "type": "px<PERSON><PERSON><PERSON>", "label": "Vedi e modifica"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Sezioni(1).pyTemplateButton", "labelFormat": "TEXT APP BDBN16 WEB BDBN18 BDBN18 BDBN18R", "disabled": false, "testID": "202301181229170356306", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"VediEModifica": "true", "GestioneProcesso.StepSuccessivo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "showLabel": true}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"VediEModifica": "true", "GestioneProcesso.StepSuccessivo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"VediEModifica": "true", "GestioneProcesso.StepSuccessivo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shouldCenterButton": "true"}}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"VediEModifica": "true", "GestioneProcesso.StepSuccessivo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shouldCenterButton": "true"}}}], "groupFormat": "ROW RT 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Responsive 2 col SB", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 0 D 0 24 0 0 T 0 24 0 0 M 0 24 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 24 D 24 T 24 M 24", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "ShowPriceGaranzieNonSelezionato", "groups": [], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "COL TL 24 0 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}]}, {"groups": [{"view": {"reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Se<PERSON>ni(2)", "validationMessages": "", "viewID": "ListaSezioni", "visible": true, "titleFormat": "", "name": "ListaSezioni", "appliesTo": "UG-Ins-PU-Data-Sezione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "True-False", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Se<PERSON>ni(2).VediEModifica", "labelFormat": "Standard", "disabled": false, "testID": "202304171734180604289", "value": "false", "maxLength": 0, "expectedLength": "", "fieldID": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "ShowPriceGaranzieNonSelezionato", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Sezioni(2).pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202302061752470181864", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "CardSezione", "deselectedText": "OPPORTUNITÀ PER TE!", "selected": "false"}}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore", "iconSource": "standardIcon", "iconStandard": "pxIcon"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Standard", "formatType": "text", "showReadOnlyValidation": "false", "iconSource": "standardIcon", "iconStandard": "pxIcon"}], "actionSets": [], "type": "pxIcon"}, "label": "Image", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Se<PERSON>ni(2).pyTemplateGeneric", "labelFormat": "Standard", "disabled": false, "testID": "202302011610530026778", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"size": "M", "resource": "sezioneTecnologia", "webResponsiveSize": "40L 48L 40C", "type": "icon"}, "showLabel": false}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "Descrizione", "control": {"format": "TEXT APP BDB16 WEB BDB20 BDB20 BDB16C", "testID": "202301181205410449672"}, "value": "SERVIZI DI SICUREZZA", "testID": "202301181205410449672"}}, {"paragraph": {"visible": true, "appliesTo": "UG-Ins-PU-Data-Sezione", "paragraphID": "TestoSezione", "readOnly": true, "value": "<p data-pega-style=\"TEXT APP BDL16C WEB BDL18 BDL18 BDL18\">La tua proposta che aumenta la tua sicurezza mediante una soluzione tecnologica.</p>", "testID": "202302061214250995217"}}], "groupFormat": "Col TC 8 4 4 8", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Sezioni(2).pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202212131640340513751", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "Separator", "style": "CardSezione", "paddingDesktop": "0 16 0 24", "paddingTablet": "0 16 0 24", "paddingApp": "0", "paddingMobile": "0 8 0 24"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"VediEModifica": "true", "GestioneProcesso.StepSuccessivo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paddingApp": "0 20 0 0"}}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Cancel button", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "runDataTransform"}, {"action": "runDataTransform"}, {"action": "finishAssignment"}], "events": [{"event": "click"}]}], "type": "px<PERSON><PERSON><PERSON>", "label": "Aggiungi protezione"}, "label": "<PERSON><PERSON>", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Sezioni(2).pyTemplateButton", "labelFormat": "Standard", "disabled": false, "testID": "202303201206030373403", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"VediEModifica": "true", "GestioneProcesso.StepSuccessivo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paddingApp": "0 20 0 0"}, "showLabel": false}}], "groupFormat": "Row CC RC RC CC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 24 D 24 T 24 M 24", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "COL TL 24 0 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}]}, {"groups": [{"view": {"reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Se<PERSON>ni(3)", "validationMessages": "", "viewID": "ListaSezioni", "visible": true, "titleFormat": "", "name": "ListaSezioni", "appliesTo": "UG-Ins-PU-Data-Sezione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "True-False", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Se<PERSON>ni(3).VediEModifica", "labelFormat": "Standard", "disabled": false, "testID": "202304171734180604289", "value": "false", "maxLength": 0, "expectedLength": "", "fieldID": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "ShowPriceGaranzieNonSelezionato", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Se<PERSON>ni(3).pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202302061752470181864", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "CardSezione", "deselectedText": "OPPORTUNITÀ PER TE!", "selected": "false"}}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore", "iconSource": "standardIcon", "iconStandard": "pxIcon"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Standard", "formatType": "text", "showReadOnlyValidation": "false", "iconSource": "standardIcon", "iconStandard": "pxIcon"}], "actionSets": [], "type": "pxIcon"}, "label": "Image", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Se<PERSON>ni(3).pyTemplateGeneric", "labelFormat": "Standard", "disabled": false, "testID": "202302011610530026778", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"size": "M", "resource": "sezioneAssistenzaVeicolo", "webResponsiveSize": "40L 48L 40C", "type": "icon"}, "showLabel": false}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "Descrizione", "control": {"format": "TEXT APP BDB16 WEB BDB20 BDB20 BDB16C", "testID": "202301181205410449672"}, "value": "ASSISTENZA VEICOLO", "testID": "202301181205410449672"}}, {"paragraph": {"visible": true, "appliesTo": "UG-Ins-PU-Data-Sezione", "paragraphID": "TestoSezione", "readOnly": true, "value": "<p data-pega-style=\"TEXT APP BDL16C WEB BDL18 BDL18 BDL18\">Per essere coperto durante i tuoi viaggi.</p>", "testID": "202302061214250995217"}}], "groupFormat": "Col TC 8 4 4 8", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Se<PERSON>ni(3).pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202212131640340513751", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "Separator", "style": "CardSezione", "paddingDesktop": "0 16 0 24", "paddingTablet": "0 16 0 24", "paddingApp": "0", "paddingMobile": "0 8 0 24"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"VediEModifica": "true", "GestioneProcesso.StepSuccessivo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paddingApp": "0 20 0 0"}}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Cancel button", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "runDataTransform"}, {"action": "runDataTransform"}, {"action": "finishAssignment"}], "events": [{"event": "click"}]}], "type": "px<PERSON><PERSON><PERSON>", "label": "Aggiungi protezione"}, "label": "<PERSON><PERSON>", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Se<PERSON>ni(3).pyTemplateButton", "labelFormat": "Standard", "disabled": false, "testID": "202303201206030373403", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"VediEModifica": "true", "GestioneProcesso.StepSuccessivo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paddingApp": "0 20 0 0"}, "showLabel": false}}], "groupFormat": "Row CC RC RC CC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 24 D 24 T 24 M 24", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "COL TL 24 0 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}]}, {"groups": [{"view": {"reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Se<PERSON>ni(4)", "validationMessages": "", "viewID": "ListaSezioni", "visible": true, "titleFormat": "", "name": "ListaSezioni", "appliesTo": "UG-Ins-PU-Data-Sezione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "True-False", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Se<PERSON>ni(4).VediEModifica", "labelFormat": "Standard", "disabled": false, "testID": "202304171734180604289", "value": "false", "maxLength": 0, "expectedLength": "", "fieldID": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "ShowPriceGaranzieNonSelezionato", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Sezioni(4).pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202302061752470181864", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "CardSezione", "deselectedText": "OPPORTUNITÀ PER TE!", "selected": "false"}}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore", "iconSource": "standardIcon", "iconStandard": "pxIcon"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Standard", "formatType": "text", "showReadOnlyValidation": "false", "iconSource": "standardIcon", "iconStandard": "pxIcon"}], "actionSets": [], "type": "pxIcon"}, "label": "Image", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Se<PERSON>ni(4).pyTemplateGeneric", "labelFormat": "Standard", "disabled": false, "testID": "202302011610530026778", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"size": "M", "resource": "sezioneTutelaLegaleVeicolo", "webResponsiveSize": "40L 48L 40C", "type": "icon"}, "showLabel": false}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "Descrizione", "control": {"format": "TEXT APP BDB16 WEB BDB20 BDB20 BDB16C", "testID": "202301181205410449672"}, "value": "TUTELA LEGALE VEICOLO", "testID": "202301181205410449672"}}, {"paragraph": {"visible": true, "appliesTo": "UG-Ins-PU-Data-Sezione", "paragraphID": "TestoSezione", "readOnly": true, "value": "<p data-pega-style=\"TEXT APP BDL16C WEB BDL18 BDL18 BDL18\">Per avere supporto legale in caso di sinistro o altri eventi.</p>", "testID": "202302061214250995217"}}], "groupFormat": "Col TC 8 4 4 8", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Sezioni(4).pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202212131640340513751", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "Separator", "style": "CardSezione", "paddingDesktop": "0 16 0 24", "paddingTablet": "0 16 0 24", "paddingApp": "0", "paddingMobile": "0 8 0 24"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"VediEModifica": "true", "GestioneProcesso.StepSuccessivo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paddingApp": "0 20 0 0"}}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Cancel button", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "runDataTransform"}, {"action": "runDataTransform"}, {"action": "finishAssignment"}], "events": [{"event": "click"}]}], "type": "px<PERSON><PERSON><PERSON>", "label": "Aggiungi protezione"}, "label": "<PERSON><PERSON>", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Se<PERSON>ni(4).pyTemplateButton", "labelFormat": "Standard", "disabled": false, "testID": "202303201206030373403", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"VediEModifica": "true", "GestioneProcesso.StepSuccessivo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paddingApp": "0 20 0 0"}, "showLabel": false}}], "groupFormat": "Row CC RC RC CC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 24 D 24 T 24 M 24", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "COL TL 24 0 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}]}, {"groups": [{"view": {"reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Se<PERSON>ni(5)", "validationMessages": "", "viewID": "ListaSezioni", "visible": true, "titleFormat": "", "name": "ListaSezioni", "appliesTo": "UG-Ins-PU-Data-Sezione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "True-False", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).<PERSON><PERSON><PERSON>(5).VediEModifica", "labelFormat": "Standard", "disabled": false, "testID": "202304171734180604289", "value": "false", "maxLength": 0, "expectedLength": "", "fieldID": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "ShowPriceGaranzieNonSelezionato", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Se<PERSON>ni(5).pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202302061752470181864", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "CardSezione", "deselectedText": "OPPORTUNITÀ PER TE!", "selected": "false"}}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore", "iconSource": "standardIcon", "iconStandard": "pxIcon"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Standard", "formatType": "text", "showReadOnlyValidation": "false", "iconSource": "standardIcon", "iconStandard": "pxIcon"}], "actionSets": [], "type": "pxIcon"}, "label": "Image", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Se<PERSON>ni(5).pyTemplateGeneric", "labelFormat": "Standard", "disabled": false, "testID": "202302011610530026778", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"size": "M", "resource": "sezioneProtezioneVeicolo", "webResponsiveSize": "40L 48L 40C", "type": "icon"}, "showLabel": false}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "Descrizione", "control": {"format": "TEXT APP BDB16 WEB BDB20 BDB20 BDB16C", "testID": "202301181205410449672"}, "value": "PROTEGGI IL TUO VEICOLO", "testID": "202301181205410449672"}}, {"paragraph": {"visible": true, "appliesTo": "UG-Ins-PU-Data-Sezione", "paragraphID": "TestoSezione", "readOnly": true, "value": "<p data-pega-style=\"TEXT APP BDL16C WEB BDL18 BDL18 BDL18\">Aggiungi protezione riguardo incendio, furto, eventi naturali, eventi socio-politici, cristalli e Kasko.</p>", "testID": "202302061214250995217"}}], "groupFormat": "Col TC 8 4 4 8", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Se<PERSON>ni(5).pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202212131640340513751", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "Separator", "style": "CardSezione", "paddingDesktop": "0 16 0 24", "paddingTablet": "0 16 0 24", "paddingApp": "0", "paddingMobile": "0 8 0 24"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"VediEModifica": "true", "GestioneProcesso.StepSuccessivo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paddingApp": "0 20 0 0"}}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Cancel button", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "runDataTransform"}, {"action": "runDataTransform"}, {"action": "finishAssignment"}], "events": [{"event": "click"}]}], "type": "px<PERSON><PERSON><PERSON>", "label": "Aggiungi protezione"}, "label": "<PERSON><PERSON>", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito<PERSON><PERSON><PERSON><PERSON>(2).Se<PERSON>ni(5).pyTemplateButton", "labelFormat": "Standard", "disabled": false, "testID": "202303201206030373403", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"VediEModifica": "true", "GestioneProcesso.StepSuccessivo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paddingApp": "0 20 0 0"}, "showLabel": false}}], "groupFormat": "Row CC RC RC CC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 24 D 24 T 24 M 24", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "COL TL 24 0 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}]}], "repeatLayoutFormat": "Col TL 24 48 20 20", "repeatContainerFormat": "NOHEADER", "testID": "202301181135490926883"}}], "title": ""}}]}], "repeatLayoutFormat": "<PERSON><PERSON><PERSON>", "repeatContainerFormat": "NOHEADER", "testID": "202301181135590923386"}}], "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "LightBlueBorderContainer", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore", "iconSource": "image", "iconImage": "cmicons/pypackage.svg"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Standard", "formatType": "text", "showReadOnlyValidation": "false", "iconSource": "image", "iconImage": "cmicons/pypackage.svg"}], "actionSets": [], "type": "pxIcon"}, "label": "Image", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateGeneric", "labelFormat": "Standard", "disabled": false, "testID": "202301021813070606142", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"size": "S", "resource": "present", "type": "icon", "paddingApp": "0 0 10 0"}, "showLabel": false}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TitoloConvenzioni STYLE TEXT APP BDB16 WEB BDB16 BDB16 BDB16", "testID": "202301021813070607571"}, "value": "SCONTI E CONVENZIONI", "testID": "202301021813070607571"}}], "groupFormat": "Row LC 8 8", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "DescrizioneConvenzioni STYLE TEXT APP BDL13 WEB BDL16 BDL16 BDL16", "testID": "202302011550590093659"}, "value": "Aggiungi una convenzione per avere ancora più van<PERSON>gi.", "testID": "202302011550590093659"}}], "groupFormat": "Col TL 16 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Accordi<PERSON><PERSON><PERSON><PERSON>", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"view": {"reference": "Ambito", "validationMessages": "", "viewID": "ScontisticaBene", "visible": true, "titleFormat": "", "name": "ScontisticaBene", "appliesTo": "UG-Ins-PU-Data-Ambito", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groupFormat": "Dynamic", "layoutFormat": "REPEATINGLAYOUT", "title": "", "reference": "Ambito.ListaSconti", "sourceType": "Property", "fieldListID": ".<PERSON>a<PERSON><PERSON><PERSON>", "referenceType": "List", "newRow": {"groups": []}, "rows": [{"groups": [{"view": {"reference": "Ambito.ListaSconti(1)", "validationMessages": "", "viewID": "ScontisticaBene", "visible": true, "titleFormat": "", "name": "ScontisticaBene", "appliesTo": "UG-Ins-PU-Data-Sconto", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "DiscountButton", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "runDataTransform"}, {"action": "localAction", "actionProcess": {"localAction": "PopupScontisticaBene", "localActionFormat": "Standard", "customTemplate": "pzModalTemplate", "target": "modalDialog"}}], "events": [{"event": "click"}]}], "type": "px<PERSON><PERSON><PERSON>", "label": "Sconto box"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito.ListaSconti(1).pyTemplateButton", "labelFormat": "TEXT APP BDB14 WEB BDB14U BDB14U BDB14U", "disabled": false, "testID": "202305181454310291903", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"GestioneProcesso.IndiceScontoSelezionato": "1"}, "showLabel": true}}], "groupFormat": "Col TL 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}]}, {"groups": [{"view": {"reference": "Ambito.ListaSconti(2)", "validationMessages": "", "viewID": "ScontisticaBene", "visible": true, "titleFormat": "", "name": "ScontisticaBene", "appliesTo": "UG-Ins-PU-Data-Sconto", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "DiscountButton", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "runDataTransform"}, {"action": "localAction", "actionProcess": {"localAction": "PopupScontisticaBene", "localActionFormat": "Standard", "customTemplate": "pzModalTemplate", "target": "modalDialog"}}], "events": [{"event": "click"}]}], "type": "px<PERSON><PERSON><PERSON>", "label": "Sconto campagna"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito.ListaSconti(2).pyTemplateButton", "labelFormat": "TEXT APP BDB14 WEB BDB14U BDB14U BDB14U", "disabled": false, "testID": "202305181454310291903", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"GestioneProcesso.IndiceScontoSelezionato": "2"}, "showLabel": true}}], "groupFormat": "Col TL 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}]}, {"groups": [{"view": {"reference": "Ambito.ListaSconti(3)", "validationMessages": "", "viewID": "ScontisticaBene", "visible": true, "titleFormat": "", "name": "ScontisticaBene", "appliesTo": "UG-Ins-PU-Data-Sconto", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "DiscountButton", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "runDataTransform"}, {"action": "localAction", "actionProcess": {"localAction": "PopupScontisticaBene", "localActionFormat": "Standard", "customTemplate": "pzModalTemplate", "target": "modalDialog"}}], "events": [{"event": "click"}]}], "type": "px<PERSON><PERSON><PERSON>", "label": "Sconto quantità"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito.ListaSconti(3).pyTemplateButton", "labelFormat": "TEXT APP BDB14 WEB BDB14U BDB14U BDB14U", "disabled": false, "testID": "202305181454310291903", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"GestioneProcesso.IndiceScontoSelezionato": "3"}, "showLabel": true}}], "groupFormat": "Col TL 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}]}, {"groups": [{"view": {"reference": "Ambito.ListaSconti(4)", "validationMessages": "", "viewID": "ScontisticaBene", "visible": true, "titleFormat": "", "name": "ScontisticaBene", "appliesTo": "UG-Ins-PU-Data-Sconto", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "DiscountButton", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "runDataTransform"}, {"action": "localAction", "actionProcess": {"localAction": "PopupScontisticaBene", "localActionFormat": "Standard", "customTemplate": "pzModalTemplate", "target": "modalDialog"}}], "events": [{"event": "click"}]}], "type": "px<PERSON><PERSON><PERSON>", "label": "Sconto welcome bonus"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito.ListaSconti(4).pyTemplateButton", "labelFormat": "TEXT APP BDB14 WEB BDB14U BDB14U BDB14U", "disabled": false, "testID": "202305181454310291903", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"GestioneProcesso.IndiceScontoSelezionato": "4"}, "showLabel": true}}], "groupFormat": "Col TL 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}]}], "repeatLayoutFormat": "Responsive 2col PU", "repeatContainerFormat": "NOHEADER", "testID": "202305181454430744196"}}], "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP BDL13 WEB BDL13C BDL13C BDL13C", "testID": "202302171527230409215"}, "value": "Vuoi usufruire di una convenzione?", "testID": "202302171527230409215"}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "SimpleTPD", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "runDataTransform"}, {"action": "finishAssignment"}], "events": [{"event": "click"}]}], "type": "px<PERSON><PERSON><PERSON>", "label": "Aggiungi convenzione"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateButton", "labelFormat": "Standard", "disabled": false, "testID": "202301191032100192863", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"analytics_action_name": "aggiungi convenzione", "analytics_track_enabled": "true", "analytics_action_detail": "convenzioni", "shouldCenterButton": "true", "GestioneProcesso.AggiungiConvenzione": "true", "analytics_action_effect": "aggiungi convenzione"}, "showLabel": true}}], "groupFormat": "ROW CC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TC 16 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 31 8 D 0 8 T 0 8 M 31 8", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Card WH 8 8", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "Card WH 8 8", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TC 16 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 16 20 D 0 16 T 0 16 M 0 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Accordion<PERSON>pened", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Accordion 8", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TC 24 0 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 0 D 0 T 24 0 M 16 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "SimpleTPD", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [], "type": "px<PERSON><PERSON><PERSON>", "label": "Leggi il set informativo"}, "label": "<PERSON><PERSON>", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateButton", "labelFormat": "Standard", "disabled": false, "testID": "202302011620480851332", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"shouldCenterButton": "true", "link": "https://www.unipolsai.it", "linkType": "Pdf", "buttonIcons": "chevron_right"}, "showLabel": false}}], "groupFormat": "ROW CC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "SimpleTPD", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "localAction", "actionProcess": {"localAction": "PopupRip<PERSON><PERSON>", "localActionFormat": "Standard", "customTemplate": "pzModalTemplate", "target": "modalDialog"}}], "events": [{"event": "click"}]}], "type": "px<PERSON><PERSON><PERSON>", "label": "Ripristina offerta iniziale"}, "label": "<PERSON><PERSON>", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateButton", "labelFormat": "Standard", "disabled": false, "testID": "202302021149430578754", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"analytics_action_name": "Ripristina offerta iniziale", "analytics_track_enabled": "true", "analytics_action_detail": "Ripristina offerta iniziale", "shouldCenterButton": "true", "buttonIcons": "chevron_right", "analytics_action_effect": "Ripristina offerta iniziale"}, "showLabel": false}}], "groupFormat": "ROW CC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TC 8 48 20 20", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 0 D 0 36 0 56 T 0 36 0 56 M 0 36 0 56", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "CardBgWhite", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "DefaultBgGrey", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"view": {"reference": "", "validationMessages": "", "viewID": "FondoPagina", "visible": true, "titleFormat": "", "name": "FondoPagina", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202301021843320704959", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "StickyFooterPU"}}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP WHM16 WEB BDL13 BDL13 BDL13", "testID": "202301021843320704118"}, "value": "TOTALE", "testID": "202301021843320704118"}}, {"view": {"reference": "", "validationMessages": "", "viewID": "MostraPremioFondoPagina", "visible": true, "titleFormat": "", "name": "MostraPremioFondoPagina", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": true, "control": {"modes": [{"textAlign": "Right", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "specifySize": "auto", "formatType": "number", "maxChars": ""}, {"textAlign": "Left", "tooltip": "", "modeType": "readOnly", "formatType": "number", "obfuscated": false, "showReadOnlyValidation": "false", "symbolValue": "€"}], "actionSets": [], "type": "pxC<PERSON>rency"}, "label": "", "type": "Decimal", "required": false, "validateAs": "", "reference": "Ambito.RataPremioPosizioneScontato", "labelFormat": "TEXT APP WHB24 WEB BDB22 BDB22 BDB22", "disabled": false, "testID": "202301031143470444207", "value": "601.69", "maxLength": 0, "expectedLength": "", "fieldID": "RataPremioPosizioneScontato"}}, {"caption": {"columnImportance": "", "visible": false, "captionFor": "", "control": {"format": "TEXT APP WHL14 WEB BDL13 BDL13 BDL13", "testID": "202308031654060711200"}, "value": "al mese", "testID": "202308031654060711200"}}], "groupFormat": "Mimic a sentence", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}, {"caption": {"columnImportance": "", "visible": false, "captionFor": "", "control": {"format": "TEXT APP WHB24 WEB BDB22 BDB22 BDB22", "testID": "202304191117590747547"}, "value": "+", "testID": "202304191117590747547"}}, {"view": {"viewID": "PremioFondoPaginaAmbitiNonFraz", "visible": false, "name": "PremioFondoPaginaAmbitiNonFraz"}}], "groupFormat": "PriceContainer", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"componentID": "footerPrimaryCTA"}}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"componentID": "footerPrimaryCTA"}}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"analytics_action_name": "aggiungi al carrello", "analytics_track_enabled": "true", "analytics_action_detail": "aggiungi al carrello", "componentID": "footerPrimaryCTA", "analytics_virtual": "action", "analytics_action_effect": "aggiungi al carrello"}}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "PositiveTPD Button", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "finishAssignment"}], "events": [{"event": "click"}]}], "type": "px<PERSON><PERSON><PERSON>", "label": "Vai a riepilogo"}, "label": "<PERSON><PERSON>", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateButton", "labelFormat": "Standard", "disabled": false, "testID": "202301021843320707291", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"submitButton": "true", "componentID": "footerPrimaryCTA"}, "showLabel": false}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"analytics_action_name": "acquista", "analytics_track_enabled": "true", "submitButton": "true", "analytics_action_detail": "acquista", "componentID": "footerPrimaryCTA", "analytics_virtual": "action", "analytics_action_effect": "acquista ambito"}}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"analytics_action_name": "acquista", "analytics_track_enabled": "true", "submitButton": "true", "analytics_action_detail": "acquista", "componentID": "footerPrimaryCTA", "analytics_virtual": "action", "analytics_action_effect": "acquista ambito"}}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"submitButton": "true", "componentID": "footerPrimaryCTA"}}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"componentID": "footerPrimaryCTA"}}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"componentID": "footerPrimaryCTA"}}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"componentID": "footerPrimaryCTA"}}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"componentID": "footerPrimaryCTA"}}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"componentID": "footerPrimaryCTA"}}}, {"field": {"visible": false, "validateAs": "", "disabled": false, "fieldID": "pyTemplateButton", "customAttributes": {"analytics_action_name": "salva per dopo", "analytics_track_enabled": "true", "analytics_action_detail": "salva per dopo", "componentID": "footerSecondaryCTA", "GestioneProcesso.FlagSalvaPerDopo": "true", "analytics_virtual": "action", "analytics_action_effect": "salva per dopo"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}], "title": ""}, "actionID": "ConfigurazioneOfferta", "caseID": "UG-INS-PU-WORK-QUOTAZIONE Q-73134", "name": "Configurazione Offerta"}, "metaBodyResponse": {"assignmentId": "ASSIGN-WORKLIST UG-INS-PU-WORK-QUOTAZIONE Q-73134!CONFIGURAZIONEOFFERTA", "actionId": "ConfigurazioneOfferta", "caseId": "UG-INS-PU-WORK-QUOTAZIONE Q-73134"}, "analyticsBodyResponse": {}}