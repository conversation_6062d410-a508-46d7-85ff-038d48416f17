{"pegaBodyResponse": {"view": {"reference": "", "validationMessages": "", "viewID": "VediDati", "visible": true, "titleFormat": "", "name": "VediDati", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"view": {"reference": "", "validationMessages": "", "viewID": "AnalyticsHidden", "visible": true, "titleFormat": "", "name": "AnalyticsHidden", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202212131640340513751", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "analyticsPageFields", "funnel_name": "", "page_name": "unico:vedi dati"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "CustomPosition", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore", "iconSource": "standardIcon", "iconStandard": "pxIcon"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Standard", "formatType": "text", "showReadOnlyValidation": "false", "iconSource": "standardIcon", "iconStandard": "pxIcon"}], "actionSets": [], "type": "pxIcon"}, "label": "Image", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateGeneric", "labelFormat": "Standard", "disabled": false, "testID": "202212221832260358946", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"size": "M", "resource": "Close", "modalCloseIcon": "true", "closeModal": "true", "position": "bottom", "type": "icon"}, "showLabel": false}}], "groupFormat": "ROW RT 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 0 D 32 32 32 8 T 32 32 32 8 M 20 20 20 8", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "P 0 0 0 20 D 0 T 0 M 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore", "iconSource": "standardIcon", "iconStandard": "pxIcon"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Standard", "formatType": "text", "showReadOnlyValidation": "false", "iconSource": "standardIcon", "iconStandard": "pxIcon"}], "actionSets": [], "type": "pxIcon"}, "label": "Image", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateGeneric", "labelFormat": "Standard", "disabled": false, "testID": "202212221557150708789", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"size": "M", "resource": "carPositive", "type": "icon"}, "showLabel": false}}], "groupFormat": "ROW CC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "NomeMarca", "control": {"format": "TEXT APP BDM16 WEB BDM20C BDM20 C BDM16C", "testID": "202212211633400929824"}, "value": "MERCEDES", "testID": "202212211633400929824"}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "<PERSON>me<PERSON><PERSON><PERSON>", "control": {"format": "TEXT APP BDM16 WEB BDM20C BDM20 C BDM16C", "testID": "202212211633400932846"}, "value": "Classe B     &#40;W247&#41;", "testID": "202212211633400932846"}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP BDM16 WEB BDM20C BDM20 C BDM16C", "testID": "202212211634510261931"}, "value": "-", "testID": "202212211634510261931"}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "Targa", "control": {"format": "TEXT APP BDM16 WEB BDM20C BDM20 C BDM16C", "testID": "202212211633400934721"}, "value": "GG810CV", "testID": "202212211633400934721"}}], "groupFormat": "Mimic a sentence center", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TC 16 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP GDB16 WEB BDM18 BDM18 BDM13", "testID": "202212201634220543254"}, "value": "Allestimento", "testID": "202212201634220543254"}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "Allestimento", "control": {"format": "TEXT APP BDB16R WEB BDB20R BDB20R BDB16R", "testID": "202212201634220544417"}, "value": "B 250 e Plug-in hybrid Automatica Business Extra", "testID": "202212201634220544417"}}], "groupFormat": "Row RC RC 4 4", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP GDB16 WEB BDM18 BDM18 BDM13", "testID": "202212201634220538509"}, "value": "Alimentazione", "testID": "202212201634220538509"}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP BDB16R WEB BDB20R BDB20R BDB16R", "testID": "202212231742520440287"}, "value": "", "testID": "202212231742520440287"}}], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP GDB16 WEB BDM18 BDM18 BDM13", "testID": "202212201634220540832"}, "value": "Classe", "testID": "202212201634220540832"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": true, "control": {"modes": [{"dataPagePrompt": "label", "groupOrder": "asc", "listSource": "datapage", "textAlign": "Left", "tooltip": "", "enableGrouping": false, "groupBy": "", "minChars": "", "dataPageParams": [{"valueReference": {"reference": ".GestioneProcesso.IdTemplate", "lastSavedValue": "181cbdfd-e9e6-4def-b33e-3eba5afa5dee"}, "name": "id"}, {"name": "detailLevel"}, {"valueReference": {"reference": ".Ambito.CodiceProdottoWPT", "lastSavedValue": "PUAUTO"}, "name": "<PERSON><PERSON><PERSON>"}, {"valueReference": {"reference": ".Ambito.Bene.DescrizioneBeneWPT", "lastSavedValue": "1"}, "name": "<PERSON><PERSON>"}], "modeType": "editable", "dataPageValue": "Valore", "controlFormat": "Standard", "dataPageTooltip": "", "loadMode": "auto", "options": [{"value": "1 - Autovettura", "key": "1"}, {"value": "8 - Autoveicolo per trasporto promiscuo", "key": "8"}, {"value": "44 - Rimorchio autovettura", "key": "44"}], "dataPageID": "D_DominioClasseVeicolo", "specifySize": "auto", "formatType": "none", "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "formatType": "none", "showReadOnlyValidation": "false"}], "actionSets": [], "type": "pxDropdown"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito.Bene.Auto.Classe", "labelFormat": "TEXT APP BDB16R WEB BDB20R BDB20R BDB16R", "disabled": false, "testID": "202212201634220540912", "value": "1", "maxLength": 0, "expectedLength": "", "fieldID": "Classe", "customAttributes": {"showLikeLabel": "true"}}}], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP GDB16 WEB BDM18 BDM18 BDM13", "testID": "202212201634220540832"}, "value": "<PERSON><PERSON>", "testID": "202212201634220540832"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": true, "control": {"modes": [{"dataPagePrompt": "label", "groupOrder": "asc", "listSource": "datapage", "textAlign": "Left", "tooltip": "", "enableGrouping": false, "groupBy": "", "minChars": "", "dataPageParams": [{"valueReference": {"reference": ".GestioneProcesso.IdTemplate", "lastSavedValue": "181cbdfd-e9e6-4def-b33e-3eba5afa5dee"}, "name": "id"}, {"name": "detailLevel"}, {"valueReference": {"reference": ".Ambito.CodiceProdottoWPT", "lastSavedValue": "PUAUTO"}, "name": "<PERSON><PERSON><PERSON>"}, {"valueReference": {"reference": ".Ambito.Bene.DescrizioneBeneWPT", "lastSavedValue": "1"}, "name": "<PERSON><PERSON>"}], "modeType": "editable", "dataPageValue": "Valore", "controlFormat": "Standard", "dataPageTooltip": "", "loadMode": "auto", "options": [{"value": "1 - <PERSON><PERSON>vat<PERSON>", "key": "1"}, {"value": "4 - Locazione senza conducente", "key": "4"}, {"value": "5 - Noleggio con conducente", "key": "5"}, {"value": "13 - Scuolabus/Alberghi/Istituti/Enti/Aziende", "key": "13"}, {"value": "26 - <PERSON><PERSON><PERSON> guida", "key": "26"}], "dataPageID": "D_DominioUsoVeicolo", "specifySize": "auto", "formatType": "none", "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "formatType": "none", "showReadOnlyValidation": "false"}], "actionSets": [], "type": "pxDropdown"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito.Bene.Auto.Uso", "labelFormat": "TEXT APP BDB16R WEB BDB20R BDB20R BDB16R", "disabled": false, "testID": "202212201634220540912", "value": "1", "maxLength": 0, "expectedLength": "", "fieldID": "<PERSON><PERSON>", "customAttributes": {"showLikeLabel": "true"}}}], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP GDB16 WEB BDM18 BDM18 BDM13", "testID": "202212201634220547731"}, "value": "<PERSON><PERSON> ve<PERSON>lo", "testID": "202212201634220547731"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": true, "control": {"modes": [{"textAlign": "Right", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "specifySize": "auto", "formatType": "number", "maxChars": ""}, {"textAlign": "Left", "tooltip": "", "modeType": "readOnly", "formatType": "number", "obfuscated": false, "showReadOnlyValidation": "false", "symbolValue": "€"}], "actionSets": [], "type": "pxC<PERSON>rency"}, "label": "", "type": "Decimal", "required": false, "validateAs": "", "reference": "Ambito.Bene.Auto.ValoreVeicolo", "labelFormat": "TEXT APP BDB16R WEB BDB20R BDB20R BDB16R", "disabled": false, "testID": "202212201634220548755", "value": "29800", "maxLength": 0, "expectedLength": "", "fieldID": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP GDB16 WEB BDM18 BDM18 BDM13", "testID": "202212201634220549970"}, "value": "Valore accessori", "testID": "202212201634220549970"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": true, "control": {"modes": [{"textAlign": "Right", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "specifySize": "auto", "formatType": "number", "maxChars": ""}, {"textAlign": "Left", "tooltip": "", "modeType": "readOnly", "formatType": "number", "obfuscated": false, "showReadOnlyValidation": "false", "symbolValue": "€"}], "actionSets": [], "type": "pxC<PERSON>rency"}, "label": "", "type": "Decimal", "required": false, "validateAs": "", "reference": "Ambito.Bene.Auto.ValoreAccessori", "labelFormat": "TEXT APP BDB16R WEB BDB20R BDB20R BDB16R", "disabled": false, "testID": "202212201634220548755", "value": "0", "maxLength": 0, "expectedLength": "", "fieldID": "ValoreAccessori"}}], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP GDB16 WEB BDM18 BDM18 BDM13", "testID": "202212201634220550676"}, "value": "Prima immatricolazione", "testID": "202212201634220550676"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": true, "control": {"modes": [{"textAlign": "Left", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "specifySize": "auto", "formatType": "text", "obfuscated": false, "dateTime": "auto", "displayMode": "calendar", "displayLongFormat": false, "ignoreLocaleSettings": false, "showReadOnlyFormatting": false, "calendarNavigation": "true", "allowTextEntry": true, "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "formatType": "date", "obfuscated": false, "showReadOnlyValidation": "false", "dateFormat": "DateTime-Custom", "customDateFormat": "yyyy"}], "actionSets": [], "type": "pxDateTime"}, "label": "", "type": "Date", "required": false, "validateAs": "", "reference": "Ambito.Bene.Auto.PrimaImmatricolazione", "labelFormat": "TEXT APP BDB16R WEB BDB20R BDB20R BDB16R", "disabled": false, "testID": "202301051716450368357", "value": "20210630", "maxLength": 0, "expectedLength": "", "fieldID": "PrimaImmatricolazione", "customAttributes": {"dateFormat": "yyyy"}}}], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP GDB16 WEB BDM18 BDM18 BDM13", "testID": "20221220163422055247"}, "value": "Data di voltura - Facoltativo", "testID": "20221220163422055247"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": true, "control": {"modes": [{"textAlign": "Left", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "specifySize": "auto", "formatType": "text", "obfuscated": false, "dateTime": "auto", "displayMode": "calendar", "displayLongFormat": false, "ignoreLocaleSettings": false, "showReadOnlyFormatting": false, "calendarNavigation": "true", "allowTextEntry": true, "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "formatType": "date", "obfuscated": false, "showReadOnlyValidation": "false", "dateFormat": "Date-Short-Custom-YYYY"}], "actionSets": [], "type": "pxDateTime"}, "label": "", "type": "Date", "required": false, "validateAs": "", "reference": "Ambito.Bene.Auto.DataVoltura", "labelFormat": "TEXT APP BDB16R WEB BDB20R BDB20R BDB16R", "disabled": false, "testID": "202301051716450370754", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "DataVoltura", "customAttributes": {"dateFormat": "dd/MM/yyyy"}}}], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TL 24 20 20 30", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TL 24 22 22 29", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 20 24 D 120 0 120 20 T 56 0 56 20 M 20 0 20 20", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202212221633290890448", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "Separator", "paddingDesktop": "120 0", "paddingTablet": "56 0", "paddingApp": "0", "paddingMobile": "20 0"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP GDB16 WEB BDM18 BDM18 BDM13", "testID": "202212201634220538509"}, "value": "Data di nascita", "testID": "202212201634220538509"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": true, "control": {"modes": [{"textAlign": "Left", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "specifySize": "auto", "formatType": "text", "obfuscated": false, "dateTime": "auto", "displayMode": "calendar", "displayLongFormat": false, "ignoreLocaleSettings": false, "showReadOnlyFormatting": false, "calendarNavigation": "true", "allowTextEntry": true, "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "formatType": "date", "obfuscated": false, "showReadOnlyValidation": "false", "dateFormat": "Date-Short-Custom-YYYY"}], "actionSets": [], "type": "pxDateTime"}, "label": "", "type": "Date", "required": false, "validateAs": "", "reference": "Ambito.Proprietario.DataDiNascita", "labelFormat": "TEXT APP BDB16R WEB BDB20R BDB20R BDB16R", "disabled": false, "testID": "202301051716450355626", "value": "19560618", "maxLength": 0, "expectedLength": "", "fieldID": "DataDiNascita", "customAttributes": {"dateFormat": "dd/MM/yyyy"}}}], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP GDB16 WEB BDM18 BDM18 BDM13", "testID": "202212201634220554545"}, "value": "Indirizzo di residenza", "testID": "202212201634220554545"}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP BDB16R WEB BDB20R BDB20R BDB16R", "testID": "202212201634220556838"}, "value": "<PERSON>, 4 Mogliano Veneto &#40;TV&#41; 31021", "testID": "202212201634220556838"}}], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP GDB16 WEB BDM18 BDM18 BDM13", "testID": "202212201634220538509"}, "value": "Classe di merito", "testID": "202212201634220538509"}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP BDB16R WEB BDB20R BDB20R BDB16R", "testID": "202212231742520440287"}, "value": "14", "testID": "202212231742520440287"}}], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "TEXT APP GDB16 WEB BDM18 BDM18 BDM13", "testID": "202212201634220540832"}, "value": "Trasferimento classe di merito da altro veicolo", "testID": "202212201634220540832"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": true, "control": {"modes": [{"textAlign": "Left", "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "specifySize": "auto", "formatType": "text", "obfuscated": false, "captionPosition": "right", "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "formatType": "<PERSON><PERSON><PERSON>e", "obfuscated": false, "showReadOnlyValidation": "false", "trueLabel": "Sì", "falseLabel": "No", "trueImage": "", "falseImage": "", "showValueAs": "text"}], "actionSets": [], "type": "pxCheckbox", "label": ""}, "label": "", "type": "True-False", "required": false, "validateAs": "", "reference": "Ambito.Bene.Auto.RecuperoClasseAltroVeicolo", "labelFormat": "TEXT APP BDB16R WEB BDB20R BDB20R BDB16R", "disabled": false, "testID": "202212201634220540912", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "RecuperoClasseAltroVeicolo", "showLabel": false}}], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"layout": {"visible": false, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TL 24 20 20 30", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 20 24 20 32 D 120 20 120 24 T 56 20 56 24 M 20 20 20 24", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"view": {"reference": "", "validationMessages": "", "viewID": "VediDatiModificaDatiButton", "visible": true, "titleFormat": "", "name": "VediDatiModificaDatiButton", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": true, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "SimpleTPD", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "runDataTransform"}, {"action": "finishAssignment"}], "events": [{"event": "click"}]}], "type": "px<PERSON><PERSON><PERSON>", "label": "Modifica Dati"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateButton", "labelFormat": "TEXT APP BDB16 WEB BDBN18 BDBN18 BDBN16", "disabled": false, "testID": "202212161154560042378", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"GestioneProcesso.VaiAModificaDati": "true", "analytics_track_enabled": "true", "componentID": "closeModal", "analytics_virtual": "action", "shouldCenterButton": "true", "buttonIcons": "chevron_right", "analytics_action_effect": "visualizzazione pagina modifica dati", "paddingTablet": "0 0 0 32", "paddingApp": "0 0 0 24", "analytics_action_name": "modifica dati", "analytics_action_detail": "modifica dati popup", "paddingDesktop": "0 0 0 32", "paddingMobile": "0 0 0 20"}, "showLabel": true}}], "groupFormat": "ROW CC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}], "groupFormat": "StackedLayout", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}, "actionID": "PopupVediDati", "caseID": "UG-INS-PU-WORK-QUOTAZIONE Q-74041", "name": "Pop up vedi dati"}, "metaBodyResponse": {"assignmentId": "ASSIGN-WORKLIST UG-INS-PU-WORK-QUOTAZIONE Q-74041!CONFIGURAZIONEOFFERTA", "actionId": "PopupVediDati"}, "analyticsBodyResponse": {}}